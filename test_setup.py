#!/usr/bin/env python3
"""
Test script to verify the YouTube Clip Generator setup.
Run this script to check if all dependencies and configurations are working correctly.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).resolve().parent / "src"))

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import yt_dlp
        print("✅ yt-dlp imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import yt-dlp: {e}")
        return False
    
    try:
        import ffmpeg
        print("✅ ffmpeg-python imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import ffmpeg-python: {e}")
        return False
    
    try:
        from moviepy.editor import VideoFileClip
        print("✅ moviepy imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import moviepy: {e}")
        return False
    
    try:
        import pysrt
        print("✅ pysrt imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import pysrt: {e}")
        return False
    
    return True

def test_environment():
    """Test environment variables"""
    print("\n🔍 Testing environment variables...")
    
    qwen_key = os.getenv('QWEN_API_KEY')
    if qwen_key:
        print("✅ QWEN_API_KEY is set")
        return True
    else:
        print("❌ QWEN_API_KEY is not set")
        print("   Please set your Qwen API key: export QWEN_API_KEY='your_key_here'")
        return False

def test_config():
    """Test configuration loading"""
    print("\n🔍 Testing configuration...")
    
    try:
        from src.utils.config_loader import ConfigLoader
        config_loader = ConfigLoader()
        config = config_loader.load_settings()
        print("✅ Configuration loaded successfully")
        
        # Check key sections
        required_sections = ['app', 'video', 'ai', 'llm', 'platforms', 'subtitle_styles']
        for section in required_sections:
            if section in config:
                print(f"✅ Configuration section '{section}' found")
            else:
                print(f"❌ Configuration section '{section}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False

def test_directories():
    """Test required directories"""
    print("\n🔍 Testing directories...")
    
    required_dirs = ['data/temp', 'data/exports', 'logs', 'assets/music', 'config']
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"✅ Directory '{dir_path}' exists")
        else:
            print(f"⚠️  Directory '{dir_path}' missing, creating...")
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ Directory '{dir_path}' created")
    
    return True

def main():
    """Run all tests"""
    print("🚀 YouTube Clip Generator Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_environment,
        test_config,
        test_directories
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nYou can now run the application:")
        print("  streamlit run streamlit_app.py")
        print("  or")
        print("  python src/main.py 'https://youtube.com/watch?v=VIDEO_ID'")
    else:
        print("❌ Some tests failed. Please fix the issues above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
