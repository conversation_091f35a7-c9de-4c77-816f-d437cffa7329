import subprocess
import time
import sys
import os

# Set working directories
backend_dir = os.path.join(os.getcwd(), "backend")
frontend_path = os.path.join(os.getcwd(), "frontend", "app.py")

# Start FastAPI (Uvicorn)
backend_proc = subprocess.Popen(
    ["uvicorn", "main:app", "--reload", "--port", "8888"],
    cwd=backend_dir
)
print("✅ FastAPI backend launched...")

# Give backend a moment to start
time.sleep(2)

# Start Streamlit
frontend_proc = subprocess.Popen(
    ["streamlit", "run", frontend_path],
    cwd=os.path.dirname(frontend_path)
)
print("✅ Streamlit frontend launched...")

# Keep alive and listen for exit
try:
    backend_proc.wait()
    frontend_proc.wait()
except KeyboardInterrupt:
    print("⛔ Shutting down...")
    backend_proc.terminate()
    frontend_proc.terminate()
    sys.exit(0)
