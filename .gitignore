# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a script in a subdirectory.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# PEP 582; Error Handlers potentially outside the root directory e.g. in Python3.7
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath files
.sage/

# Environments
.env
.venv
venv/
ENV/
env/
ENV.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Static analysis tool Nuitka
*.pyi  # Nuitka stubs

# VS Code
.vscode/

# Temporary files and directories
data/temp/
data/cache/

# Logs
logs/
*.log

# User-specific files (OS generated)
.DS_Store
Thumbs.db

# Exported clips (can be large)
data/exports/

# Models downloaded by whisper or transformers
# These can be large and are easily re-downloaded
*.pt
*.bin
*.pth
/data/models/  # If models are stored here by convention

# NLTK Data
# Often downloaded to a standard location, but if locally in project:
nltk_data/

# Streamlit specific
.streamlit/ 