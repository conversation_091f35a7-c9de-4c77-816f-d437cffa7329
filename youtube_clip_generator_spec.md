# YouTube Video Clip Generator - Complete Technical Specification

## 1. Project Overview

### 1.1 Purpose
Develop a fully local AI-powered system that automatically processes YouTube videos and creates optimized short-form clips (15-60 seconds) for TikTok, YouTube Shorts, and Instagram Reels.

### 1.2 Key Features
- 🎬 **Intelligent Clip Generation**: AI-powered highlight detection and extraction
- 📜 **Automatic Subtitle Burning**: SRT caption integration into final videos
- 🧠 **AI-Powered Titles**: Platform-optimized viral title generation
- 🎵 **Background Music Integration**: Built-in audio asset management
- 📱 **Multi-Platform Optimization**: Format-specific output for each platform
- 🔁 **Batch Processing**: Multiple clips per video processing
- 🧪 **Fully Local**: No external dependencies except LLM API

### 1.3 Technical Requirements
- **Platform**: Cross-platform (Windows, macOS, Linux)
- **Language**: Python 3.9+
- **Processing**: Local GPU acceleration when available
- **Storage**: Local file system with organized output structure
- **Interface**: CLI with optional web interface

## 2. System Architecture

### 2.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Input Layer   │───▶│ Processing Core │───▶│  Output Layer   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
│                      │                      │
├─ Video URLs          ├─ AI Analysis        ├─ Generated Clips
├─ Local Videos        ├─ Content Scoring    ├─ Burned Subtitles
├─ Subtitle Files      ├─ Clip Extraction    ├─ Platform Variants
└─ Config Settings     ├─ Audio Processing   └─ Metadata Files
                       └─ Title Generation
```

### 2.2 Core Components

#### 2.2.1 Video Processor
- **Responsibility**: Video downloading, format conversion, quality analysis
- **Technologies**: yt-dlp, FFmpeg, OpenCV
- **Output**: Standardized video files for processing

#### 2.2.2 AI Content Analyzer
- **Responsibility**: Speech recognition, sentiment analysis, highlight detection
- **Technologies**: Whisper, Transformers, Custom scoring algorithms
- **Output**: Timestamped content scores and highlight segments

#### 2.2.3 Clip Generator
- **Responsibility**: Segment extraction, subtitle burning, platform optimization
- **Technologies**: MoviePy, FFmpeg, Pillow
- **Output**: Platform-specific video clips

#### 2.2.4 Title Generator
- **Responsibility**: AI-powered title creation for viral content
- **Technologies**: Ollama, LangChain, Custom prompts
- **Output**: Platform-optimized titles and descriptions

#### 2.2.5 Audio Manager
- **Responsibility**: Background music integration, audio mixing
- **Technologies**: Pydub, Librosa
- **Output**: Audio-enhanced video clips

## 3. Technology Stack

### 3.1 Core Dependencies
```python
# requirements.txt
ffmpeg-python==0.2.0
moviepy==1.0.3
opencv-python==********
torch==2.1.0
transformers==4.35.0
pysrt==1.1.2
webvtt-py==0.4.6
Pillow==10.0.1
pydub==0.25.1
librosa==0.10.1
soundfile==0.12.1
ollama==0.1.7
langchain==0.0.330
tqdm==4.66.1
PyYAML==6.0.1
pandas==2.1.3
nltk==3.8.1
textblob==0.17.1
streamlit==1.28.1
yt-dlp==2023.11.16
requests==2.31.0
numpy==1.24.3
scipy==1.11.4
```

### 3.2 System Dependencies
- **FFmpeg**: Video/audio processing engine
- **Python 3.9+**: Core runtime
- **CUDA** (optional): GPU acceleration
- **Git**: Version control and model downloads

## 4. Project Structure

### 4.1 Directory Layout
```
youtube_clip_generator/
├── main.py                     # Application entry point
├── requirements.txt            # Python dependencies
├── config/
│   ├── settings.yaml          # Application configuration
│   ├── platforms.yaml         # Platform-specific settings
│   └── prompts.yaml           # AI prompt templates
├── src/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── video_processor.py # Video downloading and processing
│   │   ├── ai_analyzer.py     # AI content analysis
│   │   ├── clip_generator.py  # Clip creation and optimization
│   │   ├── title_generator.py # AI title generation
│   │   └── audio_manager.py   # Audio processing and mixing
│   ├── models/
│   │   ├── __init__.py
│   │   ├── content_scorer.py  # Custom AI scoring models
│   │   └── highlight_detector.py # Highlight detection algorithms
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── file_manager.py    # File operations and organization
│   │   ├── subtitle_parser.py # Subtitle processing utilities
│   │   ├── platform_optimizer.py # Platform-specific optimizations
│   │   └── progress_tracker.py # Progress monitoring
│   └── interface/
│       ├── __init__.py
│       ├── cli.py             # Command-line interface
│       └── web_app.py         # Streamlit web interface
├── assets/
│   ├── music/                 # Background music library
│   │   ├── upbeat/
│   │   ├── chill/
│   │   └── dramatic/
│   ├── fonts/                 # Subtitle fonts
│   └── templates/             # Video templates
├── data/
│   ├── models/                # AI model cache
│   ├── cache/                 # Processing cache
│   └── exports/               # Generated clips
├── tests/
│   ├── __init__.py
│   ├── test_video_processor.py
│   ├── test_ai_analyzer.py
│   ├── test_clip_generator.py
│   └── test_integration.py
└── docs/
    ├── API.md
    ├── SETUP.md
    └── USAGE.md
```

## 5. Detailed Implementation Specifications

### 5.1 Configuration Management (config/settings.yaml)
```yaml
# Application Settings
app:
  name: "YouTube Clip Generator"
  version: "1.0.0"
  debug: false
  max_workers: 4

# Video Processing
video:
  input_formats: ["mp4", "webm", "mkv", "avi"]
  output_format: "mp4"
  quality: "720p"
  fps: 30
  max_duration: 3600  # 1 hour max

# AI Processing
ai:
  whisper_model: "base"
  sentiment_threshold: 0.6
  highlight_window: 5  # seconds
  min_clip_duration: 15
  max_clip_duration: 60
  clips_per_video: 5

# Local LLM
llm:
  model: "llama2:7b"
  temperature: 0.7
  max_tokens: 200
  timeout: 30

# Platform Settings
platforms:
  tiktok:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 60
    subtitle_style: "bottom_center"
  
  youtube_shorts:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 60
    subtitle_style: "center"
  
  instagram_reels:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 90
    subtitle_style: "bottom_center"

# Audio Settings
audio:
  background_music: true
  music_volume: 0.3
  fade_duration: 2
  normalize: true
```

### 5.2 Core Video Processor (src/core/video_processor.py)
```python
import os
import yt_dlp
import ffmpeg
import cv2
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class VideoInfo:
    """Video metadata container"""
    title: str
    duration: float
    width: int
    height: int
    fps: float
    format: str
    file_path: str

class VideoProcessor:
    """Handles video downloading and preprocessing"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.temp_dir = Path("data/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def download_video(self, url: str, output_dir: str = None) -> VideoInfo:
        """
        Download video from URL using yt-dlp
        
        Args:
            url: Video URL (YouTube, etc.)
            output_dir: Output directory path
            
        Returns:
            VideoInfo object with metadata
        """
        if output_dir is None:
            output_dir = str(self.temp_dir)
        
        ydl_opts = {
            'format': 'best[height<=720]',
            'outtmpl': f'{output_dir}/%(title)s.%(ext)s',
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['en'],
            'subtitlesformat': 'srt',
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=True)
            
            # Get downloaded file path
            file_path = ydl.prepare_filename(info)
            if not os.path.exists(file_path):
                # Try common extensions
                for ext in ['.mp4', '.webm', '.mkv']:
                    test_path = file_path.rsplit('.', 1)[0] + ext
                    if os.path.exists(test_path):
                        file_path = test_path
                        break
            
            return self._extract_video_info(file_path, info)
    
    def _extract_video_info(self, file_path: str, yt_info: Dict = None) -> VideoInfo:
        """Extract video metadata using FFmpeg probe"""
        try:
            probe = ffmpeg.probe(file_path)
            video_stream = next(
                stream for stream in probe['streams']
                if stream['codec_type'] == 'video'
            )
            
            return VideoInfo(
                title=yt_info.get('title', Path(file_path).stem) if yt_info else Path(file_path).stem,
                duration=float(probe['format']['duration']),
                width=int(video_stream['width']),
                height=int(video_stream['height']),
                fps=eval(video_stream['r_frame_rate']),
                format=probe['format']['format_name'],
                file_path=file_path
            )
        except Exception as e:
            raise ValueError(f"Error extracting video info: {e}")
    
    def normalize_video(self, video_info: VideoInfo) -> str:
        """
        Normalize video format and quality for processing
        
        Args:
            video_info: VideoInfo object
            
        Returns:
            Path to normalized video file
        """
        output_path = str(self.temp_dir / f"normalized_{Path(video_info.file_path).stem}.mp4")
        
        try:
            (
                ffmpeg
                .input(video_info.file_path)
                .filter('scale', 1280, 720)
                .filter('fps', fps=30)
                .output(
                    output_path,
                    vcodec='libx264',
                    acodec='aac',
                    **{'crf': 23, 'preset': 'medium'}
                )
                .overwrite_output()
                .run(quiet=True)
            )
            return output_path
        except ffmpeg.Error as e:
            raise RuntimeError(f"Error normalizing video: {e}")
    
    def extract_frames(self, video_path: str, timestamps: List[float]) -> List[str]:
        """
        Extract frames at specific timestamps
        
        Args:
            video_path: Path to video file
            timestamps: List of timestamp in seconds
            
        Returns:
            List of frame file paths
        """
        frame_paths = []
        
        for i, timestamp in enumerate(timestamps):
            frame_path = str(self.temp_dir / f"frame_{i}_{timestamp:.2f}.jpg")
            
            try:
                (
                    ffmpeg
                    .input(video_path, ss=timestamp)
                    .filter('scale', 640, 360)
                    .output(frame_path, vframes=1)
                    .overwrite_output()
                    .run(quiet=True)
                )
                frame_paths.append(frame_path)
            except ffmpeg.Error:
                continue
        
        return frame_paths
```

### 5.3 AI Content Analyzer (src/core/ai_analyzer.py)
```python
import whisper
import torch
from transformers import pipeline
import nltk
from textblob import TextBlob
import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class ContentSegment:
    """Content segment with analysis data"""
    start_time: float
    end_time: float
    text: str
    sentiment_score: float
    engagement_score: float
    keywords: List[str]

class AIAnalyzer:
    """AI-powered content analysis for highlight detection"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize models
        self.whisper_model = whisper.load_model(
            config['ai']['whisper_model']
        ).to(self.device)
        
        self.sentiment_analyzer = pipeline(
            "sentiment-analysis",
            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
            device=0 if torch.cuda.is_available() else -1
        )
        
        # Download NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        try:
            nltk.data.find('corpora/stopwords')
        except LookupError:
            nltk.download('stopwords')
    
    def transcribe_audio(self, video_path: str) -> Dict:
        """
        Transcribe audio using Whisper
        
        Args:
            video_path: Path to video file
            
        Returns:
            Whisper transcription result
        """
        try:
            result = self.whisper_model.transcribe(
                video_path,
                task="transcribe",
                language="en"
            )
            return result
        except Exception as e:
            raise RuntimeError(f"Error transcribing audio: {e}")
    
    def analyze_content(self, transcription: Dict) -> List[ContentSegment]:
        """
        Analyze transcribed content for highlights
        
        Args:
            transcription: Whisper transcription result
            
        Returns:
            List of analyzed content segments
        """
        segments = []
        
        for segment in transcription['segments']:
            text = segment['text'].strip()
            if len(text) < 10:  # Skip very short segments
                continue
            
            # Sentiment analysis
            sentiment_result = self.sentiment_analyzer(text)[0]
            sentiment_score = self._normalize_sentiment_score(sentiment_result)
            
            # Engagement scoring
            engagement_score = self._calculate_engagement_score(text)
            
            # Keyword extraction
            keywords = self._extract_keywords(text)
            
            segments.append(ContentSegment(
                start_time=segment['start'],
                end_time=segment['end'],
                text=text,
                sentiment_score=sentiment_score,
                engagement_score=engagement_score,
                keywords=keywords
            ))
        
        return segments
    
    def _normalize_sentiment_score(self, sentiment_result: Dict) -> float:
        """Normalize sentiment score to 0-1 range"""
        label = sentiment_result['label']
        score = sentiment_result['score']
        
        if label == 'POSITIVE':
            return score
        elif label == 'NEGATIVE':
            return 1 - score
        else:  # NEUTRAL
            return 0.5
    
    def _calculate_engagement_score(self, text: str) -> float:
        """
        Calculate engagement potential score
        
        Args:
            text: Text content
            
        Returns:
            Engagement score (0-1)
        """
        engagement_keywords = [
            'amazing', 'incredible', 'wow', 'unbelievable', 'shocking',
            'must see', 'you won\'t believe', 'wait for it', 'watch this',
            'breaking', 'exclusive', 'first time', 'never seen',
            'hilarious', 'funny', 'laugh', 'crazy', 'insane'
        ]
        
        text_lower = text.lower()
        
        # Keyword matching
        keyword_score = sum(
            1 for keyword in engagement_keywords
            if keyword in text_lower
        ) / len(engagement_keywords)
        
        # Question detection
        question_score = 0.3 if '?' in text else 0
        
        # Exclamation detection
        exclamation_score = min(text.count('!') * 0.2, 0.3)
        
        # Length penalty for very long segments
        length_penalty = max(0, (len(text) - 200) / 1000)
        
        total_score = keyword_score + question_score + exclamation_score - length_penalty
        
        return max(0, min(1, total_score))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from text"""
        blob = TextBlob(text)
        
        # Get noun phrases
        noun_phrases = [str(phrase) for phrase in blob.noun_phrases]
        
        # Get important words (longer than 4 characters, not stopwords)
        from nltk.corpus import stopwords
        stop_words = set(stopwords.words('english'))
        
        important_words = [
            word.lower() for word in blob.words
            if len(word) > 4 and word.lower() not in stop_words
            and word.isalpha()
        ]
        
        # Combine and deduplicate
        keywords = list(set(noun_phrases + important_words))
        
        return keywords[:10]  # Return top 10 keywords
    
    def detect_highlights(self, segments: List[ContentSegment]) -> List[Tuple[float, float]]:
        """
        Detect highlight segments based on analysis
        
        Args:
            segments: List of analyzed content segments
            
        Returns:
            List of (start_time, end_time) tuples for highlights
        """
        # Calculate combined scores
        for segment in segments:
            segment.combined_score = (
                segment.sentiment_score * 0.4 +
                segment.engagement_score * 0.6
            )
        
        # Sort by combined score
        sorted_segments = sorted(
            segments,
            key=lambda x: x.combined_score,
            reverse=True
        )
        
        # Select top segments
        highlights = []
        min_clip_duration = self.config['ai']['min_clip_duration']
        max_clip_duration = self.config['ai']['max_clip_duration']
        clips_per_video = self.config['ai']['clips_per_video']
        
        for segment in sorted_segments[:clips_per_video * 2]:  # Get extra for filtering
            # Extend segment to minimum duration
            duration = segment.end_time - segment.start_time
            if duration < min_clip_duration:
                extension = (min_clip_duration - duration) / 2
                start_time = max(0, segment.start_time - extension)
                end_time = segment.end_time + extension
            else:
                start_time = segment.start_time
                end_time = min(segment.end_time, start_time + max_clip_duration)
            
            # Avoid overlapping clips
            overlap = False
            for existing_start, existing_end in highlights:
                if not (end_time <= existing_start or start_time >= existing_end):
                    overlap = True
                    break
            
            if not overlap and len(highlights) < clips_per_video:
                highlights.append((start_time, end_time))
        
        return highlights
```

### 5.4 Clip Generator (src/core/clip_generator.py)
```python
import ffmpeg
from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip
import pysrt
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class ClipSpec:
    """Clip generation specification"""
    start_time: float
    end_time: float
    platform: str
    title: str
    subtitle_path: Optional[str] = None
    background_music: Optional[str] = None

class ClipGenerator:
    """Generates optimized clips for different platforms"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.output_dir = Path("data/exports")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Platform configurations
        self.platforms = config['platforms']
    
    def generate_clip(self, video_path: str, clip_spec: ClipSpec) -> str:
        """
        Generate a single clip with platform optimization
        
        Args:
            video_path: Source video path
            clip_spec: Clip specification
            
        Returns:
            Path to generated clip
        """
        platform_config = self.platforms[clip_spec.platform]
        
        # Create output filename
        safe_title = "".join(c for c in clip_spec.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        output_path = self.output_dir / f"{safe_title}_{clip_spec.platform}.mp4"
        
        try:
            # Load video
            with VideoFileClip(video_path) as video:
                # Extract clip
                clip = video.subclip(clip_spec.start_time, clip_spec.end_time)
                
                # Apply platform optimization
                clip = self._optimize_for_platform(clip, platform_config)
                
                # Add subtitles if available
                if clip_spec.subtitle_path:
                    clip = self._add_subtitles(clip, clip_spec.subtitle_path, 
                                             clip_spec.start_time, clip_spec.end_time,
                                             platform_config['subtitle_style'])
                
                # Add background music if specified
                if clip_spec.background_music:
                    clip = self._add_background_music(clip, clip_spec.background_music)
                
                # Write final video
                clip.write_videofile(
                    str(output_path),
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    fps=platform_config.get('fps', 30)
                )
                
                clip.close()
            
            return str(output_path)
            
        except Exception as e:
            raise RuntimeError(f"Error generating clip: {e}")
    
    def _optimize_for_platform(self, clip, platform_config: Dict):
        """Apply platform-specific optimizations"""
        # Resize for platform
        resolution = platform_config['resolution']
        width, height = map(int, resolution.split('x'))
        
        # Calculate crop/scale parameters
        clip_aspect = clip.w / clip.h
        target_aspect = width / height
        
        if clip_aspect > target_aspect:
            # Video is wider - crop sides
            new_width = int(clip.h * target_aspect)
            x_center = clip.w // 2
            clip = clip.crop(
                x_center=x_center,
                width=new_width
            )
        elif clip_aspect < target_aspect:
            # Video is taller - crop top/bottom
            new_height = int(clip.w / target_aspect)
            y_center = clip.h // 2
            clip = clip.crop(
                y_center=y_center,
                height=new_height
            )
        
        # Resize to target resolution
        clip = clip.resize((width, height))
        
        # Ensure duration doesn't exceed platform limit
        max_duration = platform_config['max_duration']
        if clip.duration > max_duration:
            clip = clip.subclip(0, max_duration)
        
        return clip
    
    def _add_subtitles(self, clip, subtitle_path: str, start_time: float, 
                      end_time: float, style: str):
        """Add burned-in subtitles to clip"""
        try:
            # Load subtitle file
            subs = pysrt.open(subtitle_path)
            
            # Filter subtitles for clip duration
            clip_subs = []
            for sub in subs:
                sub_start = sub.start.ordinal / 1000.0
                sub_end = sub.end.ordinal / 1000.0
                
                # Check if subtitle overlaps with clip
                if sub_end > start_time and sub_start < end_time:
                    # Adjust timing relative to clip
                    relative_start = max(0, sub_start - start_time)
                    relative_end = min(clip.duration, sub_end - start_time)
                    
                    if relative_end > relative_start:
                        clip_subs.append({
                            'text': sub.text,
                            'start': relative_start,
                            'end': relative_end
                        })
            
            # Create subtitle clips
            subtitle_clips = []
            for sub in clip_subs:
                txt_clip = self._create_subtitle_clip(
                    sub['text'], 
                    sub['start'], 
                    sub['end'], 
                    clip.size,
                    style
                )
                subtitle_clips.append(txt_clip)
            
            # Composite with video
            if subtitle_clips:
                final_clip = CompositeVideoClip([clip] + subtitle_clips)
                return final_clip
            
        except Exception as e:
            print(f"Warning: Could not add subtitles: {e}")
        
        return clip
    
    def _create_subtitle_clip(self, text: str, start: float, end: float, 
                             video_size: Tuple[int, int], style: str):
        """Create a subtitle text clip"""
        width, height = video_size
        
        # Style configuration
        if style == "bottom_center":
            position = ('center', height * 0.85)
        elif style == "center":
            position = ('center', 'center')
        else:
            position = ('center', height * 0.85)
        
        # Create text clip
        txt_clip = TextClip(
            text,
            fontsize=width // 25,  # Responsive font size
            color='white',
            font='Arial-Bold',
            stroke_color='black',
            stroke_width=2
        ).set_position(position).set_duration(end - start).set_start(start)
        
        return txt_clip
    
    def _add_background_music(self, clip, music_path: str):
        """Add background music to clip"""
        try:
            from moviepy.editor import AudioFileClip
            
            # Load background music
            music = AudioFileClip(music_path)
            
            # Adjust music duration to match video
            if music.duration > clip.duration:
                music = music.subclip(0, clip.duration)
            else:
                # Loop music if needed
                loops_needed = int(clip.duration / music.duration) + 1
                music = music.loop(n=loops_needed).subclip(0, clip.duration)
            
            # Mix audio
            if clip.audio is not None:
                # Reduce background music volume
                music_volume = self.config['audio']['music_volume']
                music = music.volumex(music_volume)
                
                # Composite audio
                final_audio = clip.audio.volumex(1.0).set_start(0)
                music = music.set_start(0)
                
                from moviepy.audio.AudioClip import CompositeAudioClip
                composite_audio = CompositeAudioClip([final_audio, music])
                clip = clip.set_audio(composite_audio)
            else:
                clip = clip.set_audio(music)
            
        except Exception as e:
            print(f"Warning: Could not add background music: {e}")
        
        return clip
    
    def generate_batch(self, video_path: str, highlights: List[Tuple[float, float]], 
                      titles: List[str], subtitle_path: str = None) -> List[str]:
        """
        Generate multiple clips for all platforms
        
        Args:
            video_path: Source video path
            highlights: List of (start_time, end_time) tuples
            titles: List of titles for each clip
            subtitle_path: Path to subtitle file
            
        Returns:
            List of generated clip paths
        """
        generated_clips = []
        
        for i, ((start_time, end_time), title) in enumerate(zip(highlights, titles)):
            for platform in self.platforms.keys():
                clip_spec = ClipSpec(
                    start_time=start_time,
                    end_time=end_time,
                    platform=platform,
                    title=f"{title}_{i+1}",
                    subtitle_path=subtitle_path
                )
                
                try:
                    clip_path = self.generate_clip(video_path, clip_spec)
                    generated_clips.append(clip_path)
                    print(f"Generated: {clip_path}")
                except Exception as e:
                    print(f"Error generating clip for {platform}: {e}")
        
        return generated_clips
```

### 5.5 Title Generator (src/core/title_generator.py)
```python
import ollama
from langchain.llms import Ollama
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain
import yaml
from typing import List, Dict
from pathlib import Path

class TitleGenerator:
    """AI-powered title generation for viral content"""
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Initialize Ollama client
        self.llm = Ollama(
            model=config['llm']['model'],
            temperature=config['llm']['temperature']
        )
        
        # Load prompt templates
        self.prompts = self._load_prompts()
    
    def _load_prompts(self) -> Dict:
        """Load prompt templates from configuration"""
        prompt_file = Path("config/prompts.yaml")
        if prompt_file.exists():
            with open(prompt_file, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Default prompts
            return {
                'title_generation': """
Generate 3 viral, engaging titles for a {platform} video clip about: {content_description}

The titles should:
- Be attention-grabbing and curiosity-inducing
- Use platform-appropriate language and style
- Include relevant keywords
- Be optimized for {platform} algorithm
- Each be under {max_length} characters

Keywords found in content: {keywords}

Format your response as:
1. [Title 1]
2. [Title 2] 
3. [Title 3]
                """,
                'description_generation': """
Create a compelling description for a {platform} video with title: "{title}"

Content summary: {content_summary}
Keywords: {keywords}

The description should:
- Hook viewers in the first line
- Include relevant hashtags
- Be platform-appropriate length
- Encourage engagement (likes, shares, comments)

Description:
                """
            }
    
    def generate_titles(self, content_description: str, keywords: List[str], 
                       platform: str = "tiktok") -> List[str]:
        """
        Generate viral titles for content
        
        Args:
            content_description: Description of video content
            keywords: List of relevant keywords
            platform: Target platform (tiktok, youtube_shorts, instagram_reels)
            
        Returns:
            List of generated titles
        """
        # Platform-specific configurations
        platform_configs = {
            'tiktok': {'max_length': 100, 'style': 'casual, trendy'},
            'youtube_shorts': {'max_length': 100, 'style': 'clickbait, engaging'},
            'instagram_reels': {'max_length': 125, 'style': 'aesthetic, relatable'}
        }
        
        config = platform_configs.get(platform, platform_configs['tiktok'])
        
        # Create prompt
        prompt_template = PromptTemplate(
            input_variables=["platform", "content_description", "keywords", "max_length"],
            template=self.prompts['title_generation']
        )
        
        # Generate titles
        try:
            chain = LLMChain(llm=self.llm, prompt=prompt_template)
            result = chain.run(
                platform=platform,
                content_description=content_description,
                keywords=", ".join(keywords[:5]),  # Top 5 keywords
                max_length=config['max_length']
            )
            
            # Parse titles from response
            titles = self._parse_titles(result)
            return titles[:3]  # Return top 3 titles
            
        except Exception as e:
            print(f"Error generating titles: {e}")
            # Fallback titles
            return [
                f"Amazing {content_description[:30]}...",
                f"You Won't Believe This {content_description[:25]}!",
                f"This {content_description[:35]} is Incredible"
            ]
    
    def generate_description(self, title: str, content_summary: str, 
                           keywords: List[str], platform: str = "tiktok") -> str:
        """
        Generate platform-optimized description
        
        Args:
            title: Video title
            content_summary: Summary of video content
            keywords: List of relevant keywords
            platform: Target platform
            
        Returns:
            Generated description
        """
        prompt_template = PromptTemplate(
            input_variables=["platform", "title", "content_summary", "keywords"],
            template=self.prompts['description_generation']
        )
        
        try:
            chain = LLMChain(llm=self.llm, prompt=prompt_template)
            description = chain.run(
                platform=platform,
                title=title,
                content_summary=content_summary,
                keywords=", ".join(keywords[:8])
            )
            
            return description.strip()
            
        except Exception as e:
            print(f"Error generating description: {e}")
            # Fallback description
            return f"Check out this amazing content! {' '.join([f'#{kw}' for kw in keywords[:5]])}"
    
    def _parse_titles(self, response: str) -> List[str]:
        """Parse titles from LLM response"""
        titles = []
        lines = response.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line and (line.startswith(('1.', '2.', '3.')) or line.startswith('-')):
                # Remove numbering and clean up
                title = line
                for prefix in ['1.', '2.', '3.', '-', '*']:
                    if title.startswith(prefix):
                        title = title[len(prefix):].strip()
                        break
                
                # Remove brackets if present
                if title.startswith('[') and title.endswith(']'):
                    title = title[1:-1]
                
                if title:
                    titles.append(title)
        
        return titles
```

### 5.6 Main Application Entry Point (main.py)
```python
#!/usr/bin/env python3
"""
YouTube Clip Generator - Main Application Entry Point
"""

import sys
import argparse
import yaml
from pathlib import Path
from typing import List, Dict

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.video_processor import VideoProcessor
from src.core.ai_analyzer import AIAnalyzer  
from src.core.clip_generator import ClipGenerator
from src.core.title_generator import TitleGenerator
from src.utils.progress_tracker import ProgressTracker
from src.interface.cli import CLIInterface

class YouTubeClipGenerator:
    """Main application class"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """Initialize the application"""
        self.config = self._load_config(config_path)
        
        # Initialize components
        self.video_processor = VideoProcessor(self.config)
        self.ai_analyzer = AIAnalyzer(self.config)
        self.clip_generator = ClipGenerator(self.config)
        self.title_generator = TitleGenerator(self.config)
        self.progress = ProgressTracker()
    
    def _load_config(self, config_path: str) -> Dict:
        """Load application configuration"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"Configuration file not found: {config_path}")
            sys.exit(1)
    
    def process_video(self, video_input: str, output_dir: str = None) -> List[str]:
        """
        Process a single video and generate clips
        
        Args:
            video_input: Video URL or local file path
            output_dir: Output directory for clips
            
        Returns:
            List of generated clip paths
        """
        self.progress.start("Processing video...")
        
        try:
            # Step 1: Download/Process Video
            self.progress.update("Downloading and processing video...")
            if video_input.startswith(('http://', 'https://')):
                video_info = self.video_processor.download_video(video_input, output_dir)
            else:
                video_info = self.video_processor._extract_video_info(video_input)
            
            normalized_video = self.video_processor.normalize_video(video_info)
            
            # Step 2: AI Analysis
            self.progress.update("Analyzing content with AI...")
            transcription = self.ai_analyzer.transcribe_audio(normalized_video)
            content_segments = self.ai_analyzer.analyze_content(transcription)
            highlights = self.ai_analyzer.detect_highlights(content_segments)
            
            if not highlights:
                print("No highlights detected in video")
                return []
            
            # Step 3: Generate Titles
            self.progress.update("Generating viral titles...")
            content_description = video_info.title
            all_keywords = []
            for segment in content_segments:
                all_keywords.extend(segment.keywords)
            
            titles = []
            for i, (start, end) in enumerate(highlights):
                # Find relevant segment for this highlight
                relevant_text = ""
                for segment in content_segments:
                    if segment.start_time <= start <= segment.end_time:
                        relevant_text = segment.text
                        break
                
                if not relevant_text:
                    relevant_text = content_description
                
                clip_titles = self.title_generator.generate_titles(
                    relevant_text, all_keywords[:10], "tiktok"
                )
                titles.append(clip_titles[0] if clip_titles else f"Clip {i+1}")
            
            # Step 4: Generate Clips
            self.progress.update("Generating clips for all platforms...")
            
            # Look for subtitle file
            subtitle_path = None
            video_dir = Path(video_info.file_path).parent
            for subtitle_file in video_dir.glob("*.srt"):
                subtitle_path = str(subtitle_file)
                break
            
            generated_clips = self.clip_generator.generate_batch(
                normalized_video, highlights, titles, subtitle_path
            )
            
            self.progress.complete(f"Generated {len(generated_clips)} clips!")
            return generated_clips
            
        except Exception as e:
            self.progress.error(f"Error processing video: {e}")
            return []
    
    def process_batch(self, video_inputs: List[str], output_dir: str = None) -> Dict[str, List[str]]:
        """
        Process multiple videos in batch
        
        Args:
            video_inputs: List of video URLs or file paths
            output_dir: Output directory for clips
            
        Returns:
            Dictionary mapping input to generated clips
        """
        results = {}
        
        for i, video_input in enumerate(video_inputs):
            print(f"\nProcessing video {i+1}/{len(video_inputs)}: {video_input}")
            clips = self.process_video(video_input, output_dir)
            results[video_input] = clips
        
        return results

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="YouTube Clip Generator")
    parser.add_argument("input", help="Video URL or file path")
    parser.add_argument("-o", "--output", help="Output directory")
    parser.add_argument("-c", "--config", default="config/settings.yaml", 
                       help="Configuration file path")
    parser.add_argument("--batch", nargs="+", help="Process multiple videos")
    parser.add_argument("--web", action="store_true", help="Start web interface")
    
    args = parser.parse_args()
    
    if args.web:
        # Start web interface
        from src.interface.web_app import run_web_app
        run_web_app()
        return
    
    # Initialize application
    app = YouTubeClipGenerator(args.config)
    
    if args.batch:
        # Batch processing
        results = app.process_batch(args.batch, args.output)
        print("\nBatch processing complete!")
        for video, clips in results.items():
            print(f"{video}: {len(clips)} clips generated")
    else:
        # Single video processing
        clips = app.process_video(args.input, args.output)
        print(f"\nGenerated {len(clips)} clips:")
        for clip in clips:
            print(f"  - {clip}")

if __name__ == "__main__":
    main()
```

## 6. Installation and Setup Instructions

### 6.1 System Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+
- **Python**: 3.9 or higher
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space for models and cache
- **GPU**: NVIDIA GPU with CUDA support (optional but recommended)

### 6.2 Installation Steps

#### Step 1: Install System Dependencies
```bash
# Install FFmpeg (required)
# Windows (using chocolatey)
choco install ffmpeg

# macOS (using homebrew)
brew install ffmpeg

# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg
```

#### Step 2: Clone and Setup Project
```bash
# Create project directory
mkdir youtube_clip_generator
cd youtube_clip_generator

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
```

#### Step 3: Install Ollama (for local LLM)
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull required model
ollama pull llama2:7b
```

#### Step 4: Download Additional Models
```python
# Run this Python script to download required models
import whisper
import nltk

# Download Whisper model
whisper.load_model("base")

# Download NLTK data
nltk.download('punkt')
nltk.download('stopwords')
```

#### Step 5: Create Directory Structure
```bash
# Create required directories
mkdir -p config data/models data/cache data/exports assets/music assets/fonts tests
```

#### Step 6: Configuration Setup
Create `config/settings.yaml` with the provided configuration template.

## 7. Usage Examples

### 7.1 Command Line Usage
```bash
# Process single YouTube video
python main.py "https://www.youtube.com/watch?v=VIDEO_ID"

# Process local video file
python main.py "/path/to/video.mp4" -o ./output

# Batch processing
python main.py --batch "https://youtube.com/watch?v=ID1" "https://youtube.com/watch?v=ID2"

# Start web interface
python main.py --web
```

### 7.2 Python API Usage
```python
from youtube_clip_generator import YouTubeClipGenerator

# Initialize
app = YouTubeClipGenerator()

# Process video
clips = app.process_video("https://www.youtube.com/watch?v=VIDEO_ID")

# Print results
for clip in clips:
    print(f"Generated: {clip}")
```

## 8. Testing Strategy

### 8.1 Unit Tests
Create comprehensive unit tests in the `tests/` directory:

```python
# tests/test_video_processor.py
import unittest
from src.core.video_processor import VideoProcessor

class TestVideoProcessor(unittest.TestCase):
    def setUp(self):
        self.config = {"video": {"output_format": "mp4"}}
        self.processor = VideoProcessor(self.config)
    
    def test_video_info_extraction(self):
        # Test video metadata extraction
        pass
    
    def test_video_normalization(self):
        # Test video format normalization
        pass

# Run tests
python -m pytest tests/
```

### 8.2 Integration Tests
```python
# tests/test_integration.py
def test_full_pipeline():
    """Test complete video processing pipeline"""
    app = YouTubeClipGenerator()
    
    # Use test video
    test_video = "path/to/test/video.mp4"
    clips = app.process_video(test_video)
    
    assert len(clips) > 0
    assert all(Path(clip).exists() for clip in clips)
```

## 9. Performance Optimization

### 9.1 GPU Acceleration
- Configure CUDA for PyTorch models
- Use GPU-accelerated FFmpeg when available
- Implement batch processing for AI models

### 9.2 Caching Strategy
- Cache AI model outputs
- Store processed video metadata
- Implement smart re-processing detection

### 9.3 Parallel Processing
```python
# Implement concurrent processing
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

def process_video_parallel(video_list):
    max_workers = min(multiprocessing.cpu_count(), 4)
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_single_video, video) 
                  for video in video_list]
        
        results = [future.result() for future in futures]
    
    return results
```

## 10. Deployment and Distribution

### 10.1 Packaging for Distribution
```bash
# Create distributable package
pip install pyinstaller

# Create executable
pyinstaller --onefile --add-data "config:config" --add-data "assets:assets" main.py
```

### 10.2 Docker Deployment
```dockerfile
# Dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y ffmpeg

# Copy application
COPY . /app
WORKDIR /app

# Install Python dependencies
RUN pip install -r requirements.txt

# Run application
CMD ["python", "main.py", "--web"]
```

## 11. Future Enhancements

### 11.1 Planned Features
- **Real-time Processing**: Live stream clip generation
- **Advanced AI Models**: Custom-trained highlight detection
- **Social Media Integration**: Direct upload to platforms
- **Analytics Dashboard**: Performance tracking and optimization
- **Custom Templates**: User-defined clip styles and branding

### 11.2 Scalability Considerations
- **Cloud Processing**: AWS/GCP integration for heavy workloads
- **Database Integration**: PostgreSQL for metadata storage
- **API Service**: RESTful API for remote processing
- **Queue Management**: Redis/RabbitMQ for job processing

## 12. Troubleshooting and Support

### 12.1 Common Issues
- **FFmpeg not found**: Ensure FFmpeg is installed and in PATH
- **CUDA errors**: Verify GPU drivers and CUDA installation
- **Memory errors**: Reduce batch size or video quality
- **Model download failures**: Check internet connection and disk space

### 12.2 Debug Mode
Enable debug logging in configuration:
```yaml
app:
  debug: true
  log_level: "DEBUG"
```

### 12.3 Performance Monitoring
Implement logging and monitoring:
```python
import logging
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logging.info(f"{func.__name__} took {end_time - start_time:.2f} seconds")
        return result
    
    return wrapper
```

This comprehensive specification provides everything needed to implement a fully functional YouTube clip generation system. The AI coder can follow this document step-by-step to create a production-ready application with all the specified features.