# Application Settings
app:
  name: "YouTube Clip Generator"
  version: "1.0.0"
  debug: false
  max_workers: 4

# Video Processing
video:
  input_formats: ["mp4", "webm", "mkv", "avi"]
  output_format: "mp4"
  quality: "720p"
  fps: 30
  max_duration: 3600  # 1 hour max

# AI Processing
ai:
  whisper_model: "base"
  sentiment_threshold: 0.6
  highlight_window: 5
  min_clip_duration: 15
  max_clip_duration: 60
  clips_per_video: 5

# Local LLM
llm:
  model: "qwen3:4b"
  temperature: 0.7
  max_tokens: 200
  timeout: 30

# Platform Settings
platforms:
  tiktok:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 60
    subtitle_style: "bottom_center"
  
  youtube_shorts:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 60
    subtitle_style: "center"
  
  instagram_reels:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 90
    subtitle_style: "bottom_center"

# Audio Settings
audio:
  background_music: true
  music_volume: 0.3
  fade_duration: 2
  normalize: true 