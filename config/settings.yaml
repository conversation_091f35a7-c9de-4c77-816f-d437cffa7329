# Application Settings
app:
  name: "YouTube Clip Generator"
  version: "1.0.0"
  debug: false
  max_workers: 4
  pipeline_max_workers: 1
  log_level: "INFO"
  log_file: "logs/app.log"

# Video Processing
video:
  input_formats: ["mp4", "webm", "mkv", "avi"]
  output_format: "mp4"
  quality: "1080p"  # Download high quality source
  fps: 30
  max_duration: 3600  # 1 hour max
  codec: "libx264"
  crf: 23
  preset: "medium"

# AI Processing
ai:
  whisper_model: "base"
  sentiment_threshold: 0.6
  highlight_window: 5
  min_clip_duration: 15
  max_clip_duration: 90
  clips_per_video: 5

# LLM Configuration (Qwen via Dashscope)
llm:
  model: "qwen-turbo"
  qwen_model: "qwen-turbo"
  temperature: 0.7
  max_tokens: 200
  timeout: 30
  # qwen_api_key: "your_api_key_here"  # Better to use environment variable QWEN_API_KEY

# Platform Settings
platforms:
  tiktok:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 60
    subtitle_style: "bottom_center"
    fps: 30

  youtube_shorts:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 60
    subtitle_style: "center"
    fps: 30

  instagram_reels:
    aspect_ratio: "9:16"
    resolution: "1080x1920"
    max_duration: 90
    subtitle_style: "bottom_center"
    fps: 30

# Subtitle Styling Configuration
subtitle_styles:
  default:
    font: "Arial-Bold"
    fontsize_ratio_width: 0.04  # 4% of video width
    color: "white"
    stroke_color: "black"
    stroke_width: 2
    position: "bottom_center"
    bg_color: null  # transparent background

  bottom_center:
    font: "Arial-Bold"
    fontsize_ratio_width: 0.04
    color: "white"
    stroke_color: "black"
    stroke_width: 2
    position: "bottom_center"
    bg_color: null

  center:
    font: "Arial-Bold"
    fontsize_ratio_width: 0.04
    color: "white"
    stroke_color: "black"
    stroke_width: 2
    position: "center"
    bg_color: null

  top_center:
    font: "Arial-Bold"
    fontsize_ratio_width: 0.04
    color: "white"
    stroke_color: "black"
    stroke_width: 2
    position: "top_center"
    bg_color: null

# Default subtitle style name
default_subtitle_style: "default"

# Clip Generator Default Platform
clip_generator_default_platform: "youtube_shorts"

# Watermark Configuration
watermark:
  enabled: false
  image_path: "assets/watermark.png"
  size_ratio_width: 0.15  # 15% of video width
  opacity: 0.7
  position_preset: "bottom_right"  # top_right, top_left, bottom_right, bottom_left, center
  margin_ratio: 0.02  # 2% margin from edges

# Title Overlay Configuration
title_overlay:
  enabled: false
  font: "Arial-Bold"
  fontsize_ratio_width: 0.05  # 5% of video width
  color: "white"
  stroke_color: "black"
  stroke_width: 2
  position_preset: "top_center"  # top_center, center, bottom_center
  duration_seconds: 5
  vertical_offset_ratio: 0.1  # 10% from top/bottom

# Audio Settings
audio:
  background_music: false
  background_music_path: "assets/music/default.mp3"
  music_volume: 0.3
  fade_duration: 2
  normalize: true
  bitrate: "192k"