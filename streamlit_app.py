import streamlit as st
from pathlib import Path
import sys

# Add src to sys.path to allow direct imports
sys.path.append(str(Path(__file__).resolve().parent / "src"))

from src.main import Application
from src.utils.config_loader import ConfigLoader

# Load config for Streamlit app
config = ConfigLoader().load_settings()
st.set_page_config(page_title=config['app']['name'], layout="wide")

# --- UI Elements ---
st.title(f"🎬 {config['app']['name']}")
st.markdown("Automatically generate engaging short video clips from YouTube videos.")

# Input: YouTube URL
video_url = st.text_input(
    "Enter YouTube Video URL:",
    placeholder="https://www.youtube.com/watch?v=your_video_id"
)

# Output Directory (Optional)
output_dir_default = str(Path("data/exports").resolve())
output_dir = st.text_input(
    "Output Directory (Optional):",
    value=output_dir_default,
    help=f"Clips will be saved here. Default: {output_dir_default}"
)

# Process Button
if st.button("✨ Generate Clips", type="primary"):
    if not video_url:
        st.error("Please enter a YouTube Video URL.")
    else:
        try:
            # Initialize the application logic (includes environment validation)
            app = Application()

            with st.spinner("Processing video... This might take a while! ⏳"):
                # --- Capturing logs for display in Streamlit ---
                import logging
                from io import StringIO

                log_stream = StringIO()
                # Get the root logger used by the app and add a stream handler
                app_logger = logging.getLogger("youtube_clip_generator") # Same name as in setup_logger
                stream_handler = logging.StreamHandler(log_stream)
                formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                stream_handler.setFormatter(formatter)
                app_logger.addHandler(stream_handler)

                # Run the application
                app.run(video_url, output_dir)

                # Display logs
                st.subheader("Processing Logs:")
                st.text_area("Logs", value=log_stream.getvalue(), height=300, disabled=True)
                log_stream.close()
                app_logger.removeHandler(stream_handler) # Clean up handler

            st.success("Clip generation complete! 🎉")

            # Display generated clips (if any)
            export_path = Path(output_dir if output_dir else app.clip_generator.output_dir)
            generated_files = list(export_path.glob("*.mp4"))

            if generated_files:
                st.subheader("Generated Clips:")
                for f in generated_files:
                    st.video(str(f))
                    st.markdown(f"Download: `{f.name}`") # Provide filename for context
            else:
                st.info("No clips were generated or found in the output directory.")

        except RuntimeError as e:
            if "Environment validation failed" in str(e):
                st.error("❌ Environment Setup Error")
                st.error("Please ensure you have set the QWEN_API_KEY environment variable.")
                st.info("💡 **How to fix this:**")
                st.code("export QWEN_API_KEY='your_api_key_here'", language="bash")
                st.info("Get your API key from: https://dashscope.console.aliyun.com/")
            else:
                st.error(f"An error occurred: {e}")
                st.exception(e)
        except Exception as e:
            st.error(f"An unexpected error occurred: {e}")
            st.exception(e) # Show full traceback

# --- Sidebar ---
st.sidebar.header("Configuration")
st.sidebar.json(config, expanded=False)

st.sidebar.markdown("---_Developed with ❤️_---")

# --- Styling (Optional) ---
st.markdown("""
<style>
    .stButton>button {
        width: 100%;
    }
    /* The following rule was removed as it caused text visibility issues in dark mode:
    .stTextInput>div>div>input {
        background-color: #f0f2f6;
    }
    */
</style>
""", unsafe_allow_html=True)