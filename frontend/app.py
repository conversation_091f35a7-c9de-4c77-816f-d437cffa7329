"""Streamlit frontend for the AI Short Clip Generator.

Allows users to either upload an MP4 video or provide a YouTube URL 
for processing. The backend then extracts short clips from the video.
"""
import streamlit as st
import requests
# import os # os was imported but not used

# TODO: Make API base URL configurable (e.g., via environment variable)
API_BASE_URL = "http://localhost:8888"
API_UPLOAD = f"{API_BASE_URL}/upload"
API_YOUTUBE = f"{API_BASE_URL}/youtube"

st.set_page_config(page_title="Clip Extractor", layout="centered")
st.title("📹 AI Short Clip Generator")

# -------------------------------
# 📼 VIDEO FILE UPLOAD SECTION
# -------------------------------
st.header("1. Upload MP4 video")
uploaded_file = st.file_uploader("Choose an MP4 file", type=["mp4", "mov", "avi", "mkv"]) # Added more types

if uploaded_file:
    if st.button("Process Uploaded Video"):
        with st.spinner("Uploading and processing..."):
            files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
            try:
                response = requests.post(API_UPLOAD, files=files, timeout=300) # Added timeout
                response.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)
                result = response.json()
                st.success("Done! Clips created:")
                if result.get("clips"):
                    for path in result["clips"]:
                        # Assuming backend returns web-accessible paths or data URIs
                        st.video(path)
                        st.caption(path) # Show path for reference
                else:
                    st.info("No clips were generated or returned by the backend.")
            except requests.exceptions.HTTPError as e:
                st.error(f"API Error: {e.response.status_code} - {e.response.text}")
            except requests.exceptions.RequestException as e:
                st.error(f"Failed to connect to API: {e}")
            except Exception as e:
                st.error(f"An unexpected error occurred: {e}")

# -------------------------------
# 🔗 YOUTUBE URL SECTION
# -------------------------------
st.header("2. Paste YouTube URL")
youtube_url = st.text_input("YouTube Link (must be public)")

if youtube_url:
    if st.button("Process YouTube Video"):
        with st.spinner("Downloading and processing YouTube video..."):
            try:
                response = requests.post(API_YOUTUBE, data={"url": youtube_url}, timeout=300) # Added timeout
                response.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)
                result = response.json()
                st.success("Done! Clips created:")
                if result.get("clips"):
                    for path in result["clips"]:
                        st.video(path)
                        st.caption(path) # Show path for reference
                else:
                    st.info("No clips were generated or returned by the backend.")
            except requests.exceptions.HTTPError as e:
                st.error(f"API Error: {e.response.status_code} - {e.response.text}")
            except requests.exceptions.RequestException as e:
                st.error(f"Failed to connect to API: {e}")
            except Exception as e:
                st.error(f"An unexpected error occurred: {e}")
