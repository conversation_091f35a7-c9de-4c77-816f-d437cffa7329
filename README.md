# YouTube Clip Generator

Automatically generate engaging short video clips from YouTube videos, optimized for platforms like TikTok, YouTube Shorts, and Instagram Reels.

## Features

- **YouTube Video Downloading:** Download videos directly from YouTube URLs.
- **Video Normalization:** Standardize video format, resolution (720p), and FPS (30) for consistent processing.
- **AI-Powered Transcription:** Accurate audio transcription using `faster-whisper`.
- **Content Analysis:**
    - Sentiment analysis to identify emotional peaks.
    - Engagement scoring based on keywords and text patterns.
    - Keyword extraction for content understanding.
- **Highlight Detection:** Automatically identify the most engaging segments of a video.
- **Clip Generation:** Create short clips from detected highlights.
- **Platform Optimization:**
    - Format clips for TikTok, YouTube Shorts, Instagram Reels (9:16 aspect ratio).
    - Adjust resolution and maximum duration per platform.
- **Automated Titling:** Generate viral, platform-specific titles using a local LLM (Ollama).
- **Subtitle Integration:** Burn-in subtitles from downloaded SRT files.
- **Background Music (Optional):** Add background music to clips.
- **Streamlit UI:** Easy-to-use web interface for generating clips.
- **Configurable:** Settings, prompts, and platform specifics managed via YAML files.

## Project Structure

```
.youtube_clip_generator/
├── .gitignore
├── README.md
├── requirements.txt
├── streamlit_app.py         # Main Streamlit UI application
|
├── config/                  # Configuration files
│   ├── settings.yaml        # Main application and processing settings
│   ├── platforms.yaml       # Platform-specific parameters (empty by default, uses settings.yaml)
│   └── prompts.yaml         # LLM prompt templates for title/description generation
|
├── src/                     # Source code
│   ├── __init__.py
│   ├── main.py              # Core application logic and CLI entry point
│   |
│   ├── core/                # Core processing modules
│   │   ├── __init__.py
│   │   ├── video_processor.py # Video downloading and basic FFmpeg operations
│   │   ├── ai_analyzer.py     # Transcription, sentiment, highlight detection
│   │   ├── clip_generator.py  # Clip cutting, platform optimization, subtitles
│   │   └── title_generator.py # LLM-based title and description generation
│   |
│   ├── utils/               # Utility modules
│   │   ├── __init__.py
│   │   ├── config_loader.py   # Loads YAML configuration files
│   │   └── logger.py          # Logging setup
│   |
│   ├── interface/           # UI related components (currently Streamlit app is top-level)
│   │   └── __init__.py
│   |
│   └── models/              # Data models/schemas (dataclasses used directly in modules for now)
│       └── __init__.py
|
├── assets/                  # Static assets
│   ├── music/
│   │   ├── upbeat/
│   │   ├── chill/
│   │   └── dramatic/
│   ├── fonts/
│   └── templates/           # Video templates (e.g., for intros/outros - future)
|
├── data/                    # Data storage
│   ├── temp/                # Temporary files (downloaded videos, frames, etc.)
│   ├── cache/               # Caching for models or intermediate results
│   └── exports/             # Final generated video clips
|
├── logs/                    # Application logs
│   └── app.log
|
├── tests/                   # Unit and integration tests
│   └── __init__.py
|
└── docs/                    # Project documentation
    └── PLANNING.md          # Initial planning and thoughts (can be this file)
```

## Setup

1.  **Clone the repository:**
    ```bash
    git clone <repository_url>
    cd youtube_clip_generator
    ```

2.  **Create a virtual environment and activate it:**
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Install FFmpeg:**
    FFmpeg is required for video processing. Follow the installation instructions for your OS:
    -   **macOS (using Homebrew):** `brew install ffmpeg`
    -   **Linux (using apt):** `sudo apt update && sudo apt install ffmpeg`
    -   **Windows:** Download from [FFmpeg website](https://ffmpeg.org/download.html) and add to PATH.

5.  **Set up Qwen API (for Content Analysis):**
    -   Get your API key from [Dashscope Console](https://dashscope.console.aliyun.com/)
    -   Set the environment variable:
        ```bash
        export QWEN_API_KEY='your_api_key_here'
        ```
    -   Or copy `.env.example` to `.env` and fill in your API key:
        ```bash
        cp .env.example .env
        # Edit .env file with your API key
        ```

6.  **Whisper Model for `faster-whisper`:**
    `faster-whisper` will automatically download the specified model (e.g., "base", "small", "medium") on its first use if it's not already cached. The model is specified in `config/settings.yaml` under `ai.whisper_model`.

7.  **(Optional) Download NLTK data for AI Analyzer:**
    The `AIAnalyzer` will attempt to download `punkt` and `stopwords` from NLTK if not found. You can also pre-download them using the `download_nltk_data.py` script provided in the repository or by running:
    ```python
    import nltk
    nltk.download('punkt')
    nltk.download('stopwords')
    ```

8.  **Verify Setup:**
    Test your setup by running:
    ```bash
    python src/main.py --help
    ```
    This will validate your environment and show available options.

## Configuration

Key configurations can be modified in `config/settings.yaml`:

-   `app`: General application settings (name, version, debug mode, workers).
-   `video`: Video input/output formats, quality, FPS, max duration.
-   `ai`: `faster-whisper` model (e.g., "base", "small", "medium"), sentiment threshold, highlight window, clip duration, clips per video.
-   `llm`: Qwen model configuration, temperature, max tokens, timeout for content analysis.
-   `platforms`: Platform-specific settings (aspect ratio, resolution, max duration, subtitle style) for TikTok, YouTube Shorts, Instagram Reels.
-   `subtitle_styles`: Customizable subtitle styling (font, size, color, position) for different platforms.
-   `watermark`: Watermark configuration (enable, image path, size, position, opacity).
-   `title_overlay`: Title overlay settings (enable, font, size, position, duration).
-   `audio`: Background music settings (enable, volume, fade, normalize).

Prompts for LLM-based title and description generation are in `config/prompts.yaml`.

## Usage

### Streamlit Web UI

Run the Streamlit application:

```bash
streamlit run streamlit_app.py
```

Open your browser and navigate to the provided URL (usually `http://localhost:8501`).

1.  Enter the YouTube video URL.
2.  (Optional) Modify the output directory.
3.  Click "Generate Clips".
4.  View progress logs and generated clips directly in the interface.

### Command Line Interface (CLI)

The `src/main.py` script can also be run from the command line:

```bash
python src/main.py <youtube_video_url> [-o <output_directory>]
```

**Example:**

```bash
python src/main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ" -o my_clips
```

Logs will be printed to the console and saved to `logs/app.log`.
Generated clips will be saved in the `data/exports/` directory by default, or the specified output directory.

## Development

-   **Core Logic:** `src/core/` contains the main processing modules.
-   **Utilities:** `src/utils/` provides helper functions for configuration and logging.
-   **Configuration:** `config/` for all settings.
-   **Entry Points:** `streamlit_app.py` for UI, `src/main.py` for CLI.

### Adding New Platforms

1.  Add platform configuration to `config/settings.yaml` under the `platforms` key.
2.  Update `ClipGenerator` and `TitleGenerator` if platform-specific logic is needed beyond simple configuration changes.

### TODOs & Future Enhancements

-   [ ] More sophisticated highlight detection algorithms.
-   [ ] Customizable video templates (intros, outros, branding).
-   [ ] Advanced subtitle styling and positioning.
-   [ ] Support for uploading local video files.
-   [ ] Background task queue for long-running jobs (e.g., Celery).
-   [ ] More robust error handling and retries for external services.
-   [ ] Option to choose different background music tracks or moods.
-   [ ] Automated upload to platforms (requires API access).
-   [ ] Comprehensive unit and integration tests.
-   [ ] Dockerization for easier deployment.

## License

This project is for educational purposes. Please respect YouTube's Terms of Service and copyright laws when using this tool.