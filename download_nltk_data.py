import nltk

try:
    nltk.data.find('tokenizers/punkt')
    print("NLTK 'punkt' tokenizer is already available.")
except LookupError:
    print("NLTK 'punkt' tokenizer not found. Downloading...")
    nltk.download('punkt')
    print("NLTK 'punkt' tokenizer downloaded successfully.")
except Exception as e:
    print(f"An error occurred while checking/downloading 'punkt': {e}")

try:
    nltk.data.find('corpora/stopwords')
    print("NLTK 'stopwords' corpus is already available.")
except LookupError:
    print("NLTK 'stopwords' corpus not found. Downloading...")
    nltk.download('stopwords')
    print("NLTK 'stopwords' corpus downloaded successfully.")
except Exception as e:
    print(f"An error occurred while checking/downloading 'stopwords': {e}")

print("\nNLTK data check/download process complete.") 