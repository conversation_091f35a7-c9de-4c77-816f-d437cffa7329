import os
import uuid
import shutil
from backend.utils import (
    ensure_dir, logger, call_dashscope_qwen,
    parse_time, format_time, timestamp
)
# from backend.video_editor import generate_clip_with_effects
# from backend.highlight_selector import get_clip_timestamps_from_transcript

# ---------------------------
# 🛠️ CONFIGURATION
# ---------------------------
# API_KEY = os.getenv("GROQ_API_KEY")
QWEN_API_KEY = os.getenv("QWEN_API_KEY")
MODEL = "qwen-turbo"
PLATFORM = "tiktok"  # or "youtube", or "reels"
TRANSCRIPT_PATH = "data/sample.srt"
VIDEO_PATH = "data/sample.mp4"
OUTPUT_DIR = "outputs/clips"
MUSIC_PATH = "assets/music/bg.mp3"

# ---------------------------
# 🔤 TITLE GENERATOR
# ---------------------------
def generate_clip_title(system_prompt: str, clip_transcript: str) -> str:
    """Generates a short, viral video clip title based on a transcript using Qwen (Dashscope).

    Args:
        system_prompt (str): The system prompt for the LLM.
        clip_transcript (str): The transcript of the video clip.

    Returns:
        str: The generated video clip title, or a default if generation fails.
    """
    title_prompt = "Based on the transcript below, generate a short, viral video clip title:"
    
    if not QWEN_API_KEY:
        logger.error("QWEN_API_KEY not found in environment. Cannot generate title.")
        return "Default Clip Title"
        
    # Call the new call_dashscope_qwen function from utils
    # It expects is_json_response_expected=False for raw text output
    response_content = call_dashscope_qwen(
        api_key=QWEN_API_KEY,
        model_name=MODEL,
        system_prompt=system_prompt,
        user_prompt=f"{title_prompt}\n\nTranscript:\n{clip_transcript}",
        is_json_response_expected=False 
    )

    if response_content and isinstance(response_content, str):
        return response_content.strip('"\\n ')
    else:
        logger.warning(f"Failed to generate title or got unexpected response type for transcript: {clip_transcript[:100]}... Using default.")
        return "Default Clip Title From Pipeline"

# ---------------------------
# 🚀 MAIN PIPELINE (Simplified - assuming this file's main is for a specific test/purpose)
#    The main clip generation loop using highlight_selector.py and video_editor.py
#    is likely orchestrated elsewhere or this is a standalone part.
#    For this file, we'll assume the main() demonstrates the title generation primarily.
# ---------------------------
def main():
    """Main pipeline for generating video clips.

    This function orchestrates the video clip generation process, including:
    - Reading the transcript.
    - Identifying highlight clips.
    - Generating titles for each clip.
    - Creating video clips with effects and subtitles.
    """
    logger.info("🚀 Starting clip generation pipeline (demonstrating title generation)...")
    ensure_dir(OUTPUT_DIR)

    # Sample transcript for demonstration
    sample_clip_transcript = "This is a sample transcript for a very exciting video about AI and creativity."
    system_prompt_for_title = "You are a viral video editor assistant specializing in catchy titles."

    logger.info(f"Generating title for sample transcript: '{sample_clip_transcript}'")
    title = generate_clip_title(system_prompt_for_title, sample_clip_transcript)
    logger.info(f"Generated title: '{title}'")

    # The rest of the original main function that processes actual clips
    # from TRANSCRIPT_PATH would go here if this was the main execution point
    # For now, the parts involving get_clip_timestamps_from_transcript and 
    # generate_clip_with_effects are commented out as they are in other modules.

    # Example of how it might have been (conceptual):
    # with open(TRANSCRIPT_PATH, "r", encoding="utf-8") as f:
    #     srt_text = f.read()
    # job_id = str(uuid.uuid4())[:8]
    # clips = get_clip_timestamps_from_transcript(srt_text, platform=PLATFORM) # This function is in highlight_selector.py
    # logger.info(f"✅ Extracted {len(clips)} valid clips for {PLATFORM}")
    # for idx, clip in enumerate(clips):
    #     start_sec = parse_time(clip["start"])
    #     end_sec = parse_time(clip["end"])
    #     clip_tx = f"[{clip['start']} - {clip['end']}] Reason: {clip['reason']}"
    #     actual_title = generate_clip_title(system_prompt_for_title, clip_tx)
    #     safe_title = "".join(c for c in actual_title if c.isalnum() or c in (" ", "_", "-")).rstrip()
    #     output_path = os.path.join(OUTPUT_DIR, f"{job_id}_clip{idx+1}_{safe_title}.mp4")
    #     logger.info(f"🎬 Creating clip {idx+1}: {actual_title} [{clip['start']} → {clip['end']}]")
    #     generate_clip_with_effects( # This function is in video_editor.py
    #         input_video_path=VIDEO_PATH,
    #         output_path=output_path,
    #         start_time=start_sec,
    #         end_time=end_sec,
    #         subtitle_path=TRANSCRIPT_PATH, # This might need to be clip specific
    #         overlay_text=actual_title,
    #         background_music=MUSIC_PATH
    #     )

    logger.info("✅ Clip generation pipeline (title demo) finished.")

if __name__ == "__main__":
    main()
