# 🚀 Quick Start Guide

Get up and running with the YouTube Clip Generator in 5 minutes!

## Prerequisites

- Python 3.8+
- FFmpeg installed on your system
- Qwen API key from [Dashscope Console](https://dashscope.console.aliyun.com/)

## 1. Install Dependencies

```bash
pip install -r requirements.txt
```

## 2. Set Up Environment

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your API key
# QWEN_API_KEY=your_actual_api_key_here
```

Or set it directly:
```bash
export QWEN_API_KEY='your_actual_api_key_here'
```

## 3. Test Your Setup

```bash
python test_setup.py
```

This will verify all dependencies and configurations are working.

## 4. Run the Application

### Option A: Web Interface (Recommended)
```bash
streamlit run streamlit_app.py
```

Then open your browser to the provided URL (usually http://localhost:8501).

### Option B: Command Line
```bash
python src/main.py "https://www.youtube.com/watch?v=VIDEO_ID"
```

## 5. Generate Your First Clips

1. Enter a YouTube video URL
2. Click "Generate Clips"
3. Wait for processing (this may take a few minutes)
4. Download your generated clips from the `data/exports/` directory

## Configuration

The application works out of the box, but you can customize:

- **Subtitle styles**: Edit `config/settings.yaml` → `subtitle_styles`
- **Platform settings**: Edit `config/settings.yaml` → `platforms`
- **Video quality**: Edit `config/settings.yaml` → `video.quality`
- **Clip duration**: Edit `config/settings.yaml` → `ai.max_clip_duration`

## Troubleshooting

### "Environment validation failed"
- Make sure you've set the `QWEN_API_KEY` environment variable
- Verify your API key is valid

### "FFmpeg not found"
- Install FFmpeg: `brew install ffmpeg` (macOS) or `sudo apt install ffmpeg` (Linux)
- On Windows, download from [FFmpeg website](https://ffmpeg.org/download.html)

### "No clips generated"
- Check that the video has English subtitles
- Try a different video with clear speech
- Check the logs for detailed error messages

## Need Help?

1. Run `python test_setup.py` to diagnose issues
2. Check the logs in `logs/app.log`
3. Review the full README.md for detailed documentation

Happy clipping! 🎬✨
