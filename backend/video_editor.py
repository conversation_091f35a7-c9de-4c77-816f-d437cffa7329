import os
import subprocess
from utils import logger

def generate_clip_with_effects(
    input_video_path: str,
    output_path: str,
    start_time: float,
    end_time: float,
    subtitle_path: str = None,
    overlay_text: str = None,
    background_music: str = None
) -> None:
    """Generates a video clip with specified effects using FFmpeg.

    Args:
        input_video_path (str): Path to the input video file.
        output_path (str): Path to save the generated clip.
        start_time (float): Start time of the clip in seconds.
        end_time (float): End time of the clip in seconds.
        subtitle_path (str, optional): Path to an SRT subtitle file to burn. 
                                     Defaults to None.
        overlay_text (str, optional): Text to overlay on the video. 
                                    Defaults to None.
        background_music (str, optional): Path to a background music file.
                                          Defaults to None.
    """
    duration = end_time - start_time
    filters = []

    # Draw text overlay
    if overlay_text:
        escaped_text = overlay_text.replace("'", "\\'") # Basic escaping for single quotes
        # TODO: Make font path configurable or use a bundled font for cross-platform compatibility.
        # Current font path is macOS specific.
        font_path = "/System/Library/Fonts/Supplemental/Arial Bold.ttf"
        filters.append(
            f"drawtext=fontfile='{font_path}':"
            f"text='{escaped_text}':fontcolor=white:fontsize=48:box=1:"
            f"boxcolor=black@0.6:boxborderw=10:x=(w-text_w)/2:y=h-100"
        )

    # Burn subtitles
    if subtitle_path:
        # TODO: Review and enhance subtitle path escaping for FFmpeg robustness.
        # FFmpeg path escaping can be complex depending on special characters.
        # Consider using a helper function for FFmpeg path escaping if issues arise.
        filters.append(f"subtitles='{subtitle_path}'")

    # Combine filters
    filter_complex = ",".join(filters) if filters else None

    # FFmpeg command parts
    cmd = [
        "ffmpeg",
        "-y",  # Overwrite output
        "-ss", str(start_time),
        "-i", input_video_path,
    ]

    # Add background music if specified
    if background_music:
        cmd += ["-stream_loop", "-1", "-i", background_music]

    cmd += ["-t", str(duration)]

    # Filter logic
    if filter_complex:
        cmd += ["-vf", filter_complex]

    # Audio mixing
    if background_music:
        cmd += [
            "-filter_complex",
            "[0:a][1:a]amix=inputs=2:duration=shortest:dropout_transition=2",
            "-shortest"
        ]

    cmd += [
        "-c:v", "libx264",
        "-preset", "ultrafast",
        "-c:a", "aac",
        "-b:a", "192k",
        "-movflags", "+faststart",
        output_path
    ]

    logger.info(f"[FFmpeg] Rendering clip: {output_path}")
    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True) # Added capture_output
        logger.info(f"[FFmpeg] Successfully rendered clip: {output_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"[FFmpeg] Error rendering clip {output_path}.")
        logger.error(f"[FFmpeg] Command: {' '.join(cmd)}")
        logger.error(f"[FFmpeg] STDOUT: {e.stdout}")
        logger.error(f"[FFmpeg] STDERR: {e.stderr}")
        # Optionally re-raise or handle more gracefully
        raise
