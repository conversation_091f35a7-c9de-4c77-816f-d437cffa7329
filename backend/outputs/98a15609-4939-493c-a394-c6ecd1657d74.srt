1
00:00:00,080 --> 00:00:02,389

I just built this AI art factory that

2
00:00:02,389 --> 00:00:02,399
I just built this AI art factory that
 

3
00:00:02,399 --> 00:00:04,870
I just built this AI art factory that
recreated this Tik Tok content business

4
00:00:04,870 --> 00:00:04,880
recreated this Tik Tok content business
 

5
00:00:04,880 --> 00:00:07,349
recreated this Tik Tok content business
where just one of their posts reached

6
00:00:07,349 --> 00:00:07,359
where just one of their posts reached
 

7
00:00:07,359 --> 00:00:09,990
where just one of their posts reached
122 million views and others reaching

8
00:00:09,990 --> 00:00:10,000
122 million views and others reaching
 

9
00:00:10,000 --> 00:00:12,390
122 million views and others reaching
millions in reach. But this time I used

10
00:00:12,390 --> 00:00:12,400
millions in reach. But this time I used
 

11
00:00:12,400 --> 00:00:14,470
millions in reach. But this time I used
AI agents to automate the whole thing

12
00:00:14,470 --> 00:00:14,480
AI agents to automate the whole thing
 

13
00:00:14,480 --> 00:00:16,550
AI agents to automate the whole thing
and all without writing a single line of

14
00:00:16,550 --> 00:00:16,560
and all without writing a single line of
 

15
00:00:16,560 --> 00:00:19,109
and all without writing a single line of
code thanks to this no code tool which

16
00:00:19,109 --> 00:00:19,119
code thanks to this no code tool which
 

17
00:00:19,119 --> 00:00:20,870
code thanks to this no code tool which
makes it something that even a beginner

18
00:00:20,870 --> 00:00:20,880
makes it something that even a beginner
 

19
00:00:20,880 --> 00:00:22,310
makes it something that even a beginner
can do. And if you haven't seen these

20
00:00:22,310 --> 00:00:22,320
can do. And if you haven't seen these
 

21
00:00:22,320 --> 00:00:24,150
can do. And if you haven't seen these
yet, there are actually accounts on Tik

22
00:00:24,150 --> 00:00:24,160
yet, there are actually accounts on Tik
 

23
00:00:24,160 --> 00:00:26,310
yet, there are actually accounts on Tik
Tok like these that get so much reach

24
00:00:26,310 --> 00:00:26,320
Tok like these that get so much reach
 

25
00:00:26,320 --> 00:00:28,470
Tok like these that get so much reach
with this one having more than 500,000

26
00:00:28,470 --> 00:00:28,480
with this one having more than 500,000
 

27
00:00:28,480 --> 00:00:30,870
with this one having more than 500,000
followers. and all they do is post phone

28
00:00:30,870 --> 00:00:30,880
followers. and all they do is post phone
 

29
00:00:30,880 --> 00:00:33,590
followers. and all they do is post phone
wallpapers of digital art. And for this

30
00:00:33,590 --> 00:00:33,600
wallpapers of digital art. And for this
 

31
00:00:33,600 --> 00:00:35,590
wallpapers of digital art. And for this
account, their niche is anime. And

32
00:00:35,590 --> 00:00:35,600
account, their niche is anime. And
 

33
00:00:35,600 --> 00:00:37,270
account, their niche is anime. And
they're all really beautiful art, which

34
00:00:37,270 --> 00:00:37,280
they're all really beautiful art, which
 

35
00:00:37,280 --> 00:00:39,110
they're all really beautiful art, which
is no wonder that they're going viral.

36
00:00:39,110 --> 00:00:39,120
is no wonder that they're going viral.
 

37
00:00:39,120 --> 00:00:41,350
is no wonder that they're going viral.
But the thing is, with Chad GBT's latest

38
00:00:41,350 --> 00:00:41,360
But the thing is, with Chad GBT's latest
 

39
00:00:41,360 --> 00:00:43,430
But the thing is, with Chad GBT's latest
image model, which right now is the best

40
00:00:43,430 --> 00:00:43,440
image model, which right now is the best
 

41
00:00:43,440 --> 00:00:45,430
image model, which right now is the best
at image generation, you can actually

42
00:00:45,430 --> 00:00:45,440
at image generation, you can actually
 

43
00:00:45,440 --> 00:00:47,430
at image generation, you can actually
create digital wallpapers like this

44
00:00:47,430 --> 00:00:47,440
create digital wallpapers like this
 

45
00:00:47,440 --> 00:00:49,110
create digital wallpapers like this
automatically. So, these ones that I'm

46
00:00:49,110 --> 00:00:49,120
automatically. So, these ones that I'm
 

47
00:00:49,120 --> 00:00:50,869
automatically. So, these ones that I'm
showing are actually all generated by

48
00:00:50,869 --> 00:00:50,879
showing are actually all generated by
 

49
00:00:50,879 --> 00:00:52,310
showing are actually all generated by
the system that we're about to go

50
00:00:52,310 --> 00:00:52,320
the system that we're about to go
 

51
00:00:52,320 --> 00:00:53,910
the system that we're about to go
through. And if you set up this workflow

52
00:00:53,910 --> 00:00:53,920
through. And if you set up this workflow
 

53
00:00:53,920 --> 00:00:56,229
through. And if you set up this workflow
once, it will generate art for you every

54
00:00:56,229 --> 00:00:56,239
once, it will generate art for you every
 

55
00:00:56,239 --> 00:00:58,389
once, it will generate art for you every
day or even every hour if you want to

56
00:00:58,389 --> 00:00:58,399
day or even every hour if you want to
 

57
00:00:58,399 --> 00:01:00,229
day or even every hour if you want to
because every time it runs, it will

58
00:01:00,229 --> 00:01:00,239
because every time it runs, it will
 

59
00:01:00,239 --> 00:01:02,150
because every time it runs, it will
generate the prompts for those images

60
00:01:02,150 --> 00:01:02,160
generate the prompts for those images
 

61
00:01:02,160 --> 00:01:04,070
generate the prompts for those images
for you automatically. It will connect

62
00:01:04,070 --> 00:01:04,080
for you automatically. It will connect
 

63
00:01:04,080 --> 00:01:06,390
for you automatically. It will connect
to Chat GBT's image model directly to

64
00:01:06,390 --> 00:01:06,400
to Chat GBT's image model directly to
 

65
00:01:06,400 --> 00:01:08,789
to Chat GBT's image model directly to
autogenerate those wallpapers and post

66
00:01:08,789 --> 00:01:08,799
autogenerate those wallpapers and post
 

67
00:01:08,799 --> 00:01:10,870
autogenerate those wallpapers and post
to Tik Tok automatically, complete with

68
00:01:10,870 --> 00:01:10,880
to Tik Tok automatically, complete with
 

69
00:01:10,880 --> 00:01:13,030
to Tik Tok automatically, complete with
music, all running in the background.

70
00:01:13,030 --> 00:01:13,040
music, all running in the background.
 

71
00:01:13,040 --> 00:01:14,630
music, all running in the background.
And if you watch till the end, I'll also

72
00:01:14,630 --> 00:01:14,640
And if you watch till the end, I'll also
 

73
00:01:14,640 --> 00:01:16,469
And if you watch till the end, I'll also
talk about the top two ways by which you

74
00:01:16,469 --> 00:01:16,479
talk about the top two ways by which you
 

75
00:01:16,479 --> 00:01:18,469
talk about the top two ways by which you
can monetize this, which these content

76
00:01:18,469 --> 00:01:18,479
can monetize this, which these content
 

77
00:01:18,479 --> 00:01:20,630
can monetize this, which these content
businesses are already doing. So, if you

78
00:01:20,630 --> 00:01:20,640
businesses are already doing. So, if you
 

79
00:01:20,640 --> 00:01:22,390
businesses are already doing. So, if you
want to be ready for the future, it's

80
00:01:22,390 --> 00:01:22,400
want to be ready for the future, it's
 

81
00:01:22,400 --> 00:01:24,310
want to be ready for the future, it's
probably a good idea to invest some time

82
00:01:24,310 --> 00:01:24,320
probably a good idea to invest some time
 

83
00:01:24,320 --> 00:01:26,789
probably a good idea to invest some time
today to learn about AI agents, which is

84
00:01:26,789 --> 00:01:26,799
today to learn about AI agents, which is
 

85
00:01:26,799 --> 00:01:28,550
today to learn about AI agents, which is
quickly becoming a key skill to learn in

86
00:01:28,550 --> 00:01:28,560
quickly becoming a key skill to learn in
 

87
00:01:28,560 --> 00:01:30,390
quickly becoming a key skill to learn in
the next few years. And if you have the

88
00:01:30,390 --> 00:01:30,400
the next few years. And if you have the
 

89
00:01:30,400 --> 00:01:31,990
the next few years. And if you have the
time, watch till the end because you

90
00:01:31,990 --> 00:01:32,000
time, watch till the end because you
 

91
00:01:32,000 --> 00:01:33,590
time, watch till the end because you
might just learn something that will set

92
00:01:33,590 --> 00:01:33,600
might just learn something that will set
 

93
00:01:33,600 --> 00:01:36,800
might just learn something that will set
you up to be at the forefront of the AI

94
00:01:36,800 --> 00:01:36,810
you up to be at the forefront of the AI
 

95
00:01:36,810 --> 00:01:38,030
you up to be at the forefront of the AI
[Music]

96
00:01:38,030 --> 00:01:38,040
[Music]
 

97
00:01:38,040 --> 00:01:40,310
[Music]
space. By the way, if you're new here,

98
00:01:40,310 --> 00:01:40,320
space. By the way, if you're new here,
 

99
00:01:40,320 --> 00:01:42,149
space. By the way, if you're new here,
my name is Jay and our mission here at

100
00:01:42,149 --> 00:01:42,159
my name is Jay and our mission here at
 

101
00:01:42,159 --> 00:01:44,550
my name is Jay and our mission here at
RoboNuggets is to make AI easy to learn

102
00:01:44,550 --> 00:01:44,560
RoboNuggets is to make AI easy to learn
 

103
00:01:44,560 --> 00:01:46,870
RoboNuggets is to make AI easy to learn
and earn from. We run one of the largest

104
00:01:46,870 --> 00:01:46,880
and earn from. We run one of the largest
 

105
00:01:46,880 --> 00:01:48,630
and earn from. We run one of the largest
communities of AI practitioners

106
00:01:48,630 --> 00:01:48,640
communities of AI practitioners
 

107
00:01:48,640 --> 00:01:50,230
communities of AI practitioners
globally. Now we have something around

108
00:01:50,230 --> 00:01:50,240
globally. Now we have something around
 

109
00:01:50,240 --> 00:01:52,550
globally. Now we have something around
1,400 members here who are all AI

110
00:01:52,550 --> 00:01:52,560
1,400 members here who are all AI
 

111
00:01:52,560 --> 00:01:54,389
1,400 members here who are all AI
practitioners located across the globe.

112
00:01:54,389 --> 00:01:54,399
practitioners located across the globe.
 

113
00:01:54,399 --> 00:01:56,789
practitioners located across the globe.
And I also lead RoboLabs which is our AI

114
00:01:56,789 --> 00:01:56,799
And I also lead RoboLabs which is our AI
 

115
00:01:56,799 --> 00:01:58,709
And I also lead RoboLabs which is our AI
agency arm that helps bring businesses

116
00:01:58,709 --> 00:01:58,719
agency arm that helps bring businesses
 

117
00:01:58,719 --> 00:02:00,870
agency arm that helps bring businesses
to the future with AI. But enough about

118
00:02:00,870 --> 00:02:00,880
to the future with AI. But enough about
 

119
00:02:00,880 --> 00:02:02,870
to the future with AI. But enough about
myself. What we are here for is to learn

120
00:02:02,870 --> 00:02:02,880
myself. What we are here for is to learn
 

121
00:02:02,880 --> 00:02:05,190
myself. What we are here for is to learn
how to set up this AI factory. And in

122
00:02:05,190 --> 00:02:05,200
how to set up this AI factory. And in
 

123
00:02:05,200 --> 00:02:07,109
how to set up this AI factory. And in
fact to make this as easy as possible.

124
00:02:07,109 --> 00:02:07,119
fact to make this as easy as possible.
 

125
00:02:07,119 --> 00:02:08,949
fact to make this as easy as possible.
I've organized all of the prompts and

126
00:02:08,949 --> 00:02:08,959
I've organized all of the prompts and
 

127
00:02:08,959 --> 00:02:10,550
I've organized all of the prompts and
resources that you would need right here

128
00:02:10,550 --> 00:02:10,560
resources that you would need right here
 

129
00:02:10,560 --> 00:02:12,710
resources that you would need right here
in this page in our community with a

130
00:02:12,710 --> 00:02:12,720
in this page in our community with a
 

131
00:02:12,720 --> 00:02:14,229
in this page in our community with a
specific call out for this blueprint

132
00:02:14,229 --> 00:02:14,239
specific call out for this blueprint
 

133
00:02:14,239 --> 00:02:16,229
specific call out for this blueprint
file which you can just download and

134
00:02:16,229 --> 00:02:16,239
file which you can just download and
 

135
00:02:16,239 --> 00:02:18,229
file which you can just download and
import to the tool that we are using and

136
00:02:18,229 --> 00:02:18,239
import to the tool that we are using and
 

137
00:02:18,239 --> 00:02:20,309
import to the tool that we are using and
it will automatically create that

138
00:02:20,309 --> 00:02:20,319
it will automatically create that
 

139
00:02:20,319 --> 00:02:22,309
it will automatically create that
workflow for you without you even having

140
00:02:22,309 --> 00:02:22,319
workflow for you without you even having
 

141
00:02:22,319 --> 00:02:24,070
workflow for you without you even having
to lift a finger. And the framework of

142
00:02:24,070 --> 00:02:24,080
to lift a finger. And the framework of
 

143
00:02:24,080 --> 00:02:25,910
to lift a finger. And the framework of
what we'll be learning is actually very

144
00:02:25,910 --> 00:02:25,920
what we'll be learning is actually very
 

145
00:02:25,920 --> 00:02:27,510
what we'll be learning is actually very
simple because you can break it down

146
00:02:27,510 --> 00:02:27,520
simple because you can break it down
 

147
00:02:27,520 --> 00:02:29,589
simple because you can break it down
into three simple steps where in this

148
00:02:29,589 --> 00:02:29,599
into three simple steps where in this
 

149
00:02:29,599 --> 00:02:31,510
into three simple steps where in this
very first row of nodes we'll basically

150
00:02:31,510 --> 00:02:31,520
very first row of nodes we'll basically
 

151
00:02:31,520 --> 00:02:33,190
very first row of nodes we'll basically
be learning how to generate quality

152
00:02:33,190 --> 00:02:33,200
be learning how to generate quality
 

153
00:02:33,200 --> 00:02:35,110
be learning how to generate quality
prompts and then step two will be around

154
00:02:35,110 --> 00:02:35,120
prompts and then step two will be around
 

155
00:02:35,120 --> 00:02:36,869
prompts and then step two will be around
generating the images coming from those

156
00:02:36,869 --> 00:02:36,879
generating the images coming from those
 

157
00:02:36,879 --> 00:02:38,710
generating the images coming from those
prompts and then step three is setting

158
00:02:38,710 --> 00:02:38,720
prompts and then step three is setting
 

159
00:02:38,720 --> 00:02:41,110
prompts and then step three is setting
up our autopublishing step towards Tik

160
00:02:41,110 --> 00:02:41,120
up our autopublishing step towards Tik
 

161
00:02:41,120 --> 00:02:43,350
up our autopublishing step towards Tik
Tok or even other social channels which

162
00:02:43,350 --> 00:02:43,360
Tok or even other social channels which
 

163
00:02:43,360 --> 00:02:44,949
Tok or even other social channels which
are optional for you to set up. And

164
00:02:44,949 --> 00:02:44,959
are optional for you to set up. And
 

165
00:02:44,959 --> 00:02:46,550
are optional for you to set up. And
first to get started, the tool that

166
00:02:46,550 --> 00:02:46,560
first to get started, the tool that
 

167
00:02:46,560 --> 00:02:48,790
first to get started, the tool that
we'll be using is called N8N, which if

168
00:02:48,790 --> 00:02:48,800
we'll be using is called N8N, which if
 

169
00:02:48,800 --> 00:02:50,150
we'll be using is called N8N, which if
you just click on this link in our

170
00:02:50,150 --> 00:02:50,160
you just click on this link in our
 

171
00:02:50,160 --> 00:02:51,670
you just click on this link in our
community, that will take you straight

172
00:02:51,670 --> 00:02:51,680
community, that will take you straight
 

173
00:02:51,680 --> 00:02:53,670
community, that will take you straight
to N8N's page where you can sign up for

174
00:02:53,670 --> 00:02:53,680
to N8N's page where you can sign up for
 

175
00:02:53,680 --> 00:02:55,589
to N8N's page where you can sign up for
a free trial. And very quickly, if you

176
00:02:55,589 --> 00:02:55,599
a free trial. And very quickly, if you
 

177
00:02:55,599 --> 00:02:57,509
a free trial. And very quickly, if you
haven't heard of N8N before, it's kind

178
00:02:57,509 --> 00:02:57,519
haven't heard of N8N before, it's kind
 

179
00:02:57,519 --> 00:02:59,670
haven't heard of N8N before, it's kind
of similar to Zapier or Make.com, who

180
00:02:59,670 --> 00:02:59,680
of similar to Zapier or Make.com, who
 

181
00:02:59,680 --> 00:03:01,910
of similar to Zapier or Make.com, who
are all no code automation tools. But

182
00:03:01,910 --> 00:03:01,920
are all no code automation tools. But
 

183
00:03:01,920 --> 00:03:03,270
are all no code automation tools. But
you can see here in Google Trends that

184
00:03:03,270 --> 00:03:03,280
you can see here in Google Trends that
 

185
00:03:03,280 --> 00:03:05,509
you can see here in Google Trends that
the search demand for N8N has really

186
00:03:05,509 --> 00:03:05,519
the search demand for N8N has really
 

187
00:03:05,519 --> 00:03:07,430
the search demand for N8N has really
accelerated in the past weeks simply

188
00:03:07,430 --> 00:03:07,440
accelerated in the past weeks simply
 

189
00:03:07,440 --> 00:03:09,190
accelerated in the past weeks simply
because of how flexible it is with using

190
00:03:09,190 --> 00:03:09,200
because of how flexible it is with using
 

191
00:03:09,200 --> 00:03:11,030
because of how flexible it is with using
a lot of these AI systems that we'll be

192
00:03:11,030 --> 00:03:11,040
a lot of these AI systems that we'll be
 

193
00:03:11,040 --> 00:03:12,790
a lot of these AI systems that we'll be
learning. And it's also not complex as

194
00:03:12,790 --> 00:03:12,800
learning. And it's also not complex as
 

195
00:03:12,800 --> 00:03:14,309
learning. And it's also not complex as
long as you have someone to teach you

196
00:03:14,309 --> 00:03:14,319
long as you have someone to teach you
 

197
00:03:14,319 --> 00:03:16,309
long as you have someone to teach you
step by step how to operate it. So once

198
00:03:16,309 --> 00:03:16,319
step by step how to operate it. So once
 

199
00:03:16,319 --> 00:03:18,149
step by step how to operate it. So once
you've signed into N8N, you just need to

200
00:03:18,149 --> 00:03:18,159
you've signed into N8N, you just need to
 

201
00:03:18,159 --> 00:03:19,910
you've signed into N8N, you just need to
click on create workflow here at the top

202
00:03:19,910 --> 00:03:19,920
click on create workflow here at the top
 

203
00:03:19,920 --> 00:03:21,350
click on create workflow here at the top
right to get started. And then just go

204
00:03:21,350 --> 00:03:21,360
right to get started. And then just go
 

205
00:03:21,360 --> 00:03:23,509
right to get started. And then just go
ahead and rename this into whatever that

206
00:03:23,509 --> 00:03:23,519
ahead and rename this into whatever that
 

207
00:03:23,519 --> 00:03:25,350
ahead and rename this into whatever that
you want. And for your very first step,

208
00:03:25,350 --> 00:03:25,360
you want. And for your very first step,
 

209
00:03:25,360 --> 00:03:27,270
you want. And for your very first step,
you'll be setting up a trigger. And in

210
00:03:27,270 --> 00:03:27,280
you'll be setting up a trigger. And in
 

211
00:03:27,280 --> 00:03:28,949
you'll be setting up a trigger. And in
this case, we'll just do the schedule

212
00:03:28,949 --> 00:03:28,959
this case, we'll just do the schedule
 

213
00:03:28,959 --> 00:03:30,710
this case, we'll just do the schedule
trigger, which you can just find. And

214
00:03:30,710 --> 00:03:30,720
trigger, which you can just find. And
 

215
00:03:30,720 --> 00:03:32,630
trigger, which you can just find. And
let's have it trigger once every day. So

216
00:03:32,630 --> 00:03:32,640
let's have it trigger once every day. So
 

217
00:03:32,640 --> 00:03:34,070
let's have it trigger once every day. So
you can just leave that be. And the

218
00:03:34,070 --> 00:03:34,080
you can just leave that be. And the
 

219
00:03:34,080 --> 00:03:36,470
you can just leave that be. And the
trigger node is a unique node in N8N

220
00:03:36,470 --> 00:03:36,480
trigger node is a unique node in N8N
 

221
00:03:36,480 --> 00:03:37,589
trigger node is a unique node in N8N
because it's essentially going to

222
00:03:37,589 --> 00:03:37,599
because it's essentially going to
 

223
00:03:37,599 --> 00:03:39,589
because it's essentially going to
dictate how often and in which manner

224
00:03:39,589 --> 00:03:39,599
dictate how often and in which manner
 

225
00:03:39,599 --> 00:03:41,430
dictate how often and in which manner
your workflow is going to be activated.

226
00:03:41,430 --> 00:03:41,440
your workflow is going to be activated.
 

227
00:03:41,440 --> 00:03:42,550
your workflow is going to be activated.
So for this one, we'll just be

228
00:03:42,550 --> 00:03:42,560
So for this one, we'll just be
 

229
00:03:42,560 --> 00:03:44,390
So for this one, we'll just be
scheduling it to run once every day. Now

230
00:03:44,390 --> 00:03:44,400
scheduling it to run once every day. Now
 

231
00:03:44,400 --> 00:03:45,990
scheduling it to run once every day. Now
if you look at our end in my automation

232
00:03:45,990 --> 00:03:46,000
if you look at our end in my automation
 

233
00:03:46,000 --> 00:03:47,750
if you look at our end in my automation
here, which obviously I'll upload the

234
00:03:47,750 --> 00:03:47,760
here, which obviously I'll upload the
 

235
00:03:47,760 --> 00:03:49,509
here, which obviously I'll upload the
template in our community, but what I'll

236
00:03:49,509 --> 00:03:49,519
template in our community, but what I'll
 

237
00:03:49,519 --> 00:03:51,190
template in our community, but what I'll
be doing is to go through each node step

238
00:03:51,190 --> 00:03:51,200
be doing is to go through each node step
 

239
00:03:51,200 --> 00:03:53,030
be doing is to go through each node step
by step so that I can also talk through

240
00:03:53,030 --> 00:03:53,040
by step so that I can also talk through
 

241
00:03:53,040 --> 00:03:54,710
by step so that I can also talk through
some of the principles that are very

242
00:03:54,710 --> 00:03:54,720
some of the principles that are very
 

243
00:03:54,720 --> 00:03:56,229
some of the principles that are very
important to learn to set up something

244
00:03:56,229 --> 00:03:56,239
important to learn to set up something
 

245
00:03:56,239 --> 00:03:58,470
important to learn to set up something
like this. So let's get into it. Our

246
00:03:58,470 --> 00:03:58,480
like this. So let's get into it. Our
 

247
00:03:58,480 --> 00:04:00,550
like this. So let's get into it. Our
very first step here is essentially for

248
00:04:00,550 --> 00:04:00,560
very first step here is essentially for
 

249
00:04:00,560 --> 00:04:02,550
very first step here is essentially for
us to generate the prompts. Now this may

250
00:04:02,550 --> 00:04:02,560
us to generate the prompts. Now this may
 

251
00:04:02,560 --> 00:04:04,070
us to generate the prompts. Now this may
look complex but a simple way to

252
00:04:04,070 --> 00:04:04,080
look complex but a simple way to
 

253
00:04:04,080 --> 00:04:05,670
look complex but a simple way to
approach this is to always start with

254
00:04:05,670 --> 00:04:05,680
approach this is to always start with
 

255
00:04:05,680 --> 00:04:07,670
approach this is to always start with
the end in mind first and work backwards

256
00:04:07,670 --> 00:04:07,680
the end in mind first and work backwards
 

257
00:04:07,680 --> 00:04:09,350
the end in mind first and work backwards
from that to achieve the output that you

258
00:04:09,350 --> 00:04:09,360
from that to achieve the output that you
 

259
00:04:09,360 --> 00:04:11,750
from that to achieve the output that you
want. So for example with this step in

260
00:04:11,750 --> 00:04:11,760
want. So for example with this step in
 

261
00:04:11,760 --> 00:04:13,589
want. So for example with this step in
particular what we're really after is

262
00:04:13,589 --> 00:04:13,599
particular what we're really after is
 

263
00:04:13,599 --> 00:04:15,429
particular what we're really after is
this final prompt which if I just open

264
00:04:15,429 --> 00:04:15,439
this final prompt which if I just open
 

265
00:04:15,439 --> 00:04:16,870
this final prompt which if I just open
that and don't worry about all these

266
00:04:16,870 --> 00:04:16,880
that and don't worry about all these
 

267
00:04:16,880 --> 00:04:18,710
that and don't worry about all these
details for now. But if we look at this

268
00:04:18,710 --> 00:04:18,720
details for now. But if we look at this
 

269
00:04:18,720 --> 00:04:20,390
details for now. But if we look at this
section that I highlighted you can see

270
00:04:20,390 --> 00:04:20,400
section that I highlighted you can see
 

271
00:04:20,400 --> 00:04:22,710
section that I highlighted you can see
it's essentially an image prompt that is

272
00:04:22,710 --> 00:04:22,720
it's essentially an image prompt that is
 

273
00:04:22,720 --> 00:04:24,629
it's essentially an image prompt that is
quite detailed and which will be feeding

274
00:04:24,629 --> 00:04:24,639
quite detailed and which will be feeding
 

275
00:04:24,639 --> 00:04:26,710
quite detailed and which will be feeding
to our image model later on to achieve

276
00:04:26,710 --> 00:04:26,720
to our image model later on to achieve
 

277
00:04:26,720 --> 00:04:28,230
to our image model later on to achieve
the image quality that we want. Because

278
00:04:28,230 --> 00:04:28,240
the image quality that we want. Because
 

279
00:04:28,240 --> 00:04:29,830
the image quality that we want. Because
with a lot of these image models, the

280
00:04:29,830 --> 00:04:29,840
with a lot of these image models, the
 

281
00:04:29,840 --> 00:04:31,510
with a lot of these image models, the
quality always depends upon the strength

282
00:04:31,510 --> 00:04:31,520
quality always depends upon the strength
 

283
00:04:31,520 --> 00:04:33,030
quality always depends upon the strength
of the prompt that you give it. And so

284
00:04:33,030 --> 00:04:33,040
of the prompt that you give it. And so
 

285
00:04:33,040 --> 00:04:34,710
of the prompt that you give it. And so
the key is for us to continuously

286
00:04:34,710 --> 00:04:34,720
the key is for us to continuously
 

287
00:04:34,720 --> 00:04:37,030
the key is for us to continuously
generate prompts like this that are very

288
00:04:37,030 --> 00:04:37,040
generate prompts like this that are very
 

289
00:04:37,040 --> 00:04:38,950
generate prompts like this that are very
detailed and are exactly what we want.

290
00:04:38,950 --> 00:04:38,960
detailed and are exactly what we want.
 

291
00:04:38,960 --> 00:04:40,629
detailed and are exactly what we want.
And if you just break this down, there's

292
00:04:40,629 --> 00:04:40,639
And if you just break this down, there's
 

293
00:04:40,639 --> 00:04:42,310
And if you just break this down, there's
basically two elements in this prompt

294
00:04:42,310 --> 00:04:42,320
basically two elements in this prompt
 

295
00:04:42,320 --> 00:04:44,310
basically two elements in this prompt
that are important. One is this

296
00:04:44,310 --> 00:04:44,320
that are important. One is this
 

297
00:04:44,320 --> 00:04:45,909
that are important. One is this
character itself, which you can see

298
00:04:45,909 --> 00:04:45,919
character itself, which you can see
 

299
00:04:45,919 --> 00:04:47,749
character itself, which you can see
we're describing his signature look as

300
00:04:47,749 --> 00:04:47,759
we're describing his signature look as
 

301
00:04:47,759 --> 00:04:49,510
we're describing his signature look as
well in order to make sure that the

302
00:04:49,510 --> 00:04:49,520
well in order to make sure that the
 

303
00:04:49,520 --> 00:04:51,350
well in order to make sure that the
character is recognizable. And then the

304
00:04:51,350 --> 00:04:51,360
character is recognizable. And then the
 

305
00:04:51,360 --> 00:04:53,749
character is recognizable. And then the
second part is the style of that image,

306
00:04:53,749 --> 00:04:53,759
second part is the style of that image,
 

307
00:04:53,759 --> 00:04:55,350
second part is the style of that image,
which you can see here. And so since

308
00:04:55,350 --> 00:04:55,360
which you can see here. And so since
 

309
00:04:55,360 --> 00:04:57,110
which you can see here. And so since
there are two elements that we need to

310
00:04:57,110 --> 00:04:57,120
there are two elements that we need to
 

311
00:04:57,120 --> 00:04:58,950
there are two elements that we need to
figure out, it's also usually a smart

312
00:04:58,950 --> 00:04:58,960
figure out, it's also usually a smart
 

313
00:04:58,960 --> 00:05:01,030
figure out, it's also usually a smart
move to delegate the generation of those

314
00:05:01,030 --> 00:05:01,040
move to delegate the generation of those
 

315
00:05:01,040 --> 00:05:03,430
move to delegate the generation of those
two elements to two separate AI agents

316
00:05:03,430 --> 00:05:03,440
two elements to two separate AI agents
 

317
00:05:03,440 --> 00:05:05,430
two elements to two separate AI agents
instead of having just one agent try and

318
00:05:05,430 --> 00:05:05,440
instead of having just one agent try and
 

319
00:05:05,440 --> 00:05:07,029
instead of having just one agent try and
give you a quality output like this

320
00:05:07,029 --> 00:05:07,039
give you a quality output like this
 

321
00:05:07,039 --> 00:05:08,710
give you a quality output like this
every time. So if you go back to this

322
00:05:08,710 --> 00:05:08,720
every time. So if you go back to this
 

323
00:05:08,720 --> 00:05:10,790
every time. So if you go back to this
first row, you'll see that we have an AI

324
00:05:10,790 --> 00:05:10,800
first row, you'll see that we have an AI
 

325
00:05:10,800 --> 00:05:12,469
first row, you'll see that we have an AI
agent that essentially provides the

326
00:05:12,469 --> 00:05:12,479
agent that essentially provides the
 

327
00:05:12,479 --> 00:05:14,310
agent that essentially provides the
style guide for each of the images that

328
00:05:14,310 --> 00:05:14,320
style guide for each of the images that
 

329
00:05:14,320 --> 00:05:15,830
style guide for each of the images that
we'll generate for that run. We have

330
00:05:15,830 --> 00:05:15,840
we'll generate for that run. We have
 

331
00:05:15,840 --> 00:05:17,590
we'll generate for that run. We have
this character agent that will select

332
00:05:17,590 --> 00:05:17,600
this character agent that will select
 

333
00:05:17,600 --> 00:05:19,350
this character agent that will select
the characters that we'll generate that

334
00:05:19,350 --> 00:05:19,360
the characters that we'll generate that
 

335
00:05:19,360 --> 00:05:21,110
the characters that we'll generate that
style for. And then we have this third

336
00:05:21,110 --> 00:05:21,120
style for. And then we have this third
 

337
00:05:21,120 --> 00:05:22,790
style for. And then we have this third
final prompt agent which just takes

338
00:05:22,790 --> 00:05:22,800
final prompt agent which just takes
 

339
00:05:22,800 --> 00:05:24,870
final prompt agent which just takes
input of that style and that character

340
00:05:24,870 --> 00:05:24,880
input of that style and that character
 

341
00:05:24,880 --> 00:05:26,790
input of that style and that character
and brings them all together in order to

342
00:05:26,790 --> 00:05:26,800
and brings them all together in order to
 

343
00:05:26,800 --> 00:05:28,870
and brings them all together in order to
create quality image prompts like this.

344
00:05:28,870 --> 00:05:28,880
create quality image prompts like this.
 

345
00:05:28,880 --> 00:05:30,550
create quality image prompts like this.
So with that in mind, let's set these

346
00:05:30,550 --> 00:05:30,560
So with that in mind, let's set these
 

347
00:05:30,560 --> 00:05:32,469
So with that in mind, let's set these
up. And what you want to do is to just

348
00:05:32,469 --> 00:05:32,479
up. And what you want to do is to just
 

349
00:05:32,479 --> 00:05:34,710
up. And what you want to do is to just
look for the AI agent node. And that

350
00:05:34,710 --> 00:05:34,720
look for the AI agent node. And that
 

351
00:05:34,720 --> 00:05:36,629
look for the AI agent node. And that
will bring up N8N's common design of

352
00:05:36,629 --> 00:05:36,639
will bring up N8N's common design of
 

353
00:05:36,639 --> 00:05:38,390
will bring up N8N's common design of
working across all nodes, which if

354
00:05:38,390 --> 00:05:38,400
working across all nodes, which if
 

355
00:05:38,400 --> 00:05:39,990
working across all nodes, which if
you're new to N8N, it might be a good

356
00:05:39,990 --> 00:05:40,000
you're new to N8N, it might be a good
 

357
00:05:40,000 --> 00:05:41,510
you're new to N8N, it might be a good
idea to just step back and explain how

358
00:05:41,510 --> 00:05:41,520
idea to just step back and explain how
 

359
00:05:41,520 --> 00:05:43,189
idea to just step back and explain how
it all works. Because if you open any

360
00:05:43,189 --> 00:05:43,199
it all works. Because if you open any
 

361
00:05:43,199 --> 00:05:45,270
it all works. Because if you open any
node in N8N, you'll always see three

362
00:05:45,270 --> 00:05:45,280
node in N8N, you'll always see three
 

363
00:05:45,280 --> 00:05:47,670
node in N8N, you'll always see three
sections. One is this input area which

364
00:05:47,670 --> 00:05:47,680
sections. One is this input area which
 

365
00:05:47,680 --> 00:05:49,510
sections. One is this input area which
would contain data or values that are

366
00:05:49,510 --> 00:05:49,520
would contain data or values that are
 

367
00:05:49,520 --> 00:05:51,029
would contain data or values that are
coming from a previous node which are

368
00:05:51,029 --> 00:05:51,039
coming from a previous node which are
 

369
00:05:51,039 --> 00:05:52,629
coming from a previous node which are
then passed on the node that you are

370
00:05:52,629 --> 00:05:52,639
then passed on the node that you are
 

371
00:05:52,639 --> 00:05:54,310
then passed on the node that you are
configuring at that moment and this

372
00:05:54,310 --> 00:05:54,320
configuring at that moment and this
 

373
00:05:54,320 --> 00:05:56,150
configuring at that moment and this
green area is different for each and

374
00:05:56,150 --> 00:05:56,160
green area is different for each and
 

375
00:05:56,160 --> 00:05:57,990
green area is different for each and
every node mostly but that tells your

376
00:05:57,990 --> 00:05:58,000
every node mostly but that tells your
 

377
00:05:58,000 --> 00:06:00,150
every node mostly but that tells your
node what to do with those inputs so

378
00:06:00,150 --> 00:06:00,160
node what to do with those inputs so
 

379
00:06:00,160 --> 00:06:02,390
node what to do with those inputs so
that it can generate this output section

380
00:06:02,390 --> 00:06:02,400
that it can generate this output section
 

381
00:06:02,400 --> 00:06:03,909
that it can generate this output section
which are then passed along to the

382
00:06:03,909 --> 00:06:03,919
which are then passed along to the
 

383
00:06:03,919 --> 00:06:06,550
which are then passed along to the
succeeding nodes in your N8N workflow.

384
00:06:06,550 --> 00:06:06,560
succeeding nodes in your N8N workflow.
 

385
00:06:06,560 --> 00:06:08,629
succeeding nodes in your N8N workflow.
So if you go back to our AI agent is the

386
00:06:08,629 --> 00:06:08,639
So if you go back to our AI agent is the
 

387
00:06:08,639 --> 00:06:10,150
So if you go back to our AI agent is the
same thing where you can see the input

388
00:06:10,150 --> 00:06:10,160
same thing where you can see the input
 

389
00:06:10,160 --> 00:06:11,909
same thing where you can see the input
the config and the output. So to set

390
00:06:11,909 --> 00:06:11,919
the config and the output. So to set
 

391
00:06:11,919 --> 00:06:13,830
the config and the output. So to set
this up first, you just want to rename

392
00:06:13,830 --> 00:06:13,840
this up first, you just want to rename
 

393
00:06:13,840 --> 00:06:15,990
this up first, you just want to rename
it to something like style agent so that

394
00:06:15,990 --> 00:06:16,000
it to something like style agent so that
 

395
00:06:16,000 --> 00:06:17,909
it to something like style agent so that
you know what this AI agent is doing.

396
00:06:17,909 --> 00:06:17,919
you know what this AI agent is doing.
 

397
00:06:17,919 --> 00:06:19,270
you know what this AI agent is doing.
And then for the source for the prompt,

398
00:06:19,270 --> 00:06:19,280
And then for the source for the prompt,
 

399
00:06:19,280 --> 00:06:20,950
And then for the source for the prompt,
you just want to change that to define

400
00:06:20,950 --> 00:06:20,960
you just want to change that to define
 

401
00:06:20,960 --> 00:06:22,790
you just want to change that to define
below because we are not chatting to

402
00:06:22,790 --> 00:06:22,800
below because we are not chatting to
 

403
00:06:22,800 --> 00:06:24,230
below because we are not chatting to
this agent. And we'll go back to this

404
00:06:24,230 --> 00:06:24,240
this agent. And we'll go back to this
 

405
00:06:24,240 --> 00:06:25,749
this agent. And we'll go back to this
prompt in a bit, but you just want to

406
00:06:25,749 --> 00:06:25,759
prompt in a bit, but you just want to
 

407
00:06:25,759 --> 00:06:27,590
prompt in a bit, but you just want to
toggle this require specific output

408
00:06:27,590 --> 00:06:27,600
toggle this require specific output
 

409
00:06:27,600 --> 00:06:30,309
toggle this require specific output
format as on and add an option for a

410
00:06:30,309 --> 00:06:30,319
format as on and add an option for a
 

411
00:06:30,319 --> 00:06:32,230
format as on and add an option for a
system message. Now let's set up the

412
00:06:32,230 --> 00:06:32,240
system message. Now let's set up the
 

413
00:06:32,240 --> 00:06:34,469
system message. Now let's set up the
system message first because what that

414
00:06:34,469 --> 00:06:34,479
system message first because what that
 

415
00:06:34,479 --> 00:06:36,390
system message first because what that
essentially is is that this contains

416
00:06:36,390 --> 00:06:36,400
essentially is is that this contains
 

417
00:06:36,400 --> 00:06:38,950
essentially is is that this contains
instructions for your agent on what its

418
00:06:38,950 --> 00:06:38,960
instructions for your agent on what its
 

419
00:06:38,960 --> 00:06:41,110
instructions for your agent on what its
role in life is. So you can see by

420
00:06:41,110 --> 00:06:41,120
role in life is. So you can see by
 

421
00:06:41,120 --> 00:06:42,870
role in life is. So you can see by
default N8N says you are a helpful

422
00:06:42,870 --> 00:06:42,880
default N8N says you are a helpful
 

423
00:06:42,880 --> 00:06:44,309
default N8N says you are a helpful
assistant. We obviously need something

424
00:06:44,309 --> 00:06:44,319
assistant. We obviously need something
 

425
00:06:44,319 --> 00:06:45,909
assistant. We obviously need something
more robust than that. So to make it

426
00:06:45,909 --> 00:06:45,919
more robust than that. So to make it
 

427
00:06:45,919 --> 00:06:47,830
more robust than that. So to make it
easy you can just copy this system

428
00:06:47,830 --> 00:06:47,840
easy you can just copy this system
 

429
00:06:47,840 --> 00:06:49,670
easy you can just copy this system
prompt for the style agent over at our

430
00:06:49,670 --> 00:06:49,680
prompt for the style agent over at our
 

431
00:06:49,680 --> 00:06:51,029
prompt for the style agent over at our
community and then change this

432
00:06:51,029 --> 00:06:51,039
community and then change this
 

433
00:06:51,039 --> 00:06:52,950
community and then change this
expression and then paste that there. So

434
00:06:52,950 --> 00:06:52,960
expression and then paste that there. So
 

435
00:06:52,960 --> 00:06:54,710
expression and then paste that there. So
now if we open this up you can see that

436
00:06:54,710 --> 00:06:54,720
now if we open this up you can see that
 

437
00:06:54,720 --> 00:06:56,309
now if we open this up you can see that
this is just a system prompt that's a

438
00:06:56,309 --> 00:06:56,319
this is just a system prompt that's a
 

439
00:06:56,319 --> 00:06:58,150
this is just a system prompt that's a
bit more detailed. You can change this

440
00:06:58,150 --> 00:06:58,160
bit more detailed. You can change this
 

441
00:06:58,160 --> 00:06:59,749
bit more detailed. You can change this
input value at the top if you want

442
00:06:59,749 --> 00:06:59,759
input value at the top if you want
 

443
00:06:59,759 --> 00:07:01,909
input value at the top if you want
characters outside of anime for example.

444
00:07:01,909 --> 00:07:01,919
characters outside of anime for example.
 

445
00:07:01,919 --> 00:07:03,670
characters outside of anime for example.
But you can see this just provides some

446
00:07:03,670 --> 00:07:03,680
But you can see this just provides some
 

447
00:07:03,680 --> 00:07:05,990
But you can see this just provides some
instructions on what the style agent

448
00:07:05,990 --> 00:07:06,000
instructions on what the style agent
 

449
00:07:06,000 --> 00:07:07,830
instructions on what the style agent
should be doing along with the specific

450
00:07:07,830 --> 00:07:07,840
should be doing along with the specific
 

451
00:07:07,840 --> 00:07:09,990
should be doing along with the specific
output that we want it to generate. And

452
00:07:09,990 --> 00:07:10,000
output that we want it to generate. And
 

453
00:07:10,000 --> 00:07:11,589
output that we want it to generate. And
that output that we want is going to be

454
00:07:11,589 --> 00:07:11,599
that output that we want is going to be
 

455
00:07:11,599 --> 00:07:13,510
that output that we want is going to be
a randomized style that is as

456
00:07:13,510 --> 00:07:13,520
a randomized style that is as
 

457
00:07:13,520 --> 00:07:15,350
a randomized style that is as
descriptive as possible. And so we

458
00:07:15,350 --> 00:07:15,360
descriptive as possible. And so we
 

459
00:07:15,360 --> 00:07:17,189
descriptive as possible. And so we
provided some instructions here. But we

460
00:07:17,189 --> 00:07:17,199
provided some instructions here. But we
 

461
00:07:17,199 --> 00:07:19,430
provided some instructions here. But we
also ask some more defining values such

462
00:07:19,430 --> 00:07:19,440
also ask some more defining values such
 

463
00:07:19,440 --> 00:07:21,270
also ask some more defining values such
as where the character is placed, what's

464
00:07:21,270 --> 00:07:21,280
as where the character is placed, what's
 

465
00:07:21,280 --> 00:07:23,110
as where the character is placed, what's
the size of the character relative to

466
00:07:23,110 --> 00:07:23,120
the size of the character relative to
 

467
00:07:23,120 --> 00:07:25,270
the size of the character relative to
the frame of the whole image and a

468
00:07:25,270 --> 00:07:25,280
the frame of the whole image and a
 

469
00:07:25,280 --> 00:07:27,110
the frame of the whole image and a
couple of other enriching values here.

470
00:07:27,110 --> 00:07:27,120
couple of other enriching values here.
 

471
00:07:27,120 --> 00:07:29,029
couple of other enriching values here.
But you can also see here that we are

472
00:07:29,029 --> 00:07:29,039
But you can also see here that we are
 

473
00:07:29,039 --> 00:07:31,110
But you can also see here that we are
randomly selecting one style from the

474
00:07:31,110 --> 00:07:31,120
randomly selecting one style from the
 

475
00:07:31,120 --> 00:07:33,430
randomly selecting one style from the
curated list of style profiles given

476
00:07:33,430 --> 00:07:33,440
curated list of style profiles given
 

477
00:07:33,440 --> 00:07:35,189
curated list of style profiles given
below. Which if we scroll through that

478
00:07:35,189 --> 00:07:35,199
below. Which if we scroll through that
 

479
00:07:35,199 --> 00:07:36,710
below. Which if we scroll through that
what I actually have here is a

480
00:07:36,710 --> 00:07:36,720
what I actually have here is a
 

481
00:07:36,720 --> 00:07:39,350
what I actually have here is a
pre-filled list of 50 styles that this

482
00:07:39,350 --> 00:07:39,360
pre-filled list of 50 styles that this
 

483
00:07:39,360 --> 00:07:41,189
pre-filled list of 50 styles that this
agent will be choosing from each time.

484
00:07:41,189 --> 00:07:41,199
agent will be choosing from each time.
 

485
00:07:41,199 --> 00:07:42,629
agent will be choosing from each time.
This is just important to call out so

486
00:07:42,629 --> 00:07:42,639
This is just important to call out so
 

487
00:07:42,639 --> 00:07:44,550
This is just important to call out so
that if you want to revise it to styles

488
00:07:44,550 --> 00:07:44,560
that if you want to revise it to styles
 

489
00:07:44,560 --> 00:07:45,990
that if you want to revise it to styles
that you want to see then you can just

490
00:07:45,990 --> 00:07:46,000
that you want to see then you can just
 

491
00:07:46,000 --> 00:07:47,749
that you want to see then you can just
edit this however you want so that your

492
00:07:47,749 --> 00:07:47,759
edit this however you want so that your
 

493
00:07:47,759 --> 00:07:49,749
edit this however you want so that your
style agent is guided on what are the

494
00:07:49,749 --> 00:07:49,759
style agent is guided on what are the
 

495
00:07:49,759 --> 00:07:51,510
style agent is guided on what are the
options that it has. But here at the

496
00:07:51,510 --> 00:07:51,520
options that it has. But here at the
 

497
00:07:51,520 --> 00:07:53,350
options that it has. But here at the
bottom, we always have like a generate

498
00:07:53,350 --> 00:07:53,360
bottom, we always have like a generate
 

499
00:07:53,360 --> 00:07:55,909
bottom, we always have like a generate
your own style and a note for it to be

500
00:07:55,909 --> 00:07:55,919
your own style and a note for it to be
 

501
00:07:55,919 --> 00:07:57,510
your own style and a note for it to be
bold and diverse so that it's not

502
00:07:57,510 --> 00:07:57,520
bold and diverse so that it's not
 

503
00:07:57,520 --> 00:07:59,110
bold and diverse so that it's not
completely limited to just these styles

504
00:07:59,110 --> 00:07:59,120
completely limited to just these styles
 

505
00:07:59,120 --> 00:08:01,029
completely limited to just these styles
that we have provided. But overall, it's

506
00:08:01,029 --> 00:08:01,039
that we have provided. But overall, it's
 

507
00:08:01,039 --> 00:08:02,629
that we have provided. But overall, it's
just good to have some options here so

508
00:08:02,629 --> 00:08:02,639
just good to have some options here so
 

509
00:08:02,639 --> 00:08:04,230
just good to have some options here so
that it's more guided on what to

510
00:08:04,230 --> 00:08:04,240
that it's more guided on what to
 

511
00:08:04,240 --> 00:08:05,589
that it's more guided on what to
generate. And so with that system

512
00:08:05,589 --> 00:08:05,599
generate. And so with that system
 

513
00:08:05,599 --> 00:08:07,270
generate. And so with that system
message instructing our style agent what

514
00:08:07,270 --> 00:08:07,280
message instructing our style agent what
 

515
00:08:07,280 --> 00:08:09,189
message instructing our style agent what
to do. This user message will

516
00:08:09,189 --> 00:08:09,199
to do. This user message will
 

517
00:08:09,199 --> 00:08:11,430
to do. This user message will
essentially be just your message to the

518
00:08:11,430 --> 00:08:11,440
essentially be just your message to the
 

519
00:08:11,440 --> 00:08:12,950
essentially be just your message to the
agent the same way that you would talk

520
00:08:12,950 --> 00:08:12,960
agent the same way that you would talk
 

521
00:08:12,960 --> 00:08:15,029
agent the same way that you would talk
to chat GPT for example. So if I just

522
00:08:15,029 --> 00:08:15,039
to chat GPT for example. So if I just
 

523
00:08:15,039 --> 00:08:16,710
to chat GPT for example. So if I just
type in generate a visual style there,

524
00:08:16,710 --> 00:08:16,720
type in generate a visual style there,
 

525
00:08:16,720 --> 00:08:18,390
type in generate a visual style there,
then that should be enough. So if you

526
00:08:18,390 --> 00:08:18,400
then that should be enough. So if you
 

527
00:08:18,400 --> 00:08:20,309
then that should be enough. So if you
click test step here, it will actually

528
00:08:20,309 --> 00:08:20,319
click test step here, it will actually
 

529
00:08:20,319 --> 00:08:22,230
click test step here, it will actually
cause an error still because it's

530
00:08:22,230 --> 00:08:22,240
cause an error still because it's
 

531
00:08:22,240 --> 00:08:23,990
cause an error still because it's
telling you that we still need to set up

532
00:08:23,990 --> 00:08:24,000
telling you that we still need to set up
 

533
00:08:24,000 --> 00:08:26,629
telling you that we still need to set up
our AI model connected to this agent. So

534
00:08:26,629 --> 00:08:26,639
our AI model connected to this agent. So
 

535
00:08:26,639 --> 00:08:28,469
our AI model connected to this agent. So
if we just click away, you'll see that

536
00:08:28,469 --> 00:08:28,479
if we just click away, you'll see that
 

537
00:08:28,479 --> 00:08:30,150
if we just click away, you'll see that
you have some subnodes here and you can

538
00:08:30,150 --> 00:08:30,160
you have some subnodes here and you can
 

539
00:08:30,160 --> 00:08:32,070
you have some subnodes here and you can
see chat model here as well, which you

540
00:08:32,070 --> 00:08:32,080
see chat model here as well, which you
 

541
00:08:32,080 --> 00:08:33,509
see chat model here as well, which you
can just click. And if you open that,

542
00:08:33,509 --> 00:08:33,519
can just click. And if you open that,
 

543
00:08:33,519 --> 00:08:35,029
can just click. And if you open that,
you can see some familiar names here.

544
00:08:35,029 --> 00:08:35,039
you can see some familiar names here.
 

545
00:08:35,039 --> 00:08:37,110
you can see some familiar names here.
These are basically all language models.

546
00:08:37,110 --> 00:08:37,120
These are basically all language models.
 

547
00:08:37,120 --> 00:08:38,550
These are basically all language models.
And the one that we'll use for this

548
00:08:38,550 --> 00:08:38,560
And the one that we'll use for this
 

549
00:08:38,560 --> 00:08:40,389
And the one that we'll use for this
exercise is OpenAI since that's the one

550
00:08:40,389 --> 00:08:40,399
exercise is OpenAI since that's the one
 

551
00:08:40,399 --> 00:08:42,070
exercise is OpenAI since that's the one
that's most common. And once that's up,

552
00:08:42,070 --> 00:08:42,080
that's most common. And once that's up,
 

553
00:08:42,080 --> 00:08:43,670
that's most common. And once that's up,
all you really need to do to set this up

554
00:08:43,670 --> 00:08:43,680
all you really need to do to set this up
 

555
00:08:43,680 --> 00:08:45,670
all you really need to do to set this up
is to connect your credentials, which if

556
00:08:45,670 --> 00:08:45,680
is to connect your credentials, which if
 

557
00:08:45,680 --> 00:08:47,190
is to connect your credentials, which if
you haven't done that before, just click

558
00:08:47,190 --> 00:08:47,200
you haven't done that before, just click
 

559
00:08:47,200 --> 00:08:48,630
you haven't done that before, just click
on create new credential here. And

560
00:08:48,630 --> 00:08:48,640
on create new credential here. And
 

561
00:08:48,640 --> 00:08:50,790
on create new credential here. And
you'll see that it is asking for an API

562
00:08:50,790 --> 00:08:50,800
you'll see that it is asking for an API
 

563
00:08:50,800 --> 00:08:52,550
you'll see that it is asking for an API
key, which if you go to our community,

564
00:08:52,550 --> 00:08:52,560
key, which if you go to our community,
 

565
00:08:52,560 --> 00:08:55,269
key, which if you go to our community,
you can just go to this URL from OpenAI.

566
00:08:55,269 --> 00:08:55,279
you can just go to this URL from OpenAI.
 

567
00:08:55,279 --> 00:08:57,030
you can just go to this URL from OpenAI.
And this is where you can create a new

568
00:08:57,030 --> 00:08:57,040
And this is where you can create a new
 

569
00:08:57,040 --> 00:08:58,550
And this is where you can create a new
key, which you can just do by clicking

570
00:08:58,550 --> 00:08:58,560
key, which you can just do by clicking
 

571
00:08:58,560 --> 00:09:00,150
key, which you can just do by clicking
here at the top right. So once you paste

572
00:09:00,150 --> 00:09:00,160
here at the top right. So once you paste
 

573
00:09:00,160 --> 00:09:01,430
here at the top right. So once you paste
it there, you should now have your

574
00:09:01,430 --> 00:09:01,440
it there, you should now have your
 

575
00:09:01,440 --> 00:09:03,590
it there, you should now have your
OpenAI account set up. And for the model

576
00:09:03,590 --> 00:09:03,600
OpenAI account set up. And for the model
 

577
00:09:03,600 --> 00:09:05,190
OpenAI account set up. And for the model
at least, you can choose whichever, but

578
00:09:05,190 --> 00:09:05,200
at least, you can choose whichever, but
 

579
00:09:05,200 --> 00:09:07,509
at least, you can choose whichever, but
at the time of this tutorial, 4.1 is the

580
00:09:07,509 --> 00:09:07,519
at the time of this tutorial, 4.1 is the
 

581
00:09:07,519 --> 00:09:08,949
at the time of this tutorial, 4.1 is the
best. So we'll be using that. And then

582
00:09:08,949 --> 00:09:08,959
best. So we'll be using that. And then
 

583
00:09:08,959 --> 00:09:10,550
best. So we'll be using that. And then
just click away. And you can see that we

584
00:09:10,550 --> 00:09:10,560
just click away. And you can see that we
 

585
00:09:10,560 --> 00:09:12,949
just click away. And you can see that we
have the OpenAI chat model now connected

586
00:09:12,949 --> 00:09:12,959
have the OpenAI chat model now connected
 

587
00:09:12,959 --> 00:09:14,870
have the OpenAI chat model now connected
to that agent. Now with these sub nodes,

588
00:09:14,870 --> 00:09:14,880
to that agent. Now with these sub nodes,
 

589
00:09:14,880 --> 00:09:16,230
to that agent. Now with these sub nodes,
there's just two more things to set up.

590
00:09:16,230 --> 00:09:16,240
there's just two more things to set up.
 

591
00:09:16,240 --> 00:09:17,990
there's just two more things to set up.
And one of them is this tool which you

592
00:09:17,990 --> 00:09:18,000
And one of them is this tool which you
 

593
00:09:18,000 --> 00:09:20,230
And one of them is this tool which you
can click here called the think node

594
00:09:20,230 --> 00:09:20,240
can click here called the think node
 

595
00:09:20,240 --> 00:09:21,829
can click here called the think node
which is always just good to add so that

596
00:09:21,829 --> 00:09:21,839
which is always just good to add so that
 

597
00:09:21,839 --> 00:09:23,590
which is always just good to add so that
your AI agent can think about its

598
00:09:23,590 --> 00:09:23,600
your AI agent can think about its
 

599
00:09:23,600 --> 00:09:26,070
your AI agent can think about its
response first before giving you its

600
00:09:26,070 --> 00:09:26,080
response first before giving you its
 

601
00:09:26,080 --> 00:09:27,910
response first before giving you its
output. So with AI models now that's

602
00:09:27,910 --> 00:09:27,920
output. So with AI models now that's
 

603
00:09:27,920 --> 00:09:29,670
output. So with AI models now that's
becoming more of a practice in order to

604
00:09:29,670 --> 00:09:29,680
becoming more of a practice in order to
 

605
00:09:29,680 --> 00:09:31,190
becoming more of a practice in order to
make them smarter. So if you just add

606
00:09:31,190 --> 00:09:31,200
make them smarter. So if you just add
 

607
00:09:31,200 --> 00:09:32,550
make them smarter. So if you just add
that, you don't even need to change

608
00:09:32,550 --> 00:09:32,560
that, you don't even need to change
 

609
00:09:32,560 --> 00:09:34,230
that, you don't even need to change
anything here and you'll be able to

610
00:09:34,230 --> 00:09:34,240
anything here and you'll be able to
 

611
00:09:34,240 --> 00:09:36,230
anything here and you'll be able to
connect that to your AI agent properly.

612
00:09:36,230 --> 00:09:36,240
connect that to your AI agent properly.
 

613
00:09:36,240 --> 00:09:38,070
connect that to your AI agent properly.
And then finally, you would need this

614
00:09:38,070 --> 00:09:38,080
And then finally, you would need this
 

615
00:09:38,080 --> 00:09:40,310
And then finally, you would need this
output parser, which to recap, if we

616
00:09:40,310 --> 00:09:40,320
output parser, which to recap, if we
 

617
00:09:40,320 --> 00:09:42,150
output parser, which to recap, if we
open up our style agent again, you can

618
00:09:42,150 --> 00:09:42,160
open up our style agent again, you can
 

619
00:09:42,160 --> 00:09:43,910
open up our style agent again, you can
see that we are requiring a specific

620
00:09:43,910 --> 00:09:43,920
see that we are requiring a specific
 

621
00:09:43,920 --> 00:09:45,670
see that we are requiring a specific
output format from it. And again, this

622
00:09:45,670 --> 00:09:45,680
output format from it. And again, this
 

623
00:09:45,680 --> 00:09:47,350
output format from it. And again, this
is just good practice in order to

624
00:09:47,350 --> 00:09:47,360
is just good practice in order to
 

625
00:09:47,360 --> 00:09:49,030
is just good practice in order to
control what your AI agent will be

626
00:09:49,030 --> 00:09:49,040
control what your AI agent will be
 

627
00:09:49,040 --> 00:09:51,030
control what your AI agent will be
providing you each time. So, if we just

628
00:09:51,030 --> 00:09:51,040
providing you each time. So, if we just
 

629
00:09:51,040 --> 00:09:53,030
providing you each time. So, if we just
click that and look for structured

630
00:09:53,030 --> 00:09:53,040
click that and look for structured
 

631
00:09:53,040 --> 00:09:55,030
click that and look for structured
output parser, you'll see one field here

632
00:09:55,030 --> 00:09:55,040
output parser, you'll see one field here
 

633
00:09:55,040 --> 00:09:57,910
output parser, you'll see one field here
that asks for a JSON example. And JSON

634
00:09:57,910 --> 00:09:57,920
that asks for a JSON example. And JSON
 

635
00:09:57,920 --> 00:09:59,430
that asks for a JSON example. And JSON
just stands for JavaScript object

636
00:09:59,430 --> 00:09:59,440
just stands for JavaScript object
 

637
00:09:59,440 --> 00:10:01,030
just stands for JavaScript object
notation. And whenever you see these

638
00:10:01,030 --> 00:10:01,040
notation. And whenever you see these
 

639
00:10:01,040 --> 00:10:03,110
notation. And whenever you see these
braces and brackets, that's essentially

640
00:10:03,110 --> 00:10:03,120
braces and brackets, that's essentially
 

641
00:10:03,120 --> 00:10:04,630
braces and brackets, that's essentially
what it is. It's just a standardized

642
00:10:04,630 --> 00:10:04,640
what it is. It's just a standardized
 

643
00:10:04,640 --> 00:10:06,710
what it is. It's just a standardized
format by which a lot of AI models and

644
00:10:06,710 --> 00:10:06,720
format by which a lot of AI models and
 

645
00:10:06,720 --> 00:10:08,550
format by which a lot of AI models and
computers communicate. But again to make

646
00:10:08,550 --> 00:10:08,560
computers communicate. But again to make
 

647
00:10:08,560 --> 00:10:10,150
computers communicate. But again to make
it easy, if you just go to our

648
00:10:10,150 --> 00:10:10,160
it easy, if you just go to our
 

649
00:10:10,160 --> 00:10:11,750
it easy, if you just go to our
community, you can just copy this whole

650
00:10:11,750 --> 00:10:11,760
community, you can just copy this whole
 

651
00:10:11,760 --> 00:10:13,670
community, you can just copy this whole
thing and paste it here. So now the

652
00:10:13,670 --> 00:10:13,680
thing and paste it here. So now the
 

653
00:10:13,680 --> 00:10:16,310
thing and paste it here. So now the
output of our agent will always align to

654
00:10:16,310 --> 00:10:16,320
output of our agent will always align to
 

655
00:10:16,320 --> 00:10:18,710
output of our agent will always align to
this format which if we click away and

656
00:10:18,710 --> 00:10:18,720
this format which if we click away and
 

657
00:10:18,720 --> 00:10:20,389
this format which if we click away and
just click on our style agent again. You

658
00:10:20,389 --> 00:10:20,399
just click on our style agent again. You
 

659
00:10:20,399 --> 00:10:22,310
just click on our style agent again. You
can just click on test step and under

660
00:10:22,310 --> 00:10:22,320
can just click on test step and under
 

661
00:10:22,320 --> 00:10:24,550
can just click on test step and under
output you can see that it generated for

662
00:10:24,550 --> 00:10:24,560
output you can see that it generated for
 

663
00:10:24,560 --> 00:10:27,030
output you can see that it generated for
us one style idea. So this one is an

664
00:10:27,030 --> 00:10:27,040
us one style idea. So this one is an
 

665
00:10:27,040 --> 00:10:29,590
us one style idea. So this one is an
origami paper worlds idea which is

666
00:10:29,590 --> 00:10:29,600
origami paper worlds idea which is
 

667
00:10:29,600 --> 00:10:30,949
origami paper worlds idea which is
pretty interesting. And so it selected

668
00:10:30,949 --> 00:10:30,959
pretty interesting. And so it selected
 

669
00:10:30,959 --> 00:10:32,710
pretty interesting. And so it selected
this style most likely coming from that

670
00:10:32,710 --> 00:10:32,720
this style most likely coming from that
 

671
00:10:32,720 --> 00:10:34,790
this style most likely coming from that
list of 50 that I had earlier as well as

672
00:10:34,790 --> 00:10:34,800
list of 50 that I had earlier as well as
 

673
00:10:34,800 --> 00:10:37,430
list of 50 that I had earlier as well as
a few more guiding attributes for the

674
00:10:37,430 --> 00:10:37,440
a few more guiding attributes for the
 

675
00:10:37,440 --> 00:10:39,030
a few more guiding attributes for the
succeeding models. And if you look at

676
00:10:39,030 --> 00:10:39,040
succeeding models. And if you look at
 

677
00:10:39,040 --> 00:10:40,949
succeeding models. And if you look at
schema, the reason why this output is so

678
00:10:40,949 --> 00:10:40,959
schema, the reason why this output is so
 

679
00:10:40,959 --> 00:10:42,949
schema, the reason why this output is so
organized is because we provided it that

680
00:10:42,949 --> 00:10:42,959
organized is because we provided it that
 

681
00:10:42,959 --> 00:10:44,870
organized is because we provided it that
structured output parser which we just

682
00:10:44,870 --> 00:10:44,880
structured output parser which we just
 

683
00:10:44,880 --> 00:10:46,470
structured output parser which we just
set up. But there that is your very

684
00:10:46,470 --> 00:10:46,480
set up. But there that is your very
 

685
00:10:46,480 --> 00:10:48,790
set up. But there that is your very
first AI agent now done. And so the two

686
00:10:48,790 --> 00:10:48,800
first AI agent now done. And so the two
 

687
00:10:48,800 --> 00:10:50,470
first AI agent now done. And so the two
succeeding agents will now be much

688
00:10:50,470 --> 00:10:50,480
succeeding agents will now be much
 

689
00:10:50,480 --> 00:10:52,230
succeeding agents will now be much
simpler to set up because all you need

690
00:10:52,230 --> 00:10:52,240
simpler to set up because all you need
 

691
00:10:52,240 --> 00:10:54,230
simpler to set up because all you need
to do is to just select this and hit

692
00:10:54,230 --> 00:10:54,240
to do is to just select this and hit
 

693
00:10:54,240 --> 00:10:56,069
to do is to just select this and hit
Ctrl D if you want to duplicate that.

694
00:10:56,069 --> 00:10:56,079
Ctrl D if you want to duplicate that.
 

695
00:10:56,079 --> 00:10:57,750
Ctrl D if you want to duplicate that.
And to easily set this up, you just

696
00:10:57,750 --> 00:10:57,760
And to easily set this up, you just
 

697
00:10:57,760 --> 00:10:59,269
And to easily set this up, you just
connect it like this. And for this

698
00:10:59,269 --> 00:10:59,279
connect it like this. And for this
 

699
00:10:59,279 --> 00:11:00,949
connect it like this. And for this
agent's chat model, we'll be using the

700
00:11:00,949 --> 00:11:00,959
agent's chat model, we'll be using the
 

701
00:11:00,959 --> 00:11:02,630
agent's chat model, we'll be using the
same one. So you can just connect it to

702
00:11:02,630 --> 00:11:02,640
same one. So you can just connect it to
 

703
00:11:02,640 --> 00:11:04,470
same one. So you can just connect it to
the same chat model that we had. And we

704
00:11:04,470 --> 00:11:04,480
the same chat model that we had. And we
 

705
00:11:04,480 --> 00:11:06,310
the same chat model that we had. And we
also want it to get access to the think

706
00:11:06,310 --> 00:11:06,320
also want it to get access to the think
 

707
00:11:06,320 --> 00:11:07,590
also want it to get access to the think
tool. So we can just connect it like

708
00:11:07,590 --> 00:11:07,600
tool. So we can just connect it like
 

709
00:11:07,600 --> 00:11:09,910
tool. So we can just connect it like
that as well. Now if we open this now

710
00:11:09,910 --> 00:11:09,920
that as well. Now if we open this now
 

711
00:11:09,920 --> 00:11:13,030
that as well. Now if we open this now
will be our character agent. So its job

712
00:11:13,030 --> 00:11:13,040
will be our character agent. So its job
 

713
00:11:13,040 --> 00:11:15,509
will be our character agent. So its job
is to generate three characters

714
00:11:15,509 --> 00:11:15,519
is to generate three characters
 

715
00:11:15,519 --> 00:11:17,190
is to generate three characters
corresponding to three images that we'll

716
00:11:17,190 --> 00:11:17,200
corresponding to three images that we'll
 

717
00:11:17,200 --> 00:11:18,949
corresponding to three images that we'll
be generating that are aligned to our

718
00:11:18,949 --> 00:11:18,959
be generating that are aligned to our
 

719
00:11:18,959 --> 00:11:20,630
be generating that are aligned to our
niche of choice which in this case it

720
00:11:20,630 --> 00:11:20,640
niche of choice which in this case it
 

721
00:11:20,640 --> 00:11:22,310
niche of choice which in this case it
will be anime characters as we

722
00:11:22,310 --> 00:11:22,320
will be anime characters as we
 

723
00:11:22,320 --> 00:11:23,670
will be anime characters as we
discussed. So the only thing you need to

724
00:11:23,670 --> 00:11:23,680
discussed. So the only thing you need to
 

725
00:11:23,680 --> 00:11:25,829
discussed. So the only thing you need to
change here is this system message which

726
00:11:25,829 --> 00:11:25,839
change here is this system message which
 

727
00:11:25,839 --> 00:11:27,190
change here is this system message which
you can just delete this whole thing.

728
00:11:27,190 --> 00:11:27,200
you can just delete this whole thing.
 

729
00:11:27,200 --> 00:11:28,870
you can just delete this whole thing.
And again to make it easy, you can just

730
00:11:28,870 --> 00:11:28,880
And again to make it easy, you can just
 

731
00:11:28,880 --> 00:11:30,790
And again to make it easy, you can just
copy the system prompt here in our

732
00:11:30,790 --> 00:11:30,800
copy the system prompt here in our
 

733
00:11:30,800 --> 00:11:32,710
copy the system prompt here in our
community for that character agent. So

734
00:11:32,710 --> 00:11:32,720
community for that character agent. So
 

735
00:11:32,720 --> 00:11:35,030
community for that character agent. So
if I paste it there and if we open that

736
00:11:35,030 --> 00:11:35,040
if I paste it there and if we open that
 

737
00:11:35,040 --> 00:11:36,630
if I paste it there and if we open that
up, you'll see the same thing where you

738
00:11:36,630 --> 00:11:36,640
up, you'll see the same thing where you
 

739
00:11:36,640 --> 00:11:38,150
up, you'll see the same thing where you
can just change this niche at the top

740
00:11:38,150 --> 00:11:38,160
can just change this niche at the top
 

741
00:11:38,160 --> 00:11:39,750
can just change this niche at the top
and you can read this in your own time.

742
00:11:39,750 --> 00:11:39,760
and you can read this in your own time.
 

743
00:11:39,760 --> 00:11:41,269
and you can read this in your own time.
But what's important here is this

744
00:11:41,269 --> 00:11:41,279
But what's important here is this
 

745
00:11:41,279 --> 00:11:42,949
But what's important here is this
inclusion list which you can change

746
00:11:42,949 --> 00:11:42,959
inclusion list which you can change
 

747
00:11:42,959 --> 00:11:44,550
inclusion list which you can change
depending on what you want. Basically I

748
00:11:44,550 --> 00:11:44,560
depending on what you want. Basically I
 

749
00:11:44,560 --> 00:11:46,550
depending on what you want. Basically I
just asked Chad GPT for some anime

750
00:11:46,550 --> 00:11:46,560
just asked Chad GPT for some anime
 

751
00:11:46,560 --> 00:11:48,310
just asked Chad GPT for some anime
characters to pre-populate here and

752
00:11:48,310 --> 00:11:48,320
characters to pre-populate here and
 

753
00:11:48,320 --> 00:11:50,069
characters to pre-populate here and
basically what this agent's job is to

754
00:11:50,069 --> 00:11:50,079
basically what this agent's job is to
 

755
00:11:50,079 --> 00:11:52,150
basically what this agent's job is to
get characters from this list in random

756
00:11:52,150 --> 00:11:52,160
get characters from this list in random
 

757
00:11:52,160 --> 00:11:53,509
get characters from this list in random
whenever it's run. And then to

758
00:11:53,509 --> 00:11:53,519
whenever it's run. And then to
 

759
00:11:53,519 --> 00:11:55,110
whenever it's run. And then to
streamline its output some more, we also

760
00:11:55,110 --> 00:11:55,120
streamline its output some more, we also
 

761
00:11:55,120 --> 00:11:56,949
streamline its output some more, we also
have like an exclusion list here so that

762
00:11:56,949 --> 00:11:56,959
have like an exclusion list here so that
 

763
00:11:56,959 --> 00:11:58,630
have like an exclusion list here so that
it knows what type of characters to

764
00:11:58,630 --> 00:11:58,640
it knows what type of characters to
 

765
00:11:58,640 --> 00:12:00,389
it knows what type of characters to
avoid. And then we have a few more

766
00:12:00,389 --> 00:12:00,399
avoid. And then we have a few more
 

767
00:12:00,399 --> 00:12:02,069
avoid. And then we have a few more
details here just to guide it on the

768
00:12:02,069 --> 00:12:02,079
details here just to guide it on the
 

769
00:12:02,079 --> 00:12:03,829
details here just to guide it on the
type of output that we want. You can see

770
00:12:03,829 --> 00:12:03,839
type of output that we want. You can see
 

771
00:12:03,839 --> 00:12:05,910
type of output that we want. You can see
that we also want it to be guided on

772
00:12:05,910 --> 00:12:05,920
that we also want it to be guided on
 

773
00:12:05,920 --> 00:12:07,509
that we also want it to be guided on
where that character is from, what type

774
00:12:07,509 --> 00:12:07,519
where that character is from, what type
 

775
00:12:07,519 --> 00:12:09,110
where that character is from, what type
of clothing they usually wear, as well

776
00:12:09,110 --> 00:12:09,120
of clothing they usually wear, as well
 

777
00:12:09,120 --> 00:12:10,870
of clothing they usually wear, as well
as the color scheme that suits that

778
00:12:10,870 --> 00:12:10,880
as the color scheme that suits that
 

779
00:12:10,880 --> 00:12:12,230
as the color scheme that suits that
character. So that's something that you

780
00:12:12,230 --> 00:12:12,240
character. So that's something that you
 

781
00:12:12,240 --> 00:12:13,829
character. So that's something that you
can edit in your own time. But for the

782
00:12:13,829 --> 00:12:13,839
can edit in your own time. But for the
 

783
00:12:13,839 --> 00:12:15,350
can edit in your own time. But for the
prompt here, you can just give it a

784
00:12:15,350 --> 00:12:15,360
prompt here, you can just give it a
 

785
00:12:15,360 --> 00:12:16,949
prompt here, you can just give it a
prompt similar to this one. So if I just

786
00:12:16,949 --> 00:12:16,959
prompt similar to this one. So if I just
 

787
00:12:16,959 --> 00:12:18,629
prompt similar to this one. So if I just
copy that and paste it here, then that

788
00:12:18,629 --> 00:12:18,639
copy that and paste it here, then that
 

789
00:12:18,639 --> 00:12:20,069
copy that and paste it here, then that
would just instruct this agent to

790
00:12:20,069 --> 00:12:20,079
would just instruct this agent to
 

791
00:12:20,079 --> 00:12:22,470
would just instruct this agent to
generate three random character ids from

792
00:12:22,470 --> 00:12:22,480
generate three random character ids from
 

793
00:12:22,480 --> 00:12:24,710
generate three random character ids from
our list below. So before you hit test

794
00:12:24,710 --> 00:12:24,720
our list below. So before you hit test
 

795
00:12:24,720 --> 00:12:26,629
our list below. So before you hit test
step, it's also just good to give this

796
00:12:26,629 --> 00:12:26,639
step, it's also just good to give this
 

797
00:12:26,639 --> 00:12:28,629
step, it's also just good to give this
an output parser as well. So if I just

798
00:12:28,629 --> 00:12:28,639
an output parser as well. So if I just
 

799
00:12:28,639 --> 00:12:30,150
an output parser as well. So if I just
click on that and click on structured

800
00:12:30,150 --> 00:12:30,160
click on that and click on structured
 

801
00:12:30,160 --> 00:12:32,150
click on that and click on structured
output parser, you can just copy this

802
00:12:32,150 --> 00:12:32,160
output parser, you can just copy this
 

803
00:12:32,160 --> 00:12:34,389
output parser, you can just copy this
example from our community and paste

804
00:12:34,389 --> 00:12:34,399
example from our community and paste
 

805
00:12:34,399 --> 00:12:36,310
example from our community and paste
that there. And you can see that this is

806
00:12:36,310 --> 00:12:36,320
that there. And you can see that this is
 

807
00:12:36,320 --> 00:12:38,710
that there. And you can see that this is
just again an example of what good looks

808
00:12:38,710 --> 00:12:38,720
just again an example of what good looks
 

809
00:12:38,720 --> 00:12:40,870
just again an example of what good looks
like so that the agent is guided. So now

810
00:12:40,870 --> 00:12:40,880
like so that the agent is guided. So now
 

811
00:12:40,880 --> 00:12:42,949
like so that the agent is guided. So now
that we can click test step, let's just

812
00:12:42,949 --> 00:12:42,959
that we can click test step, let's just
 

813
00:12:42,959 --> 00:12:44,470
that we can click test step, let's just
go ahead and do that. You'll see that

814
00:12:44,470 --> 00:12:44,480
go ahead and do that. You'll see that
 

815
00:12:44,480 --> 00:12:46,550
go ahead and do that. You'll see that
the agent was able to generate for us

816
00:12:46,550 --> 00:12:46,560
the agent was able to generate for us
 

817
00:12:46,560 --> 00:12:48,629
the agent was able to generate for us
three characters along with the shows

818
00:12:48,629 --> 00:12:48,639
three characters along with the shows
 

819
00:12:48,639 --> 00:12:50,069
three characters along with the shows
that they're from as well as a

820
00:12:50,069 --> 00:12:50,079
that they're from as well as a
 

821
00:12:50,079 --> 00:12:52,069
that they're from as well as a
description of their clothing and color

822
00:12:52,069 --> 00:12:52,079
description of their clothing and color
 

823
00:12:52,079 --> 00:12:53,750
description of their clothing and color
scheme. So that's good. The character

824
00:12:53,750 --> 00:12:53,760
scheme. So that's good. The character
 

825
00:12:53,760 --> 00:12:55,910
scheme. So that's good. The character
agent has now done its job. So now

826
00:12:55,910 --> 00:12:55,920
agent has now done its job. So now
 

827
00:12:55,920 --> 00:12:57,829
agent has now done its job. So now
finally we just need an agent to put the

828
00:12:57,829 --> 00:12:57,839
finally we just need an agent to put the
 

829
00:12:57,839 --> 00:12:59,750
finally we just need an agent to put the
output of these two agents together. But

830
00:12:59,750 --> 00:12:59,760
output of these two agents together. But
 

831
00:12:59,760 --> 00:13:01,590
output of these two agents together. But
we need another agent so that it can do

832
00:13:01,590 --> 00:13:01,600
we need another agent so that it can do
 

833
00:13:01,600 --> 00:13:03,670
we need another agent so that it can do
it in a smart manner as well. So if we

834
00:13:03,670 --> 00:13:03,680
it in a smart manner as well. So if we
 

835
00:13:03,680 --> 00:13:05,750
it in a smart manner as well. So if we
just control D this one, we can do the

836
00:13:05,750 --> 00:13:05,760
just control D this one, we can do the
 

837
00:13:05,760 --> 00:13:07,110
just control D this one, we can do the
same thing where we connect it connect

838
00:13:07,110 --> 00:13:07,120
same thing where we connect it connect
 

839
00:13:07,120 --> 00:13:09,590
same thing where we connect it connect
the chat model to that same chat model.

840
00:13:09,590 --> 00:13:09,600
the chat model to that same chat model.
 

841
00:13:09,600 --> 00:13:11,269
the chat model to that same chat model.
Connect the tool to the think tool so

842
00:13:11,269 --> 00:13:11,279
Connect the tool to the think tool so
 

843
00:13:11,279 --> 00:13:13,110
Connect the tool to the think tool so
that it has access to that as well. And

844
00:13:13,110 --> 00:13:13,120
that it has access to that as well. And
 

845
00:13:13,120 --> 00:13:15,350
that it has access to that as well. And
if we open this and rename it into

846
00:13:15,350 --> 00:13:15,360
if we open this and rename it into
 

847
00:13:15,360 --> 00:13:17,350
if we open this and rename it into
something like the final prompt agent,

848
00:13:17,350 --> 00:13:17,360
something like the final prompt agent,
 

849
00:13:17,360 --> 00:13:19,509
something like the final prompt agent,
we can just remove this prompt as well

850
00:13:19,509 --> 00:13:19,519
we can just remove this prompt as well
 

851
00:13:19,519 --> 00:13:21,590
we can just remove this prompt as well
as the system prompt again, you can just

852
00:13:21,590 --> 00:13:21,600
as the system prompt again, you can just
 

853
00:13:21,600 --> 00:13:23,670
as the system prompt again, you can just
find the system prompt for this agent

854
00:13:23,670 --> 00:13:23,680
find the system prompt for this agent
 

855
00:13:23,680 --> 00:13:26,389
find the system prompt for this agent
here at our community course. And if you

856
00:13:26,389 --> 00:13:26,399
here at our community course. And if you
 

857
00:13:26,399 --> 00:13:29,030
here at our community course. And if you
copy that there and inspect it, you'll

858
00:13:29,030 --> 00:13:29,040
copy that there and inspect it, you'll
 

859
00:13:29,040 --> 00:13:30,629
copy that there and inspect it, you'll
see that it's once again just giving

860
00:13:30,629 --> 00:13:30,639
see that it's once again just giving
 

861
00:13:30,639 --> 00:13:32,629
see that it's once again just giving
instructions to this agent, saying that

862
00:13:32,629 --> 00:13:32,639
instructions to this agent, saying that
 

863
00:13:32,639 --> 00:13:34,069
instructions to this agent, saying that
these are the inputs that we'll give it,

864
00:13:34,069 --> 00:13:34,079
these are the inputs that we'll give it,
 

865
00:13:34,079 --> 00:13:35,750
these are the inputs that we'll give it,
which are basically the outputs of agent

866
00:13:35,750 --> 00:13:35,760
which are basically the outputs of agent
 

867
00:13:35,760 --> 00:13:37,910
which are basically the outputs of agent
one and agent 2. And we give it a task,

868
00:13:37,910 --> 00:13:37,920
one and agent 2. And we give it a task,
 

869
00:13:37,920 --> 00:13:39,829
one and agent 2. And we give it a task,
which is to generate distinct image

870
00:13:39,829 --> 00:13:39,839
which is to generate distinct image
 

871
00:13:39,839 --> 00:13:41,750
which is to generate distinct image
prompts that are all using the same

872
00:13:41,750 --> 00:13:41,760
prompts that are all using the same
 

873
00:13:41,760 --> 00:13:43,430
prompts that are all using the same
visual style. And then we have a few

874
00:13:43,430 --> 00:13:43,440
visual style. And then we have a few
 

875
00:13:43,440 --> 00:13:45,269
visual style. And then we have a few
more rules here just to guide what we

876
00:13:45,269 --> 00:13:45,279
more rules here just to guide what we
 

877
00:13:45,279 --> 00:13:47,110
more rules here just to guide what we
wanted to generate and what things to

878
00:13:47,110 --> 00:13:47,120
wanted to generate and what things to
 

879
00:13:47,120 --> 00:13:48,790
wanted to generate and what things to
avoid and all of that. So that's good.

880
00:13:48,790 --> 00:13:48,800
avoid and all of that. So that's good.
 

881
00:13:48,800 --> 00:13:50,629
avoid and all of that. So that's good.
And then for the prompt here, this is

882
00:13:50,629 --> 00:13:50,639
And then for the prompt here, this is
 

883
00:13:50,639 --> 00:13:52,629
And then for the prompt here, this is
going to be where we're going to feed it

884
00:13:52,629 --> 00:13:52,639
going to be where we're going to feed it
 

885
00:13:52,639 --> 00:13:54,790
going to be where we're going to feed it
these values that our previous agents

886
00:13:54,790 --> 00:13:54,800
these values that our previous agents
 

887
00:13:54,800 --> 00:13:56,470
these values that our previous agents
came up with. So how do we do that

888
00:13:56,470 --> 00:13:56,480
came up with. So how do we do that
 

889
00:13:56,480 --> 00:13:58,389
came up with. So how do we do that
dynamically so that every run is going

890
00:13:58,389 --> 00:13:58,399
dynamically so that every run is going
 

891
00:13:58,399 --> 00:13:59,829
dynamically so that every run is going
to be different. So if you go back to

892
00:13:59,829 --> 00:13:59,839
to be different. So if you go back to
 

893
00:13:59,839 --> 00:14:01,670
to be different. So if you go back to
our course page, you would have access

894
00:14:01,670 --> 00:14:01,680
our course page, you would have access
 

895
00:14:01,680 --> 00:14:03,430
our course page, you would have access
to this prompt as well, which we can

896
00:14:03,430 --> 00:14:03,440
to this prompt as well, which we can
 

897
00:14:03,440 --> 00:14:05,910
to this prompt as well, which we can
just copy and change this to expression,

898
00:14:05,910 --> 00:14:05,920
just copy and change this to expression,
 

899
00:14:05,920 --> 00:14:07,750
just copy and change this to expression,
which is very important. So that when we

900
00:14:07,750 --> 00:14:07,760
which is very important. So that when we
 

901
00:14:07,760 --> 00:14:09,509
which is very important. So that when we
copy it, we'll be able to inspect it

902
00:14:09,509 --> 00:14:09,519
copy it, we'll be able to inspect it
 

903
00:14:09,519 --> 00:14:10,949
copy it, we'll be able to inspect it
using this button. And you'll see that

904
00:14:10,949 --> 00:14:10,959
using this button. And you'll see that
 

905
00:14:10,959 --> 00:14:12,870
using this button. And you'll see that
this one's a bit more complex, but it's

906
00:14:12,870 --> 00:14:12,880
this one's a bit more complex, but it's
 

907
00:14:12,880 --> 00:14:14,470
this one's a bit more complex, but it's
still quite simple because we just have

908
00:14:14,470 --> 00:14:14,480
still quite simple because we just have
 

909
00:14:14,480 --> 00:14:16,069
still quite simple because we just have
the task here at the top. And then you

910
00:14:16,069 --> 00:14:16,079
the task here at the top. And then you
 

911
00:14:16,079 --> 00:14:17,990
the task here at the top. And then you
can see there's just two sections here.

912
00:14:17,990 --> 00:14:18,000
can see there's just two sections here.
 

913
00:14:18,000 --> 00:14:20,230
can see there's just two sections here.
One is the style input, and there are

914
00:14:20,230 --> 00:14:20,240
One is the style input, and there are
 

915
00:14:20,240 --> 00:14:22,150
One is the style input, and there are
all these green text because this is

916
00:14:22,150 --> 00:14:22,160
all these green text because this is
 

917
00:14:22,160 --> 00:14:24,310
all these green text because this is
just a dynamic value that points to the

918
00:14:24,310 --> 00:14:24,320
just a dynamic value that points to the
 

919
00:14:24,320 --> 00:14:26,150
just a dynamic value that points to the
output of the style agent, the one we

920
00:14:26,150 --> 00:14:26,160
output of the style agent, the one we
 

921
00:14:26,160 --> 00:14:27,670
output of the style agent, the one we
just set up, which is sort of like a

922
00:14:27,670 --> 00:14:27,680
just set up, which is sort of like a
 

923
00:14:27,680 --> 00:14:29,910
just set up, which is sort of like a
placeholder code value. But in reality,

924
00:14:29,910 --> 00:14:29,920
placeholder code value. But in reality,
 

925
00:14:29,920 --> 00:14:32,150
placeholder code value. But in reality,
during this run, this section is

926
00:14:32,150 --> 00:14:32,160
during this run, this section is
 

927
00:14:32,160 --> 00:14:33,910
during this run, this section is
actually what's being passed. So you can

928
00:14:33,910 --> 00:14:33,920
actually what's being passed. So you can
 

929
00:14:33,920 --> 00:14:35,670
actually what's being passed. So you can
see that the style description is that

930
00:14:35,670 --> 00:14:35,680
see that the style description is that
 

931
00:14:35,680 --> 00:14:37,430
see that the style description is that
origyami diagrams that the model

932
00:14:37,430 --> 00:14:37,440
origyami diagrams that the model
 

933
00:14:37,440 --> 00:14:39,030
origyami diagrams that the model
generated for us a while ago. We have

934
00:14:39,030 --> 00:14:39,040
generated for us a while ago. We have
 

935
00:14:39,040 --> 00:14:40,629
generated for us a while ago. We have
the character placements and other

936
00:14:40,629 --> 00:14:40,639
the character placements and other
 

937
00:14:40,639 --> 00:14:42,069
the character placements and other
attributes here. And then under

938
00:14:42,069 --> 00:14:42,079
attributes here. And then under
 

939
00:14:42,079 --> 00:14:44,069
attributes here. And then under
character input, this one now is coming

940
00:14:44,069 --> 00:14:44,079
character input, this one now is coming
 

941
00:14:44,079 --> 00:14:45,990
character input, this one now is coming
from our character agent and we're just

942
00:14:45,990 --> 00:14:46,000
from our character agent and we're just
 

943
00:14:46,000 --> 00:14:47,990
from our character agent and we're just
summarizing it here. So that this final

944
00:14:47,990 --> 00:14:48,000
summarizing it here. So that this final
 

945
00:14:48,000 --> 00:14:49,990
summarizing it here. So that this final
prompt agent understands what are the

946
00:14:49,990 --> 00:14:50,000
prompt agent understands what are the
 

947
00:14:50,000 --> 00:14:51,990
prompt agent understands what are the
characters that we're interested in for

948
00:14:51,990 --> 00:14:52,000
characters that we're interested in for
 

949
00:14:52,000 --> 00:14:54,470
characters that we're interested in for
this one run. So before we hit test step

950
00:14:54,470 --> 00:14:54,480
this one run. So before we hit test step
 

951
00:14:54,480 --> 00:14:56,310
this one run. So before we hit test step
there, remember that it's always just

952
00:14:56,310 --> 00:14:56,320
there, remember that it's always just
 

953
00:14:56,320 --> 00:14:58,310
there, remember that it's always just
good to have an output parser. So let's

954
00:14:58,310 --> 00:14:58,320
good to have an output parser. So let's
 

955
00:14:58,320 --> 00:15:00,470
good to have an output parser. So let's
just add that in. And you can just copy

956
00:15:00,470 --> 00:15:00,480
just add that in. And you can just copy
 

957
00:15:00,480 --> 00:15:02,710
just add that in. And you can just copy
that piece here which you can copy paste

958
00:15:02,710 --> 00:15:02,720
that piece here which you can copy paste
 

959
00:15:02,720 --> 00:15:04,470
that piece here which you can copy paste
there. So there that should be now set

960
00:15:04,470 --> 00:15:04,480
there. So there that should be now set
 

961
00:15:04,480 --> 00:15:06,310
there. So there that should be now set
up. And we can click on test step. And

962
00:15:06,310 --> 00:15:06,320
up. And we can click on test step. And
 

963
00:15:06,320 --> 00:15:08,710
up. And we can click on test step. And
when that's done, you now see these

964
00:15:08,710 --> 00:15:08,720
when that's done, you now see these
 

965
00:15:08,720 --> 00:15:10,790
when that's done, you now see these
three image prompts that are similar to

966
00:15:10,790 --> 00:15:10,800
three image prompts that are similar to
 

967
00:15:10,800 --> 00:15:12,949
three image prompts that are similar to
the end in mind prompts that we had from

968
00:15:12,949 --> 00:15:12,959
the end in mind prompts that we had from
 

969
00:15:12,959 --> 00:15:14,949
the end in mind prompts that we had from
the beginning, which is good because now

970
00:15:14,949 --> 00:15:14,959
the beginning, which is good because now
 

971
00:15:14,959 --> 00:15:17,110
the beginning, which is good because now
we're ready to feed these prompts into

972
00:15:17,110 --> 00:15:17,120
we're ready to feed these prompts into
 

973
00:15:17,120 --> 00:15:19,110
we're ready to feed these prompts into
our image model, which is going to be

974
00:15:19,110 --> 00:15:19,120
our image model, which is going to be
 

975
00:15:19,120 --> 00:15:21,350
our image model, which is going to be
ChhatgPT's latest image model, which is

976
00:15:21,350 --> 00:15:21,360
ChhatgPT's latest image model, which is
 

977
00:15:21,360 --> 00:15:22,870
ChhatgPT's latest image model, which is
the most powerful one at the market

978
00:15:22,870 --> 00:15:22,880
the most powerful one at the market
 

979
00:15:22,880 --> 00:15:24,389
the most powerful one at the market
right now. So if you want, you can just

980
00:15:24,389 --> 00:15:24,399
right now. So if you want, you can just
 

981
00:15:24,399 --> 00:15:26,470
right now. So if you want, you can just
clean this up and move things around so

982
00:15:26,470 --> 00:15:26,480
clean this up and move things around so
 

983
00:15:26,480 --> 00:15:28,069
clean this up and move things around so
that it's a bit cleaner the next time

984
00:15:28,069 --> 00:15:28,079
that it's a bit cleaner the next time
 

985
00:15:28,079 --> 00:15:30,389
that it's a bit cleaner the next time
you run it. So that is part one now

986
00:15:30,389 --> 00:15:30,399
you run it. So that is part one now
 

987
00:15:30,399 --> 00:15:32,870
you run it. So that is part one now
done. So now if you go to step two, the

988
00:15:32,870 --> 00:15:32,880
done. So now if you go to step two, the
 

989
00:15:32,880 --> 00:15:35,269
done. So now if you go to step two, the
end in mind for this step is for us to

990
00:15:35,269 --> 00:15:35,279
end in mind for this step is for us to
 

991
00:15:35,279 --> 00:15:37,350
end in mind for this step is for us to
be able to generate the images coming

992
00:15:37,350 --> 00:15:37,360
be able to generate the images coming
 

993
00:15:37,360 --> 00:15:39,189
be able to generate the images coming
from those prompts. And there's just a

994
00:15:39,189 --> 00:15:39,199
from those prompts. And there's just a
 

995
00:15:39,199 --> 00:15:41,269
from those prompts. And there's just a
couple of nodes here mainly due to how

996
00:15:41,269 --> 00:15:41,279
couple of nodes here mainly due to how
 

997
00:15:41,279 --> 00:15:43,829
couple of nodes here mainly due to how
the chat GBT image generation system is

998
00:15:43,829 --> 00:15:43,839
the chat GBT image generation system is
 

999
00:15:43,839 --> 00:15:45,350
the chat GBT image generation system is
set up. But if you look at the final

1000
00:15:45,350 --> 00:15:45,360
set up. But if you look at the final
 

1001
00:15:45,360 --> 00:15:47,189
set up. But if you look at the final
node just so that we know what it is

1002
00:15:47,189 --> 00:15:47,199
node just so that we know what it is
 

1003
00:15:47,199 --> 00:15:48,790
node just so that we know what it is
that we are gunning for in the end,

1004
00:15:48,790 --> 00:15:48,800
that we are gunning for in the end,
 

1005
00:15:48,800 --> 00:15:50,550
that we are gunning for in the end,
you'll see that we want these three

1006
00:15:50,550 --> 00:15:50,560
you'll see that we want these three
 

1007
00:15:50,560 --> 00:15:53,030
you'll see that we want these three
media URLs which if you just copy any of

1008
00:15:53,030 --> 00:15:53,040
media URLs which if you just copy any of
 

1009
00:15:53,040 --> 00:15:55,590
media URLs which if you just copy any of
those URLs and paste it to any browser

1010
00:15:55,590 --> 00:15:55,600
those URLs and paste it to any browser
 

1011
00:15:55,600 --> 00:15:57,670
those URLs and paste it to any browser
tab, you'll be able to preview an

1012
00:15:57,670 --> 00:15:57,680
tab, you'll be able to preview an
 

1013
00:15:57,680 --> 00:15:59,749
tab, you'll be able to preview an
example of an image that we are going to

1014
00:15:59,749 --> 00:15:59,759
example of an image that we are going to
 

1015
00:15:59,759 --> 00:16:02,230
example of an image that we are going to
generate. So for this run, this is Kuwa

1016
00:16:02,230 --> 00:16:02,240
generate. So for this run, this is Kuwa
 

1017
00:16:02,240 --> 00:16:04,230
generate. So for this run, this is Kuwa
from Hunter Hunter that was fully

1018
00:16:04,230 --> 00:16:04,240
from Hunter Hunter that was fully
 

1019
00:16:04,240 --> 00:16:05,990
from Hunter Hunter that was fully
generated by that prompt that we just

1020
00:16:05,990 --> 00:16:06,000
generated by that prompt that we just
 

1021
00:16:06,000 --> 00:16:07,910
generated by that prompt that we just
gave Chache BT. So let's talk through

1022
00:16:07,910 --> 00:16:07,920
gave Chache BT. So let's talk through
 

1023
00:16:07,920 --> 00:16:09,990
gave Chache BT. So let's talk through
how to set up these nodes step by step.

1024
00:16:09,990 --> 00:16:10,000
how to set up these nodes step by step.
 

1025
00:16:10,000 --> 00:16:11,990
how to set up these nodes step by step.
So what you want to do is to just drag

1026
00:16:11,990 --> 00:16:12,000
So what you want to do is to just drag
 

1027
00:16:12,000 --> 00:16:14,150
So what you want to do is to just drag
this plus icon at the very end of step

1028
00:16:14,150 --> 00:16:14,160
this plus icon at the very end of step
 

1029
00:16:14,160 --> 00:16:16,870
this plus icon at the very end of step
one and find the code node and then you

1030
00:16:16,870 --> 00:16:16,880
one and find the code node and then you
 

1031
00:16:16,880 --> 00:16:19,189
one and find the code node and then you
can rename this as list because what

1032
00:16:19,189 --> 00:16:19,199
can rename this as list because what
 

1033
00:16:19,199 --> 00:16:21,749
can rename this as list because what
this will basically do is to grab the

1034
00:16:21,749 --> 00:16:21,759
this will basically do is to grab the
 

1035
00:16:21,759 --> 00:16:23,350
this will basically do is to grab the
image prompts that we generated all

1036
00:16:23,350 --> 00:16:23,360
image prompts that we generated all
 

1037
00:16:23,360 --> 00:16:24,870
image prompts that we generated all
three of them and list them down

1038
00:16:24,870 --> 00:16:24,880
three of them and list them down
 

1039
00:16:24,880 --> 00:16:27,269
three of them and list them down
individually so that N8N considers them

1040
00:16:27,269 --> 00:16:27,279
individually so that N8N considers them
 

1041
00:16:27,279 --> 00:16:29,430
individually so that N8N considers them
as three separate items. And the reason

1042
00:16:29,430 --> 00:16:29,440
as three separate items. And the reason
 

1043
00:16:29,440 --> 00:16:31,269
as three separate items. And the reason
why that's important is because when you

1044
00:16:31,269 --> 00:16:31,279
why that's important is because when you
 

1045
00:16:31,279 --> 00:16:33,829
why that's important is because when you
pass along those prompts to chatbt in

1046
00:16:33,829 --> 00:16:33,839
pass along those prompts to chatbt in
 

1047
00:16:33,839 --> 00:16:35,590
pass along those prompts to chatbt in
N8N at least they need to be three

1048
00:16:35,590 --> 00:16:35,600
N8N at least they need to be three
 

1049
00:16:35,600 --> 00:16:37,590
N8N at least they need to be three
separate items so that you can send them

1050
00:16:37,590 --> 00:16:37,600
separate items so that you can send them
 

1051
00:16:37,600 --> 00:16:40,470
separate items so that you can send them
to ChachiPT's image model individually.

1052
00:16:40,470 --> 00:16:40,480
to ChachiPT's image model individually.
 

1053
00:16:40,480 --> 00:16:42,389
to ChachiPT's image model individually.
So for us to do that you can just copy

1054
00:16:42,389 --> 00:16:42,399
So for us to do that you can just copy
 

1055
00:16:42,399 --> 00:16:45,030
So for us to do that you can just copy
this piece of script in our course page

1056
00:16:45,030 --> 00:16:45,040
this piece of script in our course page
 

1057
00:16:45,040 --> 00:16:47,189
this piece of script in our course page
under generate images step and remove

1058
00:16:47,189 --> 00:16:47,199
under generate images step and remove
 

1059
00:16:47,199 --> 00:16:49,110
under generate images step and remove
this placeholder and paste that there.

1060
00:16:49,110 --> 00:16:49,120
this placeholder and paste that there.
 

1061
00:16:49,120 --> 00:16:51,430
this placeholder and paste that there.
So now if we click on test step here,

1062
00:16:51,430 --> 00:16:51,440
So now if we click on test step here,
 

1063
00:16:51,440 --> 00:16:53,269
So now if we click on test step here,
all that really did is to turn this

1064
00:16:53,269 --> 00:16:53,279
all that really did is to turn this
 

1065
00:16:53,279 --> 00:16:55,670
all that really did is to turn this
input which is clustered as one item in

1066
00:16:55,670 --> 00:16:55,680
input which is clustered as one item in
 

1067
00:16:55,680 --> 00:16:58,550
input which is clustered as one item in
N8N's instance and dissect it into three

1068
00:16:58,550 --> 00:16:58,560
N8N's instance and dissect it into three
 

1069
00:16:58,560 --> 00:17:00,389
N8N's instance and dissect it into three
specific items which if you go to the

1070
00:17:00,389 --> 00:17:00,399
specific items which if you go to the
 

1071
00:17:00,399 --> 00:17:01,990
specific items which if you go to the
table view, you'll see those same

1072
00:17:01,990 --> 00:17:02,000
table view, you'll see those same
 

1073
00:17:02,000 --> 00:17:04,470
table view, you'll see those same
prompts but now properly loaded in N8N

1074
00:17:04,470 --> 00:17:04,480
prompts but now properly loaded in N8N
 

1075
00:17:04,480 --> 00:17:07,029
prompts but now properly loaded in N8N
systems as three items. So that's good.

1076
00:17:07,029 --> 00:17:07,039
systems as three items. So that's good.
 

1077
00:17:07,039 --> 00:17:09,669
systems as three items. So that's good.
And now the next step is for us to pass

1078
00:17:09,669 --> 00:17:09,679
And now the next step is for us to pass
 

1079
00:17:09,679 --> 00:17:11,909
And now the next step is for us to pass
those prompts along to our image model.

1080
00:17:11,909 --> 00:17:11,919
those prompts along to our image model.
 

1081
00:17:11,919 --> 00:17:14,309
those prompts along to our image model.
And we'll do that via this HTTP request

1082
00:17:14,309 --> 00:17:14,319
And we'll do that via this HTTP request
 

1083
00:17:14,319 --> 00:17:16,470
And we'll do that via this HTTP request
which you can just rename as generate

1084
00:17:16,470 --> 00:17:16,480
which you can just rename as generate
 

1085
00:17:16,480 --> 00:17:18,150
which you can just rename as generate
because we'll be generating the images.

1086
00:17:18,150 --> 00:17:18,160
because we'll be generating the images.
 

1087
00:17:18,160 --> 00:17:20,309
because we'll be generating the images.
And the way that this HTTP request node

1088
00:17:20,309 --> 00:17:20,319
And the way that this HTTP request node
 

1089
00:17:20,319 --> 00:17:22,470
And the way that this HTTP request node
works is that you have a method here

1090
00:17:22,470 --> 00:17:22,480
works is that you have a method here
 

1091
00:17:22,480 --> 00:17:24,549
works is that you have a method here
which you want to select post for that

1092
00:17:24,549 --> 00:17:24,559
which you want to select post for that
 

1093
00:17:24,559 --> 00:17:26,870
which you want to select post for that
because what the HTTP request is doing

1094
00:17:26,870 --> 00:17:26,880
because what the HTTP request is doing
 

1095
00:17:26,880 --> 00:17:29,029
because what the HTTP request is doing
will be posting a request to our third

1096
00:17:29,029 --> 00:17:29,039
will be posting a request to our third
 

1097
00:17:29,039 --> 00:17:30,630
will be posting a request to our third
party tool. In this case, it will be

1098
00:17:30,630 --> 00:17:30,640
party tool. In this case, it will be
 

1099
00:17:30,640 --> 00:17:33,029
party tool. In this case, it will be
chat GPT's image model to send them our

1100
00:17:33,029 --> 00:17:33,039
chat GPT's image model to send them our
 

1101
00:17:33,039 --> 00:17:35,029
chat GPT's image model to send them our
prompt and get the image in return. And

1102
00:17:35,029 --> 00:17:35,039
prompt and get the image in return. And
 

1103
00:17:35,039 --> 00:17:37,029
prompt and get the image in return. And
this is also the reason why the HTTP

1104
00:17:37,029 --> 00:17:37,039
this is also the reason why the HTTP
 

1105
00:17:37,039 --> 00:17:38,870
this is also the reason why the HTTP
request node is one of the most common

1106
00:17:38,870 --> 00:17:38,880
request node is one of the most common
 

1107
00:17:38,880 --> 00:17:40,950
request node is one of the most common
nodes that you'll see used in N8N

1108
00:17:40,950 --> 00:17:40,960
nodes that you'll see used in N8N
 

1109
00:17:40,960 --> 00:17:42,710
nodes that you'll see used in N8N
workflows. And that's because it's so

1110
00:17:42,710 --> 00:17:42,720
workflows. And that's because it's so
 

1111
00:17:42,720 --> 00:17:44,710
workflows. And that's because it's so
flexible and is the primary way by which

1112
00:17:44,710 --> 00:17:44,720
flexible and is the primary way by which
 

1113
00:17:44,720 --> 00:17:46,710
flexible and is the primary way by which
you can call on thirdparty tools through

1114
00:17:46,710 --> 00:17:46,720
you can call on thirdparty tools through
 

1115
00:17:46,720 --> 00:17:49,110
you can call on thirdparty tools through
these standardized ways of communicating

1116
00:17:49,110 --> 00:17:49,120
these standardized ways of communicating
 

1117
00:17:49,120 --> 00:17:51,190
these standardized ways of communicating
which people are calling APIs or

1118
00:17:51,190 --> 00:17:51,200
which people are calling APIs or
 

1119
00:17:51,200 --> 00:17:53,430
which people are calling APIs or
application programming interfaces. And

1120
00:17:53,430 --> 00:17:53,440
application programming interfaces. And
 

1121
00:17:53,440 --> 00:17:55,190
application programming interfaces. And
when you're posting a request, you

1122
00:17:55,190 --> 00:17:55,200
when you're posting a request, you
 

1123
00:17:55,200 --> 00:17:56,870
when you're posting a request, you
obviously would need a place where you

1124
00:17:56,870 --> 00:17:56,880
obviously would need a place where you
 

1125
00:17:56,880 --> 00:17:58,470
obviously would need a place where you
would want to post that request in. And

1126
00:17:58,470 --> 00:17:58,480
would want to post that request in. And
 

1127
00:17:58,480 --> 00:18:00,710
would want to post that request in. And
so you would need a URL here. And to

1128
00:18:00,710 --> 00:18:00,720
so you would need a URL here. And to
 

1129
00:18:00,720 --> 00:18:02,630
so you would need a URL here. And to
make that easy, the URL I already

1130
00:18:02,630 --> 00:18:02,640
make that easy, the URL I already
 

1131
00:18:02,640 --> 00:18:04,230
make that easy, the URL I already
included here so that you can copy it

1132
00:18:04,230 --> 00:18:04,240
included here so that you can copy it
 

1133
00:18:04,240 --> 00:18:05,830
included here so that you can copy it
and just paste that there. And you can

1134
00:18:05,830 --> 00:18:05,840
and just paste that there. And you can
 

1135
00:18:05,840 --> 00:18:07,750
and just paste that there. And you can
see it's pointing to OpenAI's website

1136
00:18:07,750 --> 00:18:07,760
see it's pointing to OpenAI's website
 

1137
00:18:07,760 --> 00:18:09,830
see it's pointing to OpenAI's website
here, specifically their API domain

1138
00:18:09,830 --> 00:18:09,840
here, specifically their API domain
 

1139
00:18:09,840 --> 00:18:11,430
here, specifically their API domain
service. And then now that you know

1140
00:18:11,430 --> 00:18:11,440
service. And then now that you know
 

1141
00:18:11,440 --> 00:18:13,110
service. And then now that you know
where you'll be posting this request,

1142
00:18:13,110 --> 00:18:13,120
where you'll be posting this request,
 

1143
00:18:13,120 --> 00:18:14,950
where you'll be posting this request,
there's also a question of what is the

1144
00:18:14,950 --> 00:18:14,960
there's also a question of what is the
 

1145
00:18:14,960 --> 00:18:16,310
there's also a question of what is the
request exactly that you're going to

1146
00:18:16,310 --> 00:18:16,320
request exactly that you're going to
 

1147
00:18:16,320 --> 00:18:17,909
request exactly that you're going to
send. And so to do that, you can just

1148
00:18:17,909 --> 00:18:17,919
send. And so to do that, you can just
 

1149
00:18:17,919 --> 00:18:19,909
send. And so to do that, you can just
toggle this send body as on. And here

1150
00:18:19,909 --> 00:18:19,919
toggle this send body as on. And here
 

1151
00:18:19,919 --> 00:18:21,909
toggle this send body as on. And here
we'll just keep it as JSON. And for

1152
00:18:21,909 --> 00:18:21,919
we'll just keep it as JSON. And for
 

1153
00:18:21,919 --> 00:18:24,310
we'll just keep it as JSON. And for
specify body, we'll be using a JSON

1154
00:18:24,310 --> 00:18:24,320
specify body, we'll be using a JSON
 

1155
00:18:24,320 --> 00:18:25,750
specify body, we'll be using a JSON
structure here. And to make that easy,

1156
00:18:25,750 --> 00:18:25,760
structure here. And to make that easy,
 

1157
00:18:25,760 --> 00:18:27,510
structure here. And to make that easy,
you can just copy whatever we have here

1158
00:18:27,510 --> 00:18:27,520
you can just copy whatever we have here
 

1159
00:18:27,520 --> 00:18:29,350
you can just copy whatever we have here
in the course page. Change this to

1160
00:18:29,350 --> 00:18:29,360
in the course page. Change this to
 

1161
00:18:29,360 --> 00:18:30,950
in the course page. Change this to
expression, which is important. So that

1162
00:18:30,950 --> 00:18:30,960
expression, which is important. So that
 

1163
00:18:30,960 --> 00:18:32,950
expression, which is important. So that
if I paste it here and preview that,

1164
00:18:32,950 --> 00:18:32,960
if I paste it here and preview that,
 

1165
00:18:32,960 --> 00:18:34,710
if I paste it here and preview that,
you'll see that this is just a JSON

1166
00:18:34,710 --> 00:18:34,720
you'll see that this is just a JSON
 

1167
00:18:34,720 --> 00:18:36,549
you'll see that this is just a JSON
format. So again with the braces and

1168
00:18:36,549 --> 00:18:36,559
format. So again with the braces and
 

1169
00:18:36,559 --> 00:18:39,270
format. So again with the braces and
brackets where we're asking OpenAI if we

1170
00:18:39,270 --> 00:18:39,280
brackets where we're asking OpenAI if we
 

1171
00:18:39,280 --> 00:18:41,190
brackets where we're asking OpenAI if we
can use this image model which is their

1172
00:18:41,190 --> 00:18:41,200
can use this image model which is their
 

1173
00:18:41,200 --> 00:18:43,029
can use this image model which is their
latest image model and the prompt that

1174
00:18:43,029 --> 00:18:43,039
latest image model and the prompt that
 

1175
00:18:43,039 --> 00:18:44,950
latest image model and the prompt that
we'll give it is this dynamic value

1176
00:18:44,950 --> 00:18:44,960
we'll give it is this dynamic value
 

1177
00:18:44,960 --> 00:18:46,549
we'll give it is this dynamic value
which is resulting to this piece on the

1178
00:18:46,549 --> 00:18:46,559
which is resulting to this piece on the
 

1179
00:18:46,559 --> 00:18:48,150
which is resulting to this piece on the
right with that full prompt there and

1180
00:18:48,150 --> 00:18:48,160
right with that full prompt there and
 

1181
00:18:48,160 --> 00:18:49,590
right with that full prompt there and
then a couple of attributes like what's

1182
00:18:49,590 --> 00:18:49,600
then a couple of attributes like what's
 

1183
00:18:49,600 --> 00:18:51,430
then a couple of attributes like what's
the size what's the quality and so on

1184
00:18:51,430 --> 00:18:51,440
the size what's the quality and so on
 

1185
00:18:51,440 --> 00:18:53,110
the size what's the quality and so on
and so forth. So these parameters as

1186
00:18:53,110 --> 00:18:53,120
and so forth. So these parameters as
 

1187
00:18:53,120 --> 00:18:54,710
and so forth. So these parameters as
they call it these are all defined by

1188
00:18:54,710 --> 00:18:54,720
they call it these are all defined by
 

1189
00:18:54,720 --> 00:18:56,710
they call it these are all defined by
the service provider and so if you

1190
00:18:56,710 --> 00:18:56,720
the service provider and so if you
 

1191
00:18:56,720 --> 00:18:58,710
the service provider and so if you
mistype any of these it's not going to

1192
00:18:58,710 --> 00:18:58,720
mistype any of these it's not going to
 

1193
00:18:58,720 --> 00:19:00,470
mistype any of these it's not going to
work because it needs to be a specific

1194
00:19:00,470 --> 00:19:00,480
work because it needs to be a specific
 

1195
00:19:00,480 --> 00:19:02,710
work because it needs to be a specific
format that open AAI would accept. So

1196
00:19:02,710 --> 00:19:02,720
format that open AAI would accept. So
 

1197
00:19:02,720 --> 00:19:04,230
format that open AAI would accept. So
just some important call outs here.

1198
00:19:04,230 --> 00:19:04,240
just some important call outs here.
 

1199
00:19:04,240 --> 00:19:06,070
just some important call outs here.
Right now, we've set the quality as high

1200
00:19:06,070 --> 00:19:06,080
Right now, we've set the quality as high
 

1201
00:19:06,080 --> 00:19:07,510
Right now, we've set the quality as high
because we want to illustrate the best

1202
00:19:07,510 --> 00:19:07,520
because we want to illustrate the best
 

1203
00:19:07,520 --> 00:19:09,110
because we want to illustrate the best
quality output that this model can

1204
00:19:09,110 --> 00:19:09,120
quality output that this model can
 

1205
00:19:09,120 --> 00:19:10,630
quality output that this model can
generate. But if you're testing this out

1206
00:19:10,630 --> 00:19:10,640
generate. But if you're testing this out
 

1207
00:19:10,640 --> 00:19:12,789
generate. But if you're testing this out
and you want it to cost less credits as

1208
00:19:12,789 --> 00:19:12,799
and you want it to cost less credits as
 

1209
00:19:12,799 --> 00:19:15,029
and you want it to cost less credits as
you do your tests, just type in low and

1210
00:19:15,029 --> 00:19:15,039
you do your tests, just type in low and
 

1211
00:19:15,039 --> 00:19:16,710
you do your tests, just type in low and
that would work as well. And in our

1212
00:19:16,710 --> 00:19:16,720
that would work as well. And in our
 

1213
00:19:16,720 --> 00:19:18,070
that would work as well. And in our
course page, we also have those

1214
00:19:18,070 --> 00:19:18,080
course page, we also have those
 

1215
00:19:18,080 --> 00:19:20,150
course page, we also have those
documentation for OpenAI if you want to

1216
00:19:20,150 --> 00:19:20,160
documentation for OpenAI if you want to
 

1217
00:19:20,160 --> 00:19:21,750
documentation for OpenAI if you want to
find out more about the costs and the

1218
00:19:21,750 --> 00:19:21,760
find out more about the costs and the
 

1219
00:19:21,760 --> 00:19:23,190
find out more about the costs and the
different parameters that you can set up

1220
00:19:23,190 --> 00:19:23,200
different parameters that you can set up
 

1221
00:19:23,200 --> 00:19:24,630
different parameters that you can set up
with their model. But anyway, that

1222
00:19:24,630 --> 00:19:24,640
with their model. But anyway, that
 

1223
00:19:24,640 --> 00:19:26,549
with their model. But anyway, that
should all be set up now. And so now if

1224
00:19:26,549 --> 00:19:26,559
should all be set up now. And so now if
 

1225
00:19:26,559 --> 00:19:28,710
should all be set up now. And so now if
I click on test step, this will actually

1226
00:19:28,710 --> 00:19:28,720
I click on test step, this will actually
 

1227
00:19:28,720 --> 00:19:30,950
I click on test step, this will actually
fail still because it's telling us that

1228
00:19:30,950 --> 00:19:30,960
fail still because it's telling us that
 

1229
00:19:30,960 --> 00:19:32,789
fail still because it's telling us that
we need an authorization because

1230
00:19:32,789 --> 00:19:32,799
we need an authorization because
 

1231
00:19:32,799 --> 00:19:34,710
we need an authorization because
obviously not just anyone can call on

1232
00:19:34,710 --> 00:19:34,720
obviously not just anyone can call on
 

1233
00:19:34,720 --> 00:19:36,630
obviously not just anyone can call on
this model. We'll need a pass key or an

1234
00:19:36,630 --> 00:19:36,640
this model. We'll need a pass key or an
 

1235
00:19:36,640 --> 00:19:39,190
this model. We'll need a pass key or an
API key in order to use this model that

1236
00:19:39,190 --> 00:19:39,200
API key in order to use this model that
 

1237
00:19:39,200 --> 00:19:41,669
API key in order to use this model that
OpenAI is offering to the public. So to

1238
00:19:41,669 --> 00:19:41,679
OpenAI is offering to the public. So to
 

1239
00:19:41,679 --> 00:19:43,110
OpenAI is offering to the public. So to
do that, what you can just do is to

1240
00:19:43,110 --> 00:19:43,120
do that, what you can just do is to
 

1241
00:19:43,120 --> 00:19:45,110
do that, what you can just do is to
toggle this send headers as on. You just

1242
00:19:45,110 --> 00:19:45,120
toggle this send headers as on. You just
 

1243
00:19:45,120 --> 00:19:47,190
toggle this send headers as on. You just
want to type out authorization paying

1244
00:19:47,190 --> 00:19:47,200
want to type out authorization paying
 

1245
00:19:47,200 --> 00:19:49,270
want to type out authorization paying
attention to the capitalization and note

1246
00:19:49,270 --> 00:19:49,280
attention to the capitalization and note
 

1247
00:19:49,280 --> 00:19:51,350
attention to the capitalization and note
that there should be no spaces and then

1248
00:19:51,350 --> 00:19:51,360
that there should be no spaces and then
 

1249
00:19:51,360 --> 00:19:53,990
that there should be no spaces and then
for the value just type in bearer

1250
00:19:53,990 --> 00:19:54,000
for the value just type in bearer
 

1251
00:19:54,000 --> 00:19:56,310
for the value just type in bearer
capital B and then hit on space and then

1252
00:19:56,310 --> 00:19:56,320
capital B and then hit on space and then
 

1253
00:19:56,320 --> 00:19:58,230
capital B and then hit on space and then
if you go back to open AAI's platform

1254
00:19:58,230 --> 00:19:58,240
if you go back to open AAI's platform
 

1255
00:19:58,240 --> 00:20:00,710
if you go back to open AAI's platform
page here just copy your API key and

1256
00:20:00,710 --> 00:20:00,720
page here just copy your API key and
 

1257
00:20:00,720 --> 00:20:02,789
page here just copy your API key and
paste that here. So do note that this is

1258
00:20:02,789 --> 00:20:02,799
paste that here. So do note that this is
 

1259
00:20:02,799 --> 00:20:04,470
paste that here. So do note that this is
a pass key so it shouldn't be shared

1260
00:20:04,470 --> 00:20:04,480
a pass key so it shouldn't be shared
 

1261
00:20:04,480 --> 00:20:06,470
a pass key so it shouldn't be shared
around. So for my case for example after

1262
00:20:06,470 --> 00:20:06,480
around. So for my case for example after
 

1263
00:20:06,480 --> 00:20:08,549
around. So for my case for example after
this tutorial I'll just be disabling it

1264
00:20:08,549 --> 00:20:08,559
this tutorial I'll just be disabling it
 

1265
00:20:08,559 --> 00:20:10,230
this tutorial I'll just be disabling it
but that should now be all set up. So

1266
00:20:10,230 --> 00:20:10,240
but that should now be all set up. So
 

1267
00:20:10,240 --> 00:20:12,470
but that should now be all set up. So
now we can click on test step and what

1268
00:20:12,470 --> 00:20:12,480
now we can click on test step and what
 

1269
00:20:12,480 --> 00:20:14,310
now we can click on test step and what
it's going to do is it's going to send

1270
00:20:14,310 --> 00:20:14,320
it's going to do is it's going to send
 

1271
00:20:14,320 --> 00:20:16,870
it's going to do is it's going to send
these three prompts to be sent to open

1272
00:20:16,870 --> 00:20:16,880
these three prompts to be sent to open
 

1273
00:20:16,880 --> 00:20:19,430
these three prompts to be sent to open
AAI's image model and in return in this

1274
00:20:19,430 --> 00:20:19,440
AAI's image model and in return in this
 

1275
00:20:19,440 --> 00:20:21,430
AAI's image model and in return in this
output it will generate for us those

1276
00:20:21,430 --> 00:20:21,440
output it will generate for us those
 

1277
00:20:21,440 --> 00:20:23,270
output it will generate for us those
images which we can look at. So once

1278
00:20:23,270 --> 00:20:23,280
images which we can look at. So once
 

1279
00:20:23,280 --> 00:20:25,190
images which we can look at. So once
that's finished generated you can see

1280
00:20:25,190 --> 00:20:25,200
that's finished generated you can see
 

1281
00:20:25,200 --> 00:20:27,110
that's finished generated you can see
that there's no image here. In fact it

1282
00:20:27,110 --> 00:20:27,120
that there's no image here. In fact it
 

1283
00:20:27,120 --> 00:20:29,430
that there's no image here. In fact it
gave you this piece of text which is not

1284
00:20:29,430 --> 00:20:29,440
gave you this piece of text which is not
 

1285
00:20:29,440 --> 00:20:31,430
gave you this piece of text which is not
an image at all. So how do you turn that

1286
00:20:31,430 --> 00:20:31,440
an image at all. So how do you turn that
 

1287
00:20:31,440 --> 00:20:33,750
an image at all. So how do you turn that
into an image? So it's very simple. You

1288
00:20:33,750 --> 00:20:33,760
into an image? So it's very simple. You
 

1289
00:20:33,760 --> 00:20:36,230
into an image? So it's very simple. You
just click on this next node and find

1290
00:20:36,230 --> 00:20:36,240
just click on this next node and find
 

1291
00:20:36,240 --> 00:20:38,549
just click on this next node and find
this convert to file. And then under

1292
00:20:38,549 --> 00:20:38,559
this convert to file. And then under
 

1293
00:20:38,559 --> 00:20:41,350
this convert to file. And then under
actions, you want this B 64 string to

1294
00:20:41,350 --> 00:20:41,360
actions, you want this B 64 string to
 

1295
00:20:41,360 --> 00:20:43,270
actions, you want this B 64 string to
file action. For me, I just named that

1296
00:20:43,270 --> 00:20:43,280
file action. For me, I just named that
 

1297
00:20:43,280 --> 00:20:45,190
file action. For me, I just named that
to convert to make it simple. And this

1298
00:20:45,190 --> 00:20:45,200
to convert to make it simple. And this
 

1299
00:20:45,200 --> 00:20:46,950
to convert to make it simple. And this
is all very simple. You just want to

1300
00:20:46,950 --> 00:20:46,960
is all very simple. You just want to
 

1301
00:20:46,960 --> 00:20:49,510
is all very simple. You just want to
drag all of this data into that input

1302
00:20:49,510 --> 00:20:49,520
drag all of this data into that input
 

1303
00:20:49,520 --> 00:20:51,430
drag all of this data into that input
field. And then you can just leave this

1304
00:20:51,430 --> 00:20:51,440
field. And then you can just leave this
 

1305
00:20:51,440 --> 00:20:53,669
field. And then you can just leave this
output file name as data. And then just

1306
00:20:53,669 --> 00:20:53,679
output file name as data. And then just
 

1307
00:20:53,679 --> 00:20:55,430
output file name as data. And then just
click on test step. And what that's

1308
00:20:55,430 --> 00:20:55,440
click on test step. And what that's
 

1309
00:20:55,440 --> 00:20:57,350
click on test step. And what that's
basically doing is make your images

1310
00:20:57,350 --> 00:20:57,360
basically doing is make your images
 

1311
00:20:57,360 --> 00:20:59,350
basically doing is make your images
viewable because remember each image

1312
00:20:59,350 --> 00:20:59,360
viewable because remember each image
 

1313
00:20:59,360 --> 00:21:01,510
viewable because remember each image
that you see is all just data under the

1314
00:21:01,510 --> 00:21:01,520
that you see is all just data under the
 

1315
00:21:01,520 --> 00:21:03,669
that you see is all just data under the
hood. And so when OpenAI gave us the

1316
00:21:03,669 --> 00:21:03,679
hood. And so when OpenAI gave us the
 

1317
00:21:03,679 --> 00:21:05,830
hood. And so when OpenAI gave us the
results, it returned to us that data in

1318
00:21:05,830 --> 00:21:05,840
results, it returned to us that data in
 

1319
00:21:05,840 --> 00:21:07,909
results, it returned to us that data in
text format. So what that node just did

1320
00:21:07,909 --> 00:21:07,919
text format. So what that node just did
 

1321
00:21:07,919 --> 00:21:09,990
text format. So what that node just did
is convert that text into these three

1322
00:21:09,990 --> 00:21:10,000
is convert that text into these three
 

1323
00:21:10,000 --> 00:21:11,990
is convert that text into these three
pictures here. You can actually preview

1324
00:21:11,990 --> 00:21:12,000
pictures here. You can actually preview
 

1325
00:21:12,000 --> 00:21:14,549
pictures here. You can actually preview
them by just clicking on view. So you

1326
00:21:14,549 --> 00:21:14,559
them by just clicking on view. So you
 

1327
00:21:14,559 --> 00:21:16,390
them by just clicking on view. So you
can see that is looking pretty good. And

1328
00:21:16,390 --> 00:21:16,400
can see that is looking pretty good. And
 

1329
00:21:16,400 --> 00:21:18,149
can see that is looking pretty good. And
at least for this run, as you test it

1330
00:21:18,149 --> 00:21:18,159
at least for this run, as you test it
 

1331
00:21:18,159 --> 00:21:19,830
at least for this run, as you test it
out, what I actually just did in the

1332
00:21:19,830 --> 00:21:19,840
out, what I actually just did in the
 

1333
00:21:19,840 --> 00:21:22,470
out, what I actually just did in the
background is if we click away here and

1334
00:21:22,470 --> 00:21:22,480
background is if we click away here and
 

1335
00:21:22,480 --> 00:21:24,710
background is if we click away here and
open our character agent here, you'll

1336
00:21:24,710 --> 00:21:24,720
open our character agent here, you'll
 

1337
00:21:24,720 --> 00:21:26,789
open our character agent here, you'll
see that apart from our general prompt

1338
00:21:26,789 --> 00:21:26,799
see that apart from our general prompt
 

1339
00:21:26,799 --> 00:21:28,870
see that apart from our general prompt
here at the top, I also added a sentence

1340
00:21:28,870 --> 00:21:28,880
here at the top, I also added a sentence
 

1341
00:21:28,880 --> 00:21:30,870
here at the top, I also added a sentence
here to declare the specific characters

1342
00:21:30,870 --> 00:21:30,880
here to declare the specific characters
 

1343
00:21:30,880 --> 00:21:33,029
here to declare the specific characters
that I want featured for this run. So

1344
00:21:33,029 --> 00:21:33,039
that I want featured for this run. So
 

1345
00:21:33,039 --> 00:21:34,470
that I want featured for this run. So
that's also something that you can do.

1346
00:21:34,470 --> 00:21:34,480
that's also something that you can do.
 

1347
00:21:34,480 --> 00:21:36,149
that's also something that you can do.
And you can see that the agent adhere to

1348
00:21:36,149 --> 00:21:36,159
And you can see that the agent adhere to
 

1349
00:21:36,159 --> 00:21:38,149
And you can see that the agent adhere to
that specifically. So now if we go back

1350
00:21:38,149 --> 00:21:38,159
that specifically. So now if we go back
 

1351
00:21:38,159 --> 00:21:40,230
that specifically. So now if we go back
to our ended my automation here the

1352
00:21:40,230 --> 00:21:40,240
to our ended my automation here the
 

1353
00:21:40,240 --> 00:21:42,230
to our ended my automation here the
remaining nodes here are actually just

1354
00:21:42,230 --> 00:21:42,240
remaining nodes here are actually just
 

1355
00:21:42,240 --> 00:21:44,950
remaining nodes here are actually just
nodes to prepare for the publishing step

1356
00:21:44,950 --> 00:21:44,960
nodes to prepare for the publishing step
 

1357
00:21:44,960 --> 00:21:46,549
nodes to prepare for the publishing step
because if you can see the tool that

1358
00:21:46,549 --> 00:21:46,559
because if you can see the tool that
 

1359
00:21:46,559 --> 00:21:49,350
because if you can see the tool that
we'll be using to publish those images

1360
00:21:49,350 --> 00:21:49,360
we'll be using to publish those images
 

1361
00:21:49,360 --> 00:21:51,590
we'll be using to publish those images
is actually a tool called blot because

1362
00:21:51,590 --> 00:21:51,600
is actually a tool called blot because
 

1363
00:21:51,600 --> 00:21:53,750
is actually a tool called blot because
they have that carousel feature which we

1364
00:21:53,750 --> 00:21:53,760
they have that carousel feature which we
 

1365
00:21:53,760 --> 00:21:55,669
they have that carousel feature which we
can use as part of their automation

1366
00:21:55,669 --> 00:21:55,679
can use as part of their automation
 

1367
00:21:55,679 --> 00:21:57,750
can use as part of their automation
system. But the thing with blot is

1368
00:21:57,750 --> 00:21:57,760
system. But the thing with blot is
 

1369
00:21:57,760 --> 00:22:00,070
system. But the thing with blot is
before we can upload the file to that it

1370
00:22:00,070 --> 00:22:00,080
before we can upload the file to that it
 

1371
00:22:00,080 --> 00:22:02,710
before we can upload the file to that it
can actually only receive a URL that

1372
00:22:02,710 --> 00:22:02,720
can actually only receive a URL that
 

1373
00:22:02,720 --> 00:22:04,630
can actually only receive a URL that
contains that file. And so we just need

1374
00:22:04,630 --> 00:22:04,640
contains that file. And so we just need
 

1375
00:22:04,640 --> 00:22:06,710
contains that file. And so we just need
this one extra node in order to first

1376
00:22:06,710 --> 00:22:06,720
this one extra node in order to first
 

1377
00:22:06,720 --> 00:22:09,270
this one extra node in order to first
upload the file into an image URL before

1378
00:22:09,270 --> 00:22:09,280
upload the file into an image URL before
 

1379
00:22:09,280 --> 00:22:11,190
upload the file into an image URL before
passing it on to our publishing tool

1380
00:22:11,190 --> 00:22:11,200
passing it on to our publishing tool
 

1381
00:22:11,200 --> 00:22:13,350
passing it on to our publishing tool
which is going to be bloat in this case.

1382
00:22:13,350 --> 00:22:13,360
which is going to be bloat in this case.
 

1383
00:22:13,360 --> 00:22:15,510
which is going to be bloat in this case.
So to do that you just need to add in

1384
00:22:15,510 --> 00:22:15,520
So to do that you just need to add in
 

1385
00:22:15,520 --> 00:22:17,669
So to do that you just need to add in
another HTTP request node. You can

1386
00:22:17,669 --> 00:22:17,679
another HTTP request node. You can
 

1387
00:22:17,679 --> 00:22:20,149
another HTTP request node. You can
rename this to create URL because that

1388
00:22:20,149 --> 00:22:20,159
rename this to create URL because that
 

1389
00:22:20,159 --> 00:22:22,390
rename this to create URL because that
is its purpose. And you want to change

1390
00:22:22,390 --> 00:22:22,400
is its purpose. And you want to change
 

1391
00:22:22,400 --> 00:22:25,190
is its purpose. And you want to change
this again to post. And then for the URL

1392
00:22:25,190 --> 00:22:25,200
this again to post. And then for the URL
 

1393
00:22:25,200 --> 00:22:26,549
this again to post. And then for the URL
here, the service that we'll be

1394
00:22:26,549 --> 00:22:26,559
here, the service that we'll be
 

1395
00:22:26,559 --> 00:22:28,549
here, the service that we'll be
uploading those images to is actually

1396
00:22:28,549 --> 00:22:28,559
uploading those images to is actually
 

1397
00:22:28,559 --> 00:22:30,549
uploading those images to is actually
image UR which is a free service that

1398
00:22:30,549 --> 00:22:30,559
image UR which is a free service that
 

1399
00:22:30,559 --> 00:22:32,789
image UR which is a free service that
can host images online. So you can just

1400
00:22:32,789 --> 00:22:32,799
can host images online. So you can just
 

1401
00:22:32,799 --> 00:22:35,029
can host images online. So you can just
copy this and paste that there. And

1402
00:22:35,029 --> 00:22:35,039
copy this and paste that there. And
 

1403
00:22:35,039 --> 00:22:36,870
copy this and paste that there. And
again, now that we know where we'll send

1404
00:22:36,870 --> 00:22:36,880
again, now that we know where we'll send
 

1405
00:22:36,880 --> 00:22:39,110
again, now that we know where we'll send
the files, we also need to declare what

1406
00:22:39,110 --> 00:22:39,120
the files, we also need to declare what
 

1407
00:22:39,120 --> 00:22:40,789
the files, we also need to declare what
we're going to send. And so you just

1408
00:22:40,789 --> 00:22:40,799
we're going to send. And so you just
 

1409
00:22:40,799 --> 00:22:42,710
we're going to send. And so you just
want to again toggle the send body as

1410
00:22:42,710 --> 00:22:42,720
want to again toggle the send body as
 

1411
00:22:42,720 --> 00:22:44,950
want to again toggle the send body as
on. But this time, we'll just change the

1412
00:22:44,950 --> 00:22:44,960
on. But this time, we'll just change the
 

1413
00:22:44,960 --> 00:22:47,350
on. But this time, we'll just change the
content type to an N8N binary file,

1414
00:22:47,350 --> 00:22:47,360
content type to an N8N binary file,
 

1415
00:22:47,360 --> 00:22:49,029
content type to an N8N binary file,
which is basically a file. And then we

1416
00:22:49,029 --> 00:22:49,039
which is basically a file. And then we
 

1417
00:22:49,039 --> 00:22:51,590
which is basically a file. And then we
just want to type in data here, which

1418
00:22:51,590 --> 00:22:51,600
just want to type in data here, which
 

1419
00:22:51,600 --> 00:22:54,149
just want to type in data here, which
corresponds to the name of our files in

1420
00:22:54,149 --> 00:22:54,159
corresponds to the name of our files in
 

1421
00:22:54,159 --> 00:22:56,870
corresponds to the name of our files in
N8N. So if I click test step here, it

1422
00:22:56,870 --> 00:22:56,880
N8N. So if I click test step here, it
 

1423
00:22:56,880 --> 00:22:58,789
N8N. So if I click test step here, it
will reject it once again. And that is

1424
00:22:58,789 --> 00:22:58,799
will reject it once again. And that is
 

1425
00:22:58,799 --> 00:23:01,270
will reject it once again. And that is
because we haven't passed in our pass

1426
00:23:01,270 --> 00:23:01,280
because we haven't passed in our pass
 

1427
00:23:01,280 --> 00:23:03,510
because we haven't passed in our pass
key in order to call on image UR

1428
00:23:03,510 --> 00:23:03,520
key in order to call on image UR
 

1429
00:23:03,520 --> 00:23:06,149
key in order to call on image UR
service. So to register for your pass

1430
00:23:06,149 --> 00:23:06,159
service. So to register for your pass
 

1431
00:23:06,159 --> 00:23:07,990
service. So to register for your pass
key, which for their case is just called

1432
00:23:07,990 --> 00:23:08,000
key, which for their case is just called
 

1433
00:23:08,000 --> 00:23:10,549
key, which for their case is just called
a client ID, you can just go to this URL

1434
00:23:10,549 --> 00:23:10,559
a client ID, you can just go to this URL
 

1435
00:23:10,559 --> 00:23:12,549
a client ID, you can just go to this URL
in our course page. And when you go

1436
00:23:12,549 --> 00:23:12,559
in our course page. And when you go
 

1437
00:23:12,559 --> 00:23:14,630
in our course page. And when you go
there, you'll just be asked to sign in.

1438
00:23:14,630 --> 00:23:14,640
there, you'll just be asked to sign in.
 

1439
00:23:14,640 --> 00:23:16,390
there, you'll just be asked to sign in.
And once you're signed in, just go ahead

1440
00:23:16,390 --> 00:23:16,400
And once you're signed in, just go ahead
 

1441
00:23:16,400 --> 00:23:18,630
And once you're signed in, just go ahead
and go to that URL again. And this time,

1442
00:23:18,630 --> 00:23:18,640
and go to that URL again. And this time,
 

1443
00:23:18,640 --> 00:23:20,070
and go to that URL again. And this time,
you'll be able to register an

1444
00:23:20,070 --> 00:23:20,080
you'll be able to register an
 

1445
00:23:20,080 --> 00:23:21,669
you'll be able to register an
application, which you can just fill up

1446
00:23:21,669 --> 00:23:21,679
application, which you can just fill up
 

1447
00:23:21,679 --> 00:23:23,510
application, which you can just fill up
here in this page. And in terms of what

1448
00:23:23,510 --> 00:23:23,520
here in this page. And in terms of what
 

1449
00:23:23,520 --> 00:23:25,029
here in this page. And in terms of what
you should put here, for the application

1450
00:23:25,029 --> 00:23:25,039
you should put here, for the application
 

1451
00:23:25,039 --> 00:23:26,470
you should put here, for the application
name, you can just name it whatever you

1452
00:23:26,470 --> 00:23:26,480
name, you can just name it whatever you
 

1453
00:23:26,480 --> 00:23:28,310
name, you can just name it whatever you
want. For the authorization type, choose

1454
00:23:28,310 --> 00:23:28,320
want. For the authorization type, choose
 

1455
00:23:28,320 --> 00:23:30,070
want. For the authorization type, choose
this one, anonymous usage. And the

1456
00:23:30,070 --> 00:23:30,080
this one, anonymous usage. And the
 

1457
00:23:30,080 --> 00:23:33,350
this one, anonymous usage. And the
callback URL can be this HTTPS local

1458
00:23:33,350 --> 00:23:33,360
callback URL can be this HTTPS local
 

1459
00:23:33,360 --> 00:23:35,270
callback URL can be this HTTPS local
host. And once you submit that, you will

1460
00:23:35,270 --> 00:23:35,280
host. And once you submit that, you will
 

1461
00:23:35,280 --> 00:23:37,110
host. And once you submit that, you will
immediately receive a client ID, which

1462
00:23:37,110 --> 00:23:37,120
immediately receive a client ID, which
 

1463
00:23:37,120 --> 00:23:38,950
immediately receive a client ID, which
you can just copy here. And from within

1464
00:23:38,950 --> 00:23:38,960
you can just copy here. And from within
 

1465
00:23:38,960 --> 00:23:41,029
you can just copy here. And from within
your create URL node, toggle send

1466
00:23:41,029 --> 00:23:41,039
your create URL node, toggle send
 

1467
00:23:41,039 --> 00:23:43,350
your create URL node, toggle send
headers and place that client ID under

1468
00:23:43,350 --> 00:23:43,360
headers and place that client ID under
 

1469
00:23:43,360 --> 00:23:45,750
headers and place that client ID under
the value attribute. And same as before,

1470
00:23:45,750 --> 00:23:45,760
the value attribute. And same as before,
 

1471
00:23:45,760 --> 00:23:48,149
the value attribute. And same as before,
just type in authorization without

1472
00:23:48,149 --> 00:23:48,159
just type in authorization without
 

1473
00:23:48,159 --> 00:23:50,630
just type in authorization without
spaces, capital A there. And then here,

1474
00:23:50,630 --> 00:23:50,640
spaces, capital A there. And then here,
 

1475
00:23:50,640 --> 00:23:54,149
spaces, capital A there. And then here,
preface it with client- ID space. So

1476
00:23:54,149 --> 00:23:54,159
preface it with client- ID space. So
 

1477
00:23:54,159 --> 00:23:55,990
preface it with client- ID space. So
this is just the format that image UR

1478
00:23:55,990 --> 00:23:56,000
this is just the format that image UR
 

1479
00:23:56,000 --> 00:23:58,230
this is just the format that image UR
accepts. So now if we do a test step

1480
00:23:58,230 --> 00:23:58,240
accepts. So now if we do a test step
 

1481
00:23:58,240 --> 00:23:59,909
accepts. So now if we do a test step
here, once that's done and you get a

1482
00:23:59,909 --> 00:23:59,919
here, once that's done and you get a
 

1483
00:23:59,919 --> 00:24:02,390
here, once that's done and you get a
status 200, which stands for success,

1484
00:24:02,390 --> 00:24:02,400
status 200, which stands for success,
 

1485
00:24:02,400 --> 00:24:04,230
status 200, which stands for success,
then you'll get three items with all of

1486
00:24:04,230 --> 00:24:04,240
then you'll get three items with all of
 

1487
00:24:04,240 --> 00:24:06,149
then you'll get three items with all of
these attributes. But really the only

1488
00:24:06,149 --> 00:24:06,159
these attributes. But really the only
 

1489
00:24:06,159 --> 00:24:08,310
these attributes. But really the only
thing we're after is this URL link,

1490
00:24:08,310 --> 00:24:08,320
thing we're after is this URL link,
 

1491
00:24:08,320 --> 00:24:10,070
thing we're after is this URL link,
which if you open in any tab, that will

1492
00:24:10,070 --> 00:24:10,080
which if you open in any tab, that will
 

1493
00:24:10,080 --> 00:24:12,230
which if you open in any tab, that will
lead to your images as well. So that's

1494
00:24:12,230 --> 00:24:12,240
lead to your images as well. So that's
 

1495
00:24:12,240 --> 00:24:14,070
lead to your images as well. So that's
good. And so now we can just upload this

1496
00:24:14,070 --> 00:24:14,080
good. And so now we can just upload this
 

1497
00:24:14,080 --> 00:24:16,310
good. And so now we can just upload this
to Blato. And so we can just go ahead

1498
00:24:16,310 --> 00:24:16,320
to Blato. And so we can just go ahead
 

1499
00:24:16,320 --> 00:24:18,630
to Blato. And so we can just go ahead
and create another HTTP request and

1500
00:24:18,630 --> 00:24:18,640
and create another HTTP request and
 

1501
00:24:18,640 --> 00:24:21,430
and create another HTTP request and
rename that to upload to BL. Change this

1502
00:24:21,430 --> 00:24:21,440
rename that to upload to BL. Change this
 

1503
00:24:21,440 --> 00:24:23,750
rename that to upload to BL. Change this
again to post. And then for the URL, you

1504
00:24:23,750 --> 00:24:23,760
again to post. And then for the URL, you
 

1505
00:24:23,760 --> 00:24:25,830
again to post. And then for the URL, you
can just copy this one from the course

1506
00:24:25,830 --> 00:24:25,840
can just copy this one from the course
 

1507
00:24:25,840 --> 00:24:27,590
can just copy this one from the course
page again and paste that there. Now,

1508
00:24:27,590 --> 00:24:27,600
page again and paste that there. Now,
 

1509
00:24:27,600 --> 00:24:29,669
page again and paste that there. Now,
for us to rotate, we of course need an

1510
00:24:29,669 --> 00:24:29,679
for us to rotate, we of course need an
 

1511
00:24:29,679 --> 00:24:31,590
for us to rotate, we of course need an
API key there as well. And so what you

1512
00:24:31,590 --> 00:24:31,600
API key there as well. And so what you
 

1513
00:24:31,600 --> 00:24:33,750
API key there as well. And so what you
can do so that it'll be easier for you

1514
00:24:33,750 --> 00:24:33,760
can do so that it'll be easier for you
 

1515
00:24:33,760 --> 00:24:35,909
can do so that it'll be easier for you
down the line is under authentication,

1516
00:24:35,909 --> 00:24:35,919
down the line is under authentication,
 

1517
00:24:35,919 --> 00:24:37,510
down the line is under authentication,
you can just choose generic credential

1518
00:24:37,510 --> 00:24:37,520
you can just choose generic credential
 

1519
00:24:37,520 --> 00:24:39,750
you can just choose generic credential
type. Select header O here, which is

1520
00:24:39,750 --> 00:24:39,760
type. Select header O here, which is
 

1521
00:24:39,760 --> 00:24:41,510
type. Select header O here, which is
just a type of authorization we'll do.

1522
00:24:41,510 --> 00:24:41,520
just a type of authorization we'll do.
 

1523
00:24:41,520 --> 00:24:43,190
just a type of authorization we'll do.
And then create a new credential. So

1524
00:24:43,190 --> 00:24:43,200
And then create a new credential. So
 

1525
00:24:43,200 --> 00:24:45,110
And then create a new credential. So
this one you can just rename as Blotato

1526
00:24:45,110 --> 00:24:45,120
this one you can just rename as Blotato
 

1527
00:24:45,120 --> 00:24:46,789
this one you can just rename as Blotato
credential. And you can see that it is

1528
00:24:46,789 --> 00:24:46,799
credential. And you can see that it is
 

1529
00:24:46,799 --> 00:24:48,710
credential. And you can see that it is
requesting for a name and value here.

1530
00:24:48,710 --> 00:24:48,720
requesting for a name and value here.
 

1531
00:24:48,720 --> 00:24:50,149
requesting for a name and value here.
And for you to get that, you just need

1532
00:24:50,149 --> 00:24:50,159
And for you to get that, you just need
 

1533
00:24:50,159 --> 00:24:52,470
And for you to get that, you just need
to head to blueato.com in this link and

1534
00:24:52,470 --> 00:24:52,480
to head to blueato.com in this link and
 

1535
00:24:52,480 --> 00:24:54,070
to head to blueato.com in this link and
sign up here so that you can try it out

1536
00:24:54,070 --> 00:24:54,080
sign up here so that you can try it out
 

1537
00:24:54,080 --> 00:24:55,430
sign up here so that you can try it out
for free. And by the way, if you're part

1538
00:24:55,430 --> 00:24:55,440
for free. And by the way, if you're part
 

1539
00:24:55,440 --> 00:24:57,029
for free. And by the way, if you're part
of the community, we do have a discount

1540
00:24:57,029 --> 00:24:57,039
of the community, we do have a discount
 

1541
00:24:57,039 --> 00:24:59,430
of the community, we do have a discount
code that will give you 30% off for life

1542
00:24:59,430 --> 00:24:59,440
code that will give you 30% off for life
 

1543
00:24:59,440 --> 00:25:01,110
code that will give you 30% off for life
on that software. So make sure to use

1544
00:25:01,110 --> 00:25:01,120
on that software. So make sure to use
 

1545
00:25:01,120 --> 00:25:02,630
on that software. So make sure to use
that before you sign up. And also, if

1546
00:25:02,630 --> 00:25:02,640
that before you sign up. And also, if
 

1547
00:25:02,640 --> 00:25:04,630
that before you sign up. And also, if
you haven't used Blotato before and want

1548
00:25:04,630 --> 00:25:04,640
you haven't used Blotato before and want
 

1549
00:25:04,640 --> 00:25:06,470
you haven't used Blotato before and want
to dive deeper into this tool, you can

1550
00:25:06,470 --> 00:25:06,480
to dive deeper into this tool, you can
 

1551
00:25:06,480 --> 00:25:08,630
to dive deeper into this tool, you can
watch this specific course because that

1552
00:25:08,630 --> 00:25:08,640
watch this specific course because that
 

1553
00:25:08,640 --> 00:25:10,070
watch this specific course because that
talks through using Blotato as a

1554
00:25:10,070 --> 00:25:10,080
talks through using Blotato as a
 

1555
00:25:10,080 --> 00:25:11,750
talks through using Blotato as a
publishing tool, which you don't need to

1556
00:25:11,750 --> 00:25:11,760
publishing tool, which you don't need to
 

1557
00:25:11,760 --> 00:25:13,430
publishing tool, which you don't need to
watch for this course specifically, but

1558
00:25:13,430 --> 00:25:13,440
watch for this course specifically, but
 

1559
00:25:13,440 --> 00:25:15,029
watch for this course specifically, but
that's just there if in case you want to

1560
00:25:15,029 --> 00:25:15,039
that's just there if in case you want to
 

1561
00:25:15,039 --> 00:25:17,750
that's just there if in case you want to
dive deeper into that system. So anyway,

1562
00:25:17,750 --> 00:25:17,760
dive deeper into that system. So anyway,
 

1563
00:25:17,760 --> 00:25:19,430
dive deeper into that system. So anyway,
once you've signed up to Blato, you just

1564
00:25:19,430 --> 00:25:19,440
once you've signed up to Blato, you just
 

1565
00:25:19,440 --> 00:25:21,510
once you've signed up to Blato, you just
want to go to the settings and here at

1566
00:25:21,510 --> 00:25:21,520
want to go to the settings and here at
 

1567
00:25:21,520 --> 00:25:23,269
want to go to the settings and here at
the bottom is where you can copy your

1568
00:25:23,269 --> 00:25:23,279
the bottom is where you can copy your
 

1569
00:25:23,279 --> 00:25:25,350
the bottom is where you can copy your
API key, which you can just go and paste

1570
00:25:25,350 --> 00:25:25,360
API key, which you can just go and paste
 

1571
00:25:25,360 --> 00:25:27,350
API key, which you can just go and paste
here under value and then for the name,

1572
00:25:27,350 --> 00:25:27,360
here under value and then for the name,
 

1573
00:25:27,360 --> 00:25:29,310
here under value and then for the name,
you want to type in

1574
00:25:29,310 --> 00:25:29,320
you want to type in
 

1575
00:25:29,320 --> 00:25:31,909
you want to type in
exactly API key. So that needs to be

1576
00:25:31,909 --> 00:25:31,919
exactly API key. So that needs to be
 

1577
00:25:31,919 --> 00:25:34,070
exactly API key. So that needs to be
exact because that is the syntax that

1578
00:25:34,070 --> 00:25:34,080
exact because that is the syntax that
 

1579
00:25:34,080 --> 00:25:36,070
exact because that is the syntax that
plot is accepting and just click on

1580
00:25:36,070 --> 00:25:36,080
plot is accepting and just click on
 

1581
00:25:36,080 --> 00:25:37,909
plot is accepting and just click on
save. So once you have that set up, you

1582
00:25:37,909 --> 00:25:37,919
save. So once you have that set up, you
 

1583
00:25:37,919 --> 00:25:39,510
save. So once you have that set up, you
just need to select that whenever you

1584
00:25:39,510 --> 00:25:39,520
just need to select that whenever you
 

1585
00:25:39,520 --> 00:25:42,070
just need to select that whenever you
use, which you'll also see later on. But

1586
00:25:42,070 --> 00:25:42,080
use, which you'll also see later on. But
 

1587
00:25:42,080 --> 00:25:43,750
use, which you'll also see later on. But
now that we have that set up, we just

1588
00:25:43,750 --> 00:25:43,760
now that we have that set up, we just
 

1589
00:25:43,760 --> 00:25:46,310
now that we have that set up, we just
need to toggle this send body as on. And

1590
00:25:46,310 --> 00:25:46,320
need to toggle this send body as on. And
 

1591
00:25:46,320 --> 00:25:48,470
need to toggle this send body as on. And
then the only thing you want here is to

1592
00:25:48,470 --> 00:25:48,480
then the only thing you want here is to
 

1593
00:25:48,480 --> 00:25:51,190
then the only thing you want here is to
send a field called URL which is again

1594
00:25:51,190 --> 00:25:51,200
send a field called URL which is again
 

1595
00:25:51,200 --> 00:25:53,590
send a field called URL which is again
directed by Blotato's specifications.

1596
00:25:53,590 --> 00:25:53,600
directed by Blotato's specifications.
 

1597
00:25:53,600 --> 00:25:55,909
directed by Blotato's specifications.
And under value, you want the URL coming

1598
00:25:55,909 --> 00:25:55,919
And under value, you want the URL coming
 

1599
00:25:55,919 --> 00:25:58,230
And under value, you want the URL coming
from your previous node, which is the

1600
00:25:58,230 --> 00:25:58,240
from your previous node, which is the
 

1601
00:25:58,240 --> 00:26:00,230
from your previous node, which is the
one here. So you can just drag that and

1602
00:26:00,230 --> 00:26:00,240
one here. So you can just drag that and
 

1603
00:26:00,240 --> 00:26:02,070
one here. So you can just drag that and
put that there. So now if we hit test

1604
00:26:02,070 --> 00:26:02,080
put that there. So now if we hit test
 

1605
00:26:02,080 --> 00:26:03,909
put that there. So now if we hit test
step here and once that's done it will

1606
00:26:03,909 --> 00:26:03,919
step here and once that's done it will
 

1607
00:26:03,919 --> 00:26:06,149
step here and once that's done it will
give you three items which are all these

1608
00:26:06,149 --> 00:26:06,159
give you three items which are all these
 

1609
00:26:06,159 --> 00:26:08,789
give you three items which are all these
URLs that would all be your images but

1610
00:26:08,789 --> 00:26:08,799
URLs that would all be your images but
 

1611
00:26:08,799 --> 00:26:10,870
URLs that would all be your images but
this time they're just uploaded to

1612
00:26:10,870 --> 00:26:10,880
this time they're just uploaded to
 

1613
00:26:10,880 --> 00:26:12,950
this time they're just uploaded to
servers which is a requirement when

1614
00:26:12,950 --> 00:26:12,960
servers which is a requirement when
 

1615
00:26:12,960 --> 00:26:14,710
servers which is a requirement when
posting to different social channels

1616
00:26:14,710 --> 00:26:14,720
posting to different social channels
 

1617
00:26:14,720 --> 00:26:16,310
posting to different social channels
because for most of these social media

1618
00:26:16,310 --> 00:26:16,320
because for most of these social media
 

1619
00:26:16,320 --> 00:26:18,149
because for most of these social media
platforms like Tik Tok or Instagram

1620
00:26:18,149 --> 00:26:18,159
platforms like Tik Tok or Instagram
 

1621
00:26:18,159 --> 00:26:19,750
platforms like Tik Tok or Instagram
they're quite sensitive and they only

1622
00:26:19,750 --> 00:26:19,760
they're quite sensitive and they only
 

1623
00:26:19,760 --> 00:26:22,149
they're quite sensitive and they only
allow accredited thirdparty tools like

1624
00:26:22,149 --> 00:26:22,159
allow accredited thirdparty tools like
 

1625
00:26:22,159 --> 00:26:24,549
allow accredited thirdparty tools like
potato to auto post to their platforms.

1626
00:26:24,549 --> 00:26:24,559
potato to auto post to their platforms.
 

1627
00:26:24,559 --> 00:26:26,470
potato to auto post to their platforms.
So now we're almost done with this

1628
00:26:26,470 --> 00:26:26,480
So now we're almost done with this
 

1629
00:26:26,480 --> 00:26:28,070
So now we're almost done with this
second part, but the last thing that you

1630
00:26:28,070 --> 00:26:28,080
second part, but the last thing that you
 

1631
00:26:28,080 --> 00:26:29,990
second part, but the last thing that you
just want is to add a code node. And

1632
00:26:29,990 --> 00:26:30,000
just want is to add a code node. And
 

1633
00:26:30,000 --> 00:26:31,190
just want is to add a code node. And
you'll see in a bit why this is

1634
00:26:31,190 --> 00:26:31,200
you'll see in a bit why this is
 

1635
00:26:31,200 --> 00:26:33,269
you'll see in a bit why this is
important. And just name this into

1636
00:26:33,269 --> 00:26:33,279
important. And just name this into
 

1637
00:26:33,279 --> 00:26:34,870
important. And just name this into
combine. And just remove this

1638
00:26:34,870 --> 00:26:34,880
combine. And just remove this
 

1639
00:26:34,880 --> 00:26:36,390
combine. And just remove this
placeholder. And it's called combine

1640
00:26:36,390 --> 00:26:36,400
placeholder. And it's called combine
 

1641
00:26:36,400 --> 00:26:38,149
placeholder. And it's called combine
because all that we are going to do is

1642
00:26:38,149 --> 00:26:38,159
because all that we are going to do is
 

1643
00:26:38,159 --> 00:26:40,390
because all that we are going to do is
to take these three items and combine

1644
00:26:40,390 --> 00:26:40,400
to take these three items and combine
 

1645
00:26:40,400 --> 00:26:42,950
to take these three items and combine
them once again into one item so that we

1646
00:26:42,950 --> 00:26:42,960
them once again into one item so that we
 

1647
00:26:42,960 --> 00:26:45,029
them once again into one item so that we
can properly publish it as just one

1648
00:26:45,029 --> 00:26:45,039
can properly publish it as just one
 

1649
00:26:45,039 --> 00:26:47,830
can properly publish it as just one
carousel post in Tik Tok. So here in our

1650
00:26:47,830 --> 00:26:47,840
carousel post in Tik Tok. So here in our
 

1651
00:26:47,840 --> 00:26:49,430
carousel post in Tik Tok. So here in our
community, you can just copy that whole

1652
00:26:49,430 --> 00:26:49,440
community, you can just copy that whole
 

1653
00:26:49,440 --> 00:26:51,590
community, you can just copy that whole
script, place that there, and then click

1654
00:26:51,590 --> 00:26:51,600
script, place that there, and then click
 

1655
00:26:51,600 --> 00:26:53,990
script, place that there, and then click
on test. And all that did is to list

1656
00:26:53,990 --> 00:26:54,000
on test. And all that did is to list
 

1657
00:26:54,000 --> 00:26:56,789
on test. And all that did is to list
down these media URLs that we created in

1658
00:26:56,789 --> 00:26:56,799
down these media URLs that we created in
 

1659
00:26:56,799 --> 00:26:58,310
down these media URLs that we created in
the previous step and just combine them

1660
00:26:58,310 --> 00:26:58,320
the previous step and just combine them
 

1661
00:26:58,320 --> 00:27:00,470
the previous step and just combine them
into one item. And there you go. So if

1662
00:27:00,470 --> 00:27:00,480
into one item. And there you go. So if
 

1663
00:27:00,480 --> 00:27:01,909
into one item. And there you go. So if
you just want to clean that up, you now

1664
00:27:01,909 --> 00:27:01,919
you just want to clean that up, you now
 

1665
00:27:01,919 --> 00:27:04,230
you just want to clean that up, you now
have part two fully done. So now in our

1666
00:27:04,230 --> 00:27:04,240
have part two fully done. So now in our
 

1667
00:27:04,240 --> 00:27:06,470
have part two fully done. So now in our
very final step, we only need two nodes,

1668
00:27:06,470 --> 00:27:06,480
very final step, we only need two nodes,
 

1669
00:27:06,480 --> 00:27:08,390
very final step, we only need two nodes,
which is this Google sheet node as well

1670
00:27:08,390 --> 00:27:08,400
which is this Google sheet node as well
 

1671
00:27:08,400 --> 00:27:10,630
which is this Google sheet node as well
as this Tik Tok publishing node. And so

1672
00:27:10,630 --> 00:27:10,640
as this Tik Tok publishing node. And so
 

1673
00:27:10,640 --> 00:27:12,230
as this Tik Tok publishing node. And so
we'll set that up next. Because

1674
00:27:12,230 --> 00:27:12,240
we'll set that up next. Because
 

1675
00:27:12,240 --> 00:27:14,070
we'll set that up next. Because
actually, if you extend this and look

1676
00:27:14,070 --> 00:27:14,080
actually, if you extend this and look
 

1677
00:27:14,080 --> 00:27:16,149
actually, if you extend this and look
for Google Sheets, you can just select

1678
00:27:16,149 --> 00:27:16,159
for Google Sheets, you can just select
 

1679
00:27:16,159 --> 00:27:19,190
for Google Sheets, you can just select
that and click on append row and sheet

1680
00:27:19,190 --> 00:27:19,200
that and click on append row and sheet
 

1681
00:27:19,200 --> 00:27:21,350
that and click on append row and sheet
as the action. This Google sheet node is

1682
00:27:21,350 --> 00:27:21,360
as the action. This Google sheet node is
 

1683
00:27:21,360 --> 00:27:23,110
as the action. This Google sheet node is
honestly an optional step, but it's just

1684
00:27:23,110 --> 00:27:23,120
honestly an optional step, but it's just
 

1685
00:27:23,120 --> 00:27:25,830
honestly an optional step, but it's just
good practice so that these URLs that

1686
00:27:25,830 --> 00:27:25,840
good practice so that these URLs that
 

1687
00:27:25,840 --> 00:27:28,230
good practice so that these URLs that
you created for this specific run can be

1688
00:27:28,230 --> 00:27:28,240
you created for this specific run can be
 

1689
00:27:28,240 --> 00:27:29,669
you created for this specific run can be
stored in a spreadsheet somewhere so

1690
00:27:29,669 --> 00:27:29,679
stored in a spreadsheet somewhere so
 

1691
00:27:29,679 --> 00:27:31,350
stored in a spreadsheet somewhere so
that you can get back to it if in case

1692
00:27:31,350 --> 00:27:31,360
that you can get back to it if in case
 

1693
00:27:31,360 --> 00:27:32,950
that you can get back to it if in case
you want to, let's say, upscale those

1694
00:27:32,950 --> 00:27:32,960
you want to, let's say, upscale those
 

1695
00:27:32,960 --> 00:27:35,430
you want to, let's say, upscale those
images as posters and even sell them as

1696
00:27:35,430 --> 00:27:35,440
images as posters and even sell them as
 

1697
00:27:35,440 --> 00:27:37,350
images as posters and even sell them as
digital art down the track. So to do

1698
00:27:37,350 --> 00:27:37,360
digital art down the track. So to do
 

1699
00:27:37,360 --> 00:27:38,950
digital art down the track. So to do
this, just go ahead and connect your

1700
00:27:38,950 --> 00:27:38,960
this, just go ahead and connect your
 

1701
00:27:38,960 --> 00:27:40,390
this, just go ahead and connect your
Google Sheets by creating a new

1702
00:27:40,390 --> 00:27:40,400
Google Sheets by creating a new
 

1703
00:27:40,400 --> 00:27:42,710
Google Sheets by creating a new
credential and using N8N's easy signin

1704
00:27:42,710 --> 00:27:42,720
credential and using N8N's easy signin
 

1705
00:27:42,720 --> 00:27:44,230
credential and using N8N's easy signin
with Google button here. And then what

1706
00:27:44,230 --> 00:27:44,240
with Google button here. And then what
 

1707
00:27:44,240 --> 00:27:46,549
with Google button here. And then what
you want to do is to find your template

1708
00:27:46,549 --> 00:27:46,559
you want to do is to find your template
 

1709
00:27:46,559 --> 00:27:48,549
you want to do is to find your template
document from this list. And if you need

1710
00:27:48,549 --> 00:27:48,559
document from this list. And if you need
 

1711
00:27:48,559 --> 00:27:50,549
document from this list. And if you need
a template, what we've done is make it

1712
00:27:50,549 --> 00:27:50,559
a template, what we've done is make it
 

1713
00:27:50,559 --> 00:27:53,029
a template, what we've done is make it
easy by creating one for you. So if we

1714
00:27:53,029 --> 00:27:53,039
easy by creating one for you. So if we
 

1715
00:27:53,039 --> 00:27:54,630
easy by creating one for you. So if we
click this link, you'll find there an

1716
00:27:54,630 --> 00:27:54,640
click this link, you'll find there an
 

1717
00:27:54,640 --> 00:27:56,149
click this link, you'll find there an
easy template. There's not too many

1718
00:27:56,149 --> 00:27:56,159
easy template. There's not too many
 

1719
00:27:56,159 --> 00:27:58,070
easy template. There's not too many
columns here, but to make a copy of it,

1720
00:27:58,070 --> 00:27:58,080
columns here, but to make a copy of it,
 

1721
00:27:58,080 --> 00:27:59,669
columns here, but to make a copy of it,
just head to file and click on make a

1722
00:27:59,669 --> 00:27:59,679
just head to file and click on make a
 

1723
00:27:59,679 --> 00:28:01,110
just head to file and click on make a
copy. And that will create a copy of

1724
00:28:01,110 --> 00:28:01,120
copy. And that will create a copy of
 

1725
00:28:01,120 --> 00:28:03,430
copy. And that will create a copy of
that same template in your Google Drive.

1726
00:28:03,430 --> 00:28:03,440
that same template in your Google Drive.
 

1727
00:28:03,440 --> 00:28:05,750
that same template in your Google Drive.
So now back in N8N, we can just search

1728
00:28:05,750 --> 00:28:05,760
So now back in N8N, we can just search
 

1729
00:28:05,760 --> 00:28:08,310
So now back in N8N, we can just search
for that sheet, select that, and also

1730
00:28:08,310 --> 00:28:08,320
for that sheet, select that, and also
 

1731
00:28:08,320 --> 00:28:11,110
for that sheet, select that, and also
choose the tab or the sheet itself. And

1732
00:28:11,110 --> 00:28:11,120
choose the tab or the sheet itself. And
 

1733
00:28:11,120 --> 00:28:12,710
choose the tab or the sheet itself. And
for me, it would be sheet one. And

1734
00:28:12,710 --> 00:28:12,720
for me, it would be sheet one. And
 

1735
00:28:12,720 --> 00:28:14,630
for me, it would be sheet one. And
you'll see N8N already mapped these

1736
00:28:14,630 --> 00:28:14,640
you'll see N8N already mapped these
 

1737
00:28:14,640 --> 00:28:16,789
you'll see N8N already mapped these
columns automatically. And so what you

1738
00:28:16,789 --> 00:28:16,799
columns automatically. And so what you
 

1739
00:28:16,799 --> 00:28:19,110
columns automatically. And so what you
can do is to just drag these inputs at

1740
00:28:19,110 --> 00:28:19,120
can do is to just drag these inputs at
 

1741
00:28:19,120 --> 00:28:22,070
can do is to just drag these inputs at
the left into the image URLs so that you

1742
00:28:22,070 --> 00:28:22,080
the left into the image URLs so that you
 

1743
00:28:22,080 --> 00:28:23,990
the left into the image URLs so that you
store them properly. And then under the

1744
00:28:23,990 --> 00:28:24,000
store them properly. And then under the
 

1745
00:28:24,000 --> 00:28:25,909
store them properly. And then under the
title and caption, these are again just

1746
00:28:25,909 --> 00:28:25,919
title and caption, these are again just
 

1747
00:28:25,919 --> 00:28:28,389
title and caption, these are again just
for tracking. You can just go and find

1748
00:28:28,389 --> 00:28:28,399
for tracking. You can just go and find
 

1749
00:28:28,399 --> 00:28:30,630
for tracking. You can just go and find
the output of your very first agent

1750
00:28:30,630 --> 00:28:30,640
the output of your very first agent
 

1751
00:28:30,640 --> 00:28:32,710
the output of your very first agent
because this is where the title and the

1752
00:28:32,710 --> 00:28:32,720
because this is where the title and the
 

1753
00:28:32,720 --> 00:28:34,870
because this is where the title and the
caption is. And you can see it's red

1754
00:28:34,870 --> 00:28:34,880
caption is. And you can see it's red
 

1755
00:28:34,880 --> 00:28:36,549
caption is. And you can see it's red
here just because we're working with

1756
00:28:36,549 --> 00:28:36,559
here just because we're working with
 

1757
00:28:36,559 --> 00:28:39,029
here just because we're working with
multiple items on different stages of

1758
00:28:39,029 --> 00:28:39,039
multiple items on different stages of
 

1759
00:28:39,039 --> 00:28:41,510
multiple items on different stages of
running this workflow. So to fix this,

1760
00:28:41,510 --> 00:28:41,520
running this workflow. So to fix this,
 

1761
00:28:41,520 --> 00:28:44,149
running this workflow. So to fix this,
you can just replace this item word into

1762
00:28:44,149 --> 00:28:44,159
you can just replace this item word into
 

1763
00:28:44,159 --> 00:28:46,710
you can just replace this item word into
first and an open and close parenthesis.

1764
00:28:46,710 --> 00:28:46,720
first and an open and close parenthesis.
 

1765
00:28:46,720 --> 00:28:48,710
first and an open and close parenthesis.
So that will basically just get the very

1766
00:28:48,710 --> 00:28:48,720
So that will basically just get the very
 

1767
00:28:48,720 --> 00:28:50,950
So that will basically just get the very
first item, which is what you need

1768
00:28:50,950 --> 00:28:50,960
first item, which is what you need
 

1769
00:28:50,960 --> 00:28:52,950
first item, which is what you need
anyway. So do that also for the caption

1770
00:28:52,950 --> 00:28:52,960
anyway. So do that also for the caption
 

1771
00:28:52,960 --> 00:28:54,389
anyway. So do that also for the caption
and that should be corrected. And now

1772
00:28:54,389 --> 00:28:54,399
and that should be corrected. And now
 

1773
00:28:54,399 --> 00:28:56,149
and that should be corrected. And now
for the date published, what you want

1774
00:28:56,149 --> 00:28:56,159
for the date published, what you want
 

1775
00:28:56,159 --> 00:28:58,710
for the date published, what you want
here is a dynamic script that will just

1776
00:28:58,710 --> 00:28:58,720
here is a dynamic script that will just
 

1777
00:28:58,720 --> 00:29:00,630
here is a dynamic script that will just
return today's date. If you just head to

1778
00:29:00,630 --> 00:29:00,640
return today's date. If you just head to
 

1779
00:29:00,640 --> 00:29:02,789
return today's date. If you just head to
the step three page in our community,

1780
00:29:02,789 --> 00:29:02,799
the step three page in our community,
 

1781
00:29:02,799 --> 00:29:04,710
the step three page in our community,
you can just copy this, change this to

1782
00:29:04,710 --> 00:29:04,720
you can just copy this, change this to
 

1783
00:29:04,720 --> 00:29:06,630
you can just copy this, change this to
expression, and paste it there. And

1784
00:29:06,630 --> 00:29:06,640
expression, and paste it there. And
 

1785
00:29:06,640 --> 00:29:08,230
expression, and paste it there. And
you'll see that it's returning today's

1786
00:29:08,230 --> 00:29:08,240
you'll see that it's returning today's
 

1787
00:29:08,240 --> 00:29:10,070
you'll see that it's returning today's
date. And so now the only remaining

1788
00:29:10,070 --> 00:29:10,080
date. And so now the only remaining
 

1789
00:29:10,080 --> 00:29:11,750
date. And so now the only remaining
column we haven't filled up here is this

1790
00:29:11,750 --> 00:29:11,760
column we haven't filled up here is this
 

1791
00:29:11,760 --> 00:29:13,669
column we haven't filled up here is this
ID column. And for this one, what I like

1792
00:29:13,669 --> 00:29:13,679
ID column. And for this one, what I like
 

1793
00:29:13,679 --> 00:29:15,750
ID column. And for this one, what I like
to do is just type in a simple Excel

1794
00:29:15,750 --> 00:29:15,760
to do is just type in a simple Excel
 

1795
00:29:15,760 --> 00:29:18,310
to do is just type in a simple Excel
formula like so. And what this will do

1796
00:29:18,310 --> 00:29:18,320
formula like so. And what this will do
 

1797
00:29:18,320 --> 00:29:20,870
formula like so. And what this will do
if I click on test step is that it will

1798
00:29:20,870 --> 00:29:20,880
if I click on test step is that it will
 

1799
00:29:20,880 --> 00:29:23,190
if I click on test step is that it will
append one row in our sheet which if we

1800
00:29:23,190 --> 00:29:23,200
append one row in our sheet which if we
 

1801
00:29:23,200 --> 00:29:24,789
append one row in our sheet which if we
go back to that you can see it just

1802
00:29:24,789 --> 00:29:24,799
go back to that you can see it just
 

1803
00:29:24,799 --> 00:29:26,470
go back to that you can see it just
appended that along with all the values

1804
00:29:26,470 --> 00:29:26,480
appended that along with all the values
 

1805
00:29:26,480 --> 00:29:28,310
appended that along with all the values
that we got for this run. And you'll see

1806
00:29:28,310 --> 00:29:28,320
that we got for this run. And you'll see
 

1807
00:29:28,320 --> 00:29:30,710
that we got for this run. And you'll see
that the ID is reflecting properly. This

1808
00:29:30,710 --> 00:29:30,720
that the ID is reflecting properly. This
 

1809
00:29:30,720 --> 00:29:32,470
that the ID is reflecting properly. This
is basically just getting the row number

1810
00:29:32,470 --> 00:29:32,480
is basically just getting the row number
 

1811
00:29:32,480 --> 00:29:35,430
is basically just getting the row number
for this row minus one. So 2 - 1 equals

1812
00:29:35,430 --> 00:29:35,440
for this row minus one. So 2 - 1 equals
 

1813
00:29:35,440 --> 00:29:37,190
for this row minus one. So 2 - 1 equals
1 in that case. And so the next ones

1814
00:29:37,190 --> 00:29:37,200
1 in that case. And so the next ones
 

1815
00:29:37,200 --> 00:29:39,029
1 in that case. And so the next ones
will be filling up as the automation

1816
00:29:39,029 --> 00:29:39,039
will be filling up as the automation
 

1817
00:29:39,039 --> 00:29:41,190
will be filling up as the automation
runs continuously. So that's just good

1818
00:29:41,190 --> 00:29:41,200
runs continuously. So that's just good
 

1819
00:29:41,200 --> 00:29:43,669
runs continuously. So that's just good
for logging things in a spreadsheet. But

1820
00:29:43,669 --> 00:29:43,679
for logging things in a spreadsheet. But
 

1821
00:29:43,679 --> 00:29:45,269
for logging things in a spreadsheet. But
now the actual step that we need is to

1822
00:29:45,269 --> 00:29:45,279
now the actual step that we need is to
 

1823
00:29:45,279 --> 00:29:47,029
now the actual step that we need is to
actually post this to Tik Tok. So the

1824
00:29:47,029 --> 00:29:47,039
actually post this to Tik Tok. So the
 

1825
00:29:47,039 --> 00:29:49,190
actually post this to Tik Tok. So the
way to do that is through another HTTP

1826
00:29:49,190 --> 00:29:49,200
way to do that is through another HTTP
 

1827
00:29:49,200 --> 00:29:50,950
way to do that is through another HTTP
request and you can just rename this to

1828
00:29:50,950 --> 00:29:50,960
request and you can just rename this to
 

1829
00:29:50,960 --> 00:29:53,029
request and you can just rename this to
Tik Tok. And once again we'll be posting

1830
00:29:53,029 --> 00:29:53,039
Tik Tok. And once again we'll be posting
 

1831
00:29:53,039 --> 00:29:55,029
Tik Tok. And once again we'll be posting
a request and we'll be using Blotato

1832
00:29:55,029 --> 00:29:55,039
a request and we'll be using Blotato
 

1833
00:29:55,039 --> 00:29:57,110
a request and we'll be using Blotato
again to do this but the URL this time

1834
00:29:57,110 --> 00:29:57,120
again to do this but the URL this time
 

1835
00:29:57,120 --> 00:29:59,029
again to do this but the URL this time
would be this piece which you can just

1836
00:29:59,029 --> 00:29:59,039
would be this piece which you can just
 

1837
00:29:59,039 --> 00:30:01,110
would be this piece which you can just
copy and place it there. And now under

1838
00:30:01,110 --> 00:30:01,120
copy and place it there. And now under
 

1839
00:30:01,120 --> 00:30:03,269
copy and place it there. And now under
authentication, you can just use your

1840
00:30:03,269 --> 00:30:03,279
authentication, you can just use your
 

1841
00:30:03,279 --> 00:30:05,830
authentication, you can just use your
header O authentication which you've set

1842
00:30:05,830 --> 00:30:05,840
header O authentication which you've set
 

1843
00:30:05,840 --> 00:30:07,830
header O authentication which you've set
up earlier and that just saves you a lot

1844
00:30:07,830 --> 00:30:07,840
up earlier and that just saves you a lot
 

1845
00:30:07,840 --> 00:30:09,590
up earlier and that just saves you a lot
of time. So now the only thing you need

1846
00:30:09,590 --> 00:30:09,600
of time. So now the only thing you need
 

1847
00:30:09,600 --> 00:30:12,549
of time. So now the only thing you need
to do is to configure the request that

1848
00:30:12,549 --> 00:30:12,559
to do is to configure the request that
 

1849
00:30:12,559 --> 00:30:14,710
to do is to configure the request that
you'll be making. So in this case, JSON

1850
00:30:14,710 --> 00:30:14,720
you'll be making. So in this case, JSON
 

1851
00:30:14,720 --> 00:30:17,430
you'll be making. So in this case, JSON
is what accepts. So you can just specify

1852
00:30:17,430 --> 00:30:17,440
is what accepts. So you can just specify
 

1853
00:30:17,440 --> 00:30:19,750
is what accepts. So you can just specify
the body using JSON to make it simpler.

1854
00:30:19,750 --> 00:30:19,760
the body using JSON to make it simpler.
 

1855
00:30:19,760 --> 00:30:21,590
the body using JSON to make it simpler.
And once again, you can just copy this

1856
00:30:21,590 --> 00:30:21,600
And once again, you can just copy this
 

1857
00:30:21,600 --> 00:30:23,990
And once again, you can just copy this
JSON script over at the community and

1858
00:30:23,990 --> 00:30:24,000
JSON script over at the community and
 

1859
00:30:24,000 --> 00:30:25,830
JSON script over at the community and
change that to expression and paste it

1860
00:30:25,830 --> 00:30:25,840
change that to expression and paste it
 

1861
00:30:25,840 --> 00:30:28,310
change that to expression and paste it
there. So now if I open that you'll see

1862
00:30:28,310 --> 00:30:28,320
there. So now if I open that you'll see
 

1863
00:30:28,320 --> 00:30:29,669
there. So now if I open that you'll see
that once again it's just a lot of

1864
00:30:29,669 --> 00:30:29,679
that once again it's just a lot of
 

1865
00:30:29,679 --> 00:30:32,470
that once again it's just a lot of
attributes that accepts. So if you're

1866
00:30:32,470 --> 00:30:32,480
attributes that accepts. So if you're
 

1867
00:30:32,480 --> 00:30:34,310
attributes that accepts. So if you're
using Tik Tok this may be familiar to

1868
00:30:34,310 --> 00:30:34,320
using Tik Tok this may be familiar to
 

1869
00:30:34,320 --> 00:30:36,070
using Tik Tok this may be familiar to
you already. But just to go through the

1870
00:30:36,070 --> 00:30:36,080
you already. But just to go through the
 

1871
00:30:36,080 --> 00:30:37,510
you already. But just to go through the
important pieces here there's an

1872
00:30:37,510 --> 00:30:37,520
important pieces here there's an
 

1873
00:30:37,520 --> 00:30:39,269
important pieces here there's an
attribute here called auto add music

1874
00:30:39,269 --> 00:30:39,279
attribute here called auto add music
 

1875
00:30:39,279 --> 00:30:41,029
attribute here called auto add music
which is set to true. So that's great

1876
00:30:41,029 --> 00:30:41,039
which is set to true. So that's great
 

1877
00:30:41,039 --> 00:30:42,789
which is set to true. So that's great
because it will actually automatically

1878
00:30:42,789 --> 00:30:42,799
because it will actually automatically
 

1879
00:30:42,799 --> 00:30:45,029
because it will actually automatically
choose the music from Tik Tok's database

1880
00:30:45,029 --> 00:30:45,039
choose the music from Tik Tok's database
 

1881
00:30:45,039 --> 00:30:46,870
choose the music from Tik Tok's database
for you so that there's background music

1882
00:30:46,870 --> 00:30:46,880
for you so that there's background music
 

1883
00:30:46,880 --> 00:30:48,870
for you so that there's background music
for your post. And then for the content

1884
00:30:48,870 --> 00:30:48,880
for your post. And then for the content
 

1885
00:30:48,880 --> 00:30:50,470
for your post. And then for the content
you have here the text which would be

1886
00:30:50,470 --> 00:30:50,480
you have here the text which would be
 

1887
00:30:50,480 --> 00:30:52,149
you have here the text which would be
the caption. So you can see we're

1888
00:30:52,149 --> 00:30:52,159
the caption. So you can see we're
 

1889
00:30:52,159 --> 00:30:54,149
the caption. So you can see we're
mapping it on the output of our style

1890
00:30:54,149 --> 00:30:54,159
mapping it on the output of our style
 

1891
00:30:54,159 --> 00:30:55,669
mapping it on the output of our style
agent here, which by the way, if you

1892
00:30:55,669 --> 00:30:55,679
agent here, which by the way, if you
 

1893
00:30:55,679 --> 00:30:57,510
agent here, which by the way, if you
haven't renamed that, this may come up

1894
00:30:57,510 --> 00:30:57,520
haven't renamed that, this may come up
 

1895
00:30:57,520 --> 00:30:59,190
haven't renamed that, this may come up
as red to you. So you just need to

1896
00:30:59,190 --> 00:30:59,200
as red to you. So you just need to
 

1897
00:30:59,200 --> 00:31:00,950
as red to you. So you just need to
either rename your style agent or

1898
00:31:00,950 --> 00:31:00,960
either rename your style agent or
 

1899
00:31:00,960 --> 00:31:02,710
either rename your style agent or
alternatively find that caption

1900
00:31:02,710 --> 00:31:02,720
alternatively find that caption
 

1901
00:31:02,720 --> 00:31:04,950
alternatively find that caption
attribute here at the left and just drag

1902
00:31:04,950 --> 00:31:04,960
attribute here at the left and just drag
 

1903
00:31:04,960 --> 00:31:06,470
attribute here at the left and just drag
it in there. And then same with the

1904
00:31:06,470 --> 00:31:06,480
it in there. And then same with the
 

1905
00:31:06,480 --> 00:31:08,149
it in there. And then same with the
Google sheet, you can just change this

1906
00:31:08,149 --> 00:31:08,159
Google sheet, you can just change this
 

1907
00:31:08,159 --> 00:31:10,630
Google sheet, you can just change this
item to first and that should map

1908
00:31:10,630 --> 00:31:10,640
item to first and that should map
 

1909
00:31:10,640 --> 00:31:12,630
item to first and that should map
properly. And then for the media URL,

1910
00:31:12,630 --> 00:31:12,640
properly. And then for the media URL,
 

1911
00:31:12,640 --> 00:31:14,389
properly. And then for the media URL,
you can see that we are just uploading

1912
00:31:14,389 --> 00:31:14,399
you can see that we are just uploading
 

1913
00:31:14,399 --> 00:31:16,470
you can see that we are just uploading
those pictures here one by one in this

1914
00:31:16,470 --> 00:31:16,480
those pictures here one by one in this
 

1915
00:31:16,480 --> 00:31:18,549
those pictures here one by one in this
array format, which is basically like a

1916
00:31:18,549 --> 00:31:18,559
array format, which is basically like a
 

1917
00:31:18,559 --> 00:31:20,870
array format, which is basically like a
list. And finally, very important, this

1918
00:31:20,870 --> 00:31:20,880
list. And finally, very important, this
 

1919
00:31:20,880 --> 00:31:23,269
list. And finally, very important, this
account ID, you should actually change

1920
00:31:23,269 --> 00:31:23,279
account ID, you should actually change
 

1921
00:31:23,279 --> 00:31:25,190
account ID, you should actually change
for yourself because right now this is

1922
00:31:25,190 --> 00:31:25,200
for yourself because right now this is
 

1923
00:31:25,200 --> 00:31:27,510
for yourself because right now this is
just a placeholder. And the value here

1924
00:31:27,510 --> 00:31:27,520
just a placeholder. And the value here
 

1925
00:31:27,520 --> 00:31:29,510
just a placeholder. And the value here
should actually point to your Tik Tok

1926
00:31:29,510 --> 00:31:29,520
should actually point to your Tik Tok
 

1927
00:31:29,520 --> 00:31:31,669
should actually point to your Tik Tok
account instead of the placeholder. So

1928
00:31:31,669 --> 00:31:31,679
account instead of the placeholder. So
 

1929
00:31:31,679 --> 00:31:33,430
account instead of the placeholder. So
for you to get that, very simple. We're

1930
00:31:33,430 --> 00:31:33,440
for you to get that, very simple. We're
 

1931
00:31:33,440 --> 00:31:35,350
for you to get that, very simple. We're
again just in Blotato settings. You can

1932
00:31:35,350 --> 00:31:35,360
again just in Blotato settings. You can
 

1933
00:31:35,360 --> 00:31:37,029
again just in Blotato settings. You can
just log in with Tik Tok here. And that

1934
00:31:37,029 --> 00:31:37,039
just log in with Tik Tok here. And that
 

1935
00:31:37,039 --> 00:31:38,710
just log in with Tik Tok here. And that
would connect Blato to your Tik Tok

1936
00:31:38,710 --> 00:31:38,720
would connect Blato to your Tik Tok
 

1937
00:31:38,720 --> 00:31:39,990
would connect Blato to your Tik Tok
account. And once you get that

1938
00:31:39,990 --> 00:31:40,000
account. And once you get that
 

1939
00:31:40,000 --> 00:31:41,669
account. And once you get that
connected, you can just go ahead and

1940
00:31:41,669 --> 00:31:41,679
connected, you can just go ahead and
 

1941
00:31:41,679 --> 00:31:44,230
connected, you can just go ahead and
click copy account ID here and paste it

1942
00:31:44,230 --> 00:31:44,240
click copy account ID here and paste it
 

1943
00:31:44,240 --> 00:31:46,630
click copy account ID here and paste it
here in this area. So now if you've set

1944
00:31:46,630 --> 00:31:46,640
here in this area. So now if you've set
 

1945
00:31:46,640 --> 00:31:48,630
here in this area. So now if you've set
that up successfully, what you can do is

1946
00:31:48,630 --> 00:31:48,640
that up successfully, what you can do is
 

1947
00:31:48,640 --> 00:31:50,789
that up successfully, what you can do is
to click test step. You'll be receiving

1948
00:31:50,789 --> 00:31:50,799
to click test step. You'll be receiving
 

1949
00:31:50,799 --> 00:31:52,789
to click test step. You'll be receiving
this post submission ID which tells you

1950
00:31:52,789 --> 00:31:52,799
this post submission ID which tells you
 

1951
00:31:52,799 --> 00:31:54,549
this post submission ID which tells you
that it's been submitted. And over at

1952
00:31:54,549 --> 00:31:54,559
that it's been submitted. And over at
 

1953
00:31:54,559 --> 00:31:57,350
that it's been submitted. And over at
Blotato, if you now go to publish posts,

1954
00:31:57,350 --> 00:31:57,360
Blotato, if you now go to publish posts,
 

1955
00:31:57,360 --> 00:31:59,509
Blotato, if you now go to publish posts,
you'll be able to check if that post got

1956
00:31:59,509 --> 00:31:59,519
you'll be able to check if that post got
 

1957
00:31:59,519 --> 00:32:01,190
you'll be able to check if that post got
submitted correctly. And if you preview

1958
00:32:01,190 --> 00:32:01,200
submitted correctly. And if you preview
 

1959
00:32:01,200 --> 00:32:03,190
submitted correctly. And if you preview
that over at Tik Tok, you'll see that we

1960
00:32:03,190 --> 00:32:03,200
that over at Tik Tok, you'll see that we
 

1961
00:32:03,200 --> 00:32:05,190
that over at Tik Tok, you'll see that we
have those three images here and even a

1962
00:32:05,190 --> 00:32:05,200
have those three images here and even a
 

1963
00:32:05,200 --> 00:32:07,110
have those three images here and even a
background music automatically selected

1964
00:32:07,110 --> 00:32:07,120
background music automatically selected
 

1965
00:32:07,120 --> 00:32:07,980
background music automatically selected
for

1966
00:32:07,980 --> 00:32:07,990
for
 

1967
00:32:07,990 --> 00:32:11,630
for
[Music]

1968
00:32:11,630 --> 00:32:11,640

 

1969
00:32:11,640 --> 00:32:13,909

you. So that was actually a very good

1970
00:32:13,909 --> 00:32:13,919
you. So that was actually a very good
 

1971
00:32:13,919 --> 00:32:15,590
you. So that was actually a very good
choice for the music. And you can see

1972
00:32:15,590 --> 00:32:15,600
choice for the music. And you can see
 

1973
00:32:15,600 --> 00:32:17,509
choice for the music. And you can see
the images here are also for the first

1974
00:32:17,509 --> 00:32:17,519
the images here are also for the first
 

1975
00:32:17,519 --> 00:32:19,430
the images here are also for the first
time adhering to the characters that we

1976
00:32:19,430 --> 00:32:19,440
time adhering to the characters that we
 

1977
00:32:19,440 --> 00:32:21,430
time adhering to the characters that we
want. And it is now so much easier

1978
00:32:21,430 --> 00:32:21,440
want. And it is now so much easier
 

1979
00:32:21,440 --> 00:32:23,750
want. And it is now so much easier
because of Chad GPT's image generation

1980
00:32:23,750 --> 00:32:23,760
because of Chad GPT's image generation
 

1981
00:32:23,760 --> 00:32:25,430
because of Chad GPT's image generation
model. So you can see we have a couple

1982
00:32:25,430 --> 00:32:25,440
model. So you can see we have a couple
 

1983
00:32:25,440 --> 00:32:27,430
model. So you can see we have a couple
more examples here. And then I got these

1984
00:32:27,430 --> 00:32:27,440
more examples here. And then I got these
 

1985
00:32:27,440 --> 00:32:29,190
more examples here. And then I got these
images because I actually left the

1986
00:32:29,190 --> 00:32:29,200
images because I actually left the
 

1987
00:32:29,200 --> 00:32:31,430
images because I actually left the
workflow to run every 10 minutes just to

1988
00:32:31,430 --> 00:32:31,440
workflow to run every 10 minutes just to
 

1989
00:32:31,440 --> 00:32:33,430
workflow to run every 10 minutes just to
see the images that it will come up with

1990
00:32:33,430 --> 00:32:33,440
see the images that it will come up with
 

1991
00:32:33,440 --> 00:32:34,630
see the images that it will come up with
and they're all pretty good. There's

1992
00:32:34,630 --> 00:32:34,640
and they're all pretty good. There's
 

1993
00:32:34,640 --> 00:32:36,389
and they're all pretty good. There's
even a few that are really of different

1994
00:32:36,389 --> 00:32:36,399
even a few that are really of different
 

1995
00:32:36,399 --> 00:32:38,310
even a few that are really of different
styles, which is all thanks to the

1996
00:32:38,310 --> 00:32:38,320
styles, which is all thanks to the
 

1997
00:32:38,320 --> 00:32:39,830
styles, which is all thanks to the
prompting mechanism that we set up

1998
00:32:39,830 --> 00:32:39,840
prompting mechanism that we set up
 

1999
00:32:39,840 --> 00:32:41,509
prompting mechanism that we set up
earlier. And so if you just continue to

2000
00:32:41,509 --> 00:32:41,519
earlier. And so if you just continue to
 

2001
00:32:41,519 --> 00:32:43,430
earlier. And so if you just continue to
test it out, you'll be able to generate

2002
00:32:43,430 --> 00:32:43,440
test it out, you'll be able to generate
 

2003
00:32:43,440 --> 00:32:45,269
test it out, you'll be able to generate
completely different styles of

2004
00:32:45,269 --> 00:32:45,279
completely different styles of
 

2005
00:32:45,279 --> 00:32:46,950
completely different styles of
completely different characters every

2006
00:32:46,950 --> 00:32:46,960
completely different characters every
 

2007
00:32:46,960 --> 00:32:48,789
completely different characters every
time. And that's it. You now have built

2008
00:32:48,789 --> 00:32:48,799
time. And that's it. You now have built
 

2009
00:32:48,799 --> 00:32:51,509
time. And that's it. You now have built
this Tik Tok AI digital art factory. And

2010
00:32:51,509 --> 00:32:51,519
this Tik Tok AI digital art factory. And
 

2011
00:32:51,519 --> 00:32:53,029
this Tik Tok AI digital art factory. And
if you look into our framework from

2012
00:32:53,029 --> 00:32:53,039
if you look into our framework from
 

2013
00:32:53,039 --> 00:32:54,389
if you look into our framework from
earlier, you can see that there are

2014
00:32:54,389 --> 00:32:54,399
earlier, you can see that there are
 

2015
00:32:54,399 --> 00:32:56,389
earlier, you can see that there are
actually these optional nodes, which if

2016
00:32:56,389 --> 00:32:56,399
actually these optional nodes, which if
 

2017
00:32:56,399 --> 00:32:58,149
actually these optional nodes, which if
I zoom in, they just correspond to the

2018
00:32:58,149 --> 00:32:58,159
I zoom in, they just correspond to the
 

2019
00:32:58,159 --> 00:33:00,549
I zoom in, they just correspond to the
different social channels that can also

2020
00:33:00,549 --> 00:33:00,559
different social channels that can also
 

2021
00:33:00,559 --> 00:33:02,389
different social channels that can also
post. But I won't be going through them.

2022
00:33:02,389 --> 00:33:02,399
post. But I won't be going through them.
 

2023
00:33:02,399 --> 00:33:04,230
post. But I won't be going through them.
But if you have access to this template,

2024
00:33:04,230 --> 00:33:04,240
But if you have access to this template,
 

2025
00:33:04,240 --> 00:33:05,990
But if you have access to this template,
you can just select them and do a right

2026
00:33:05,990 --> 00:33:06,000
you can just select them and do a right
 

2027
00:33:06,000 --> 00:33:07,990
you can just select them and do a right
click in order to activate them using

2028
00:33:07,990 --> 00:33:08,000
click in order to activate them using
 

2029
00:33:08,000 --> 00:33:09,990
click in order to activate them using
this button. or if you just want to plug

2030
00:33:09,990 --> 00:33:10,000
this button. or if you just want to plug
 

2031
00:33:10,000 --> 00:33:11,830
this button. or if you just want to plug
them into the workflow that you just

2032
00:33:11,830 --> 00:33:11,840
them into the workflow that you just
 

2033
00:33:11,840 --> 00:33:13,909
them into the workflow that you just
created, you can just go ahead and copy

2034
00:33:13,909 --> 00:33:13,919
created, you can just go ahead and copy
 

2035
00:33:13,919 --> 00:33:15,990
created, you can just go ahead and copy
them and paste them here. And all you

2036
00:33:15,990 --> 00:33:16,000
them and paste them here. And all you
 

2037
00:33:16,000 --> 00:33:17,590
them and paste them here. And all you
need to do is to just connect those

2038
00:33:17,590 --> 00:33:17,600
need to do is to just connect those
 

2039
00:33:17,600 --> 00:33:19,990
need to do is to just connect those
social channels as succeeding steps

2040
00:33:19,990 --> 00:33:20,000
social channels as succeeding steps
 

2041
00:33:20,000 --> 00:33:21,350
social channels as succeeding steps
depending on which ones you want to

2042
00:33:21,350 --> 00:33:21,360
depending on which ones you want to
 

2043
00:33:21,360 --> 00:33:22,950
depending on which ones you want to
activate. So there you just learned how

2044
00:33:22,950 --> 00:33:22,960
activate. So there you just learned how
 

2045
00:33:22,960 --> 00:33:24,870
activate. So there you just learned how
to properly segment prompting for

2046
00:33:24,870 --> 00:33:24,880
to properly segment prompting for
 

2047
00:33:24,880 --> 00:33:27,190
to properly segment prompting for
different AI agents as well as how to

2048
00:33:27,190 --> 00:33:27,200
different AI agents as well as how to
 

2049
00:33:27,200 --> 00:33:29,669
different AI agents as well as how to
use the Chat GPD image generation model

2050
00:33:29,669 --> 00:33:29,679
use the Chat GPD image generation model
 

2051
00:33:29,679 --> 00:33:31,830
use the Chat GPD image generation model
to automatically create images and post

2052
00:33:31,830 --> 00:33:31,840
to automatically create images and post
 

2053
00:33:31,840 --> 00:33:33,750
to automatically create images and post
them over at Tik Tok and any social

2054
00:33:33,750 --> 00:33:33,760
them over at Tik Tok and any social
 

2055
00:33:33,760 --> 00:33:35,669
them over at Tik Tok and any social
channel of your choice. Now, if you're

2056
00:33:35,669 --> 00:33:35,679
channel of your choice. Now, if you're
 

2057
00:33:35,679 --> 00:33:37,590
channel of your choice. Now, if you're
looking to turn this into a content

2058
00:33:37,590 --> 00:33:37,600
looking to turn this into a content
 

2059
00:33:37,600 --> 00:33:39,430
looking to turn this into a content
business, what you can do is to build up

2060
00:33:39,430 --> 00:33:39,440
business, what you can do is to build up
 

2061
00:33:39,440 --> 00:33:41,190
business, what you can do is to build up
your Tik Tok account so that you can get

2062
00:33:41,190 --> 00:33:41,200
your Tik Tok account so that you can get
 

2063
00:33:41,200 --> 00:33:43,669
your Tik Tok account so that you can get
reach similar to this user. For example,

2064
00:33:43,669 --> 00:33:43,679
reach similar to this user. For example,
 

2065
00:33:43,679 --> 00:33:45,590
reach similar to this user. For example,
if I were them and I want to monetize,

2066
00:33:45,590 --> 00:33:45,600
if I were them and I want to monetize,
 

2067
00:33:45,600 --> 00:33:47,509
if I were them and I want to monetize,
what I would do is to utilize the link

2068
00:33:47,509 --> 00:33:47,519
what I would do is to utilize the link
 

2069
00:33:47,519 --> 00:33:49,110
what I would do is to utilize the link
in bio feature, which they're not even

2070
00:33:49,110 --> 00:33:49,120
in bio feature, which they're not even
 

2071
00:33:49,120 --> 00:33:50,630
in bio feature, which they're not even
doing at the moment, and use that link

2072
00:33:50,630 --> 00:33:50,640
doing at the moment, and use that link
 

2073
00:33:50,640 --> 00:33:52,630
doing at the moment, and use that link
in bio to direct them to your Etsy

2074
00:33:52,630 --> 00:33:52,640
in bio to direct them to your Etsy
 

2075
00:33:52,640 --> 00:33:54,070
in bio to direct them to your Etsy
store, for example, where you can just

2076
00:33:54,070 --> 00:33:54,080
store, for example, where you can just
 

2077
00:33:54,080 --> 00:33:56,070
store, for example, where you can just
upscale those images and sell them as

2078
00:33:56,070 --> 00:33:56,080
upscale those images and sell them as
 

2079
00:33:56,080 --> 00:33:57,830
upscale those images and sell them as
digital posters. And there's a lot of

2080
00:33:57,830 --> 00:33:57,840
digital posters. And there's a lot of
 

2081
00:33:57,840 --> 00:33:59,830
digital posters. And there's a lot of
examples of Etsy businesses doing this

2082
00:33:59,830 --> 00:33:59,840
examples of Etsy businesses doing this
 

2083
00:33:59,840 --> 00:34:01,430
examples of Etsy businesses doing this
if you just go ahead and search there.

2084
00:34:01,430 --> 00:34:01,440
if you just go ahead and search there.
 

2085
00:34:01,440 --> 00:34:03,350
if you just go ahead and search there.
Or alternatively, you can direct them to

2086
00:34:03,350 --> 00:34:03,360
Or alternatively, you can direct them to
 

2087
00:34:03,360 --> 00:34:04,950
Or alternatively, you can direct them to
Patreon where you can upload those

2088
00:34:04,950 --> 00:34:04,960
Patreon where you can upload those
 

2089
00:34:04,960 --> 00:34:07,590
Patreon where you can upload those
images as wallpaper kits and digital

2090
00:34:07,590 --> 00:34:07,600
images as wallpaper kits and digital
 

2091
00:34:07,600 --> 00:34:09,109
images as wallpaper kits and digital
illustrations. So you can see this one

2092
00:34:09,109 --> 00:34:09,119
illustrations. So you can see this one
 

2093
00:34:09,119 --> 00:34:11,190
illustrations. So you can see this one
for example, he does these illustrations

2094
00:34:11,190 --> 00:34:11,200
for example, he does these illustrations
 

2095
00:34:11,200 --> 00:34:12,869
for example, he does these illustrations
and he was able to build up this brand

2096
00:34:12,869 --> 00:34:12,879
and he was able to build up this brand
 

2097
00:34:12,879 --> 00:34:14,550
and he was able to build up this brand
so that he now has more than 2,000

2098
00:34:14,550 --> 00:34:14,560
so that he now has more than 2,000
 

2099
00:34:14,560 --> 00:34:16,629
so that he now has more than 2,000
members who are subscribed to his work

2100
00:34:16,629 --> 00:34:16,639
members who are subscribed to his work
 

2101
00:34:16,639 --> 00:34:18,950
members who are subscribed to his work
for these digital art. So those are just

2102
00:34:18,950 --> 00:34:18,960
for these digital art. So those are just
 

2103
00:34:18,960 --> 00:34:21,030
for these digital art. So those are just
some ideas that sooner or later will be

2104
00:34:21,030 --> 00:34:21,040
some ideas that sooner or later will be
 

2105
00:34:21,040 --> 00:34:23,270
some ideas that sooner or later will be
heading toward even for AI art because

2106
00:34:23,270 --> 00:34:23,280
heading toward even for AI art because
 

2107
00:34:23,280 --> 00:34:25,109
heading toward even for AI art because
of how good it's getting. So there, if

2108
00:34:25,109 --> 00:34:25,119
of how good it's getting. So there, if
 

2109
00:34:25,119 --> 00:34:27,190
of how good it's getting. So there, if
you like this lesson, then also like and

2110
00:34:27,190 --> 00:34:27,200
you like this lesson, then also like and
 

2111
00:34:27,200 --> 00:34:29,430
you like this lesson, then also like and
subscribe to the page. It helps support

2112
00:34:29,430 --> 00:34:29,440
subscribe to the page. It helps support
 

2113
00:34:29,440 --> 00:34:31,349
subscribe to the page. It helps support
this channel and the community as well.

2114
00:34:31,349 --> 00:34:31,359
this channel and the community as well.
 

2115
00:34:31,359 --> 00:34:32,790
this channel and the community as well.
And if in case you're not yet part of

2116
00:34:32,790 --> 00:34:32,800
And if in case you're not yet part of
 

2117
00:34:32,800 --> 00:34:34,629
And if in case you're not yet part of
the RoboNuggets community, check it out

2118
00:34:34,629 --> 00:34:34,639
the RoboNuggets community, check it out
 

2119
00:34:34,639 --> 00:34:35,750
the RoboNuggets community, check it out
just in the link in the video

2120
00:34:35,750 --> 00:34:35,760
just in the link in the video
 

2121
00:34:35,760 --> 00:34:37,190
just in the link in the video
description. We have tons of other

2122
00:34:37,190 --> 00:34:37,200
description. We have tons of other
 

2123
00:34:37,200 --> 00:34:39,109
description. We have tons of other
lessons here all around AI and

2124
00:34:39,109 --> 00:34:39,119
lessons here all around AI and
 

2125
00:34:39,119 --> 00:34:41,109
lessons here all around AI and
automation where we release new ones

2126
00:34:41,109 --> 00:34:41,119
automation where we release new ones
 

2127
00:34:41,119 --> 00:34:42,950
automation where we release new ones
almost every week. And all of these also

2128
00:34:42,950 --> 00:34:42,960
almost every week. And all of these also
 

2129
00:34:42,960 --> 00:34:44,470
almost every week. And all of these also
have the blueprints and templates

2130
00:34:44,470 --> 00:34:44,480
have the blueprints and templates
 

2131
00:34:44,480 --> 00:34:46,550
have the blueprints and templates
cleanly organized within each of them.

2132
00:34:46,550 --> 00:34:46,560
cleanly organized within each of them.
 

2133
00:34:46,560 --> 00:34:48,629
cleanly organized within each of them.
And we also have a growing network of AI

2134
00:34:48,629 --> 00:34:48,639
And we also have a growing network of AI
 

2135
00:34:48,639 --> 00:34:50,629
And we also have a growing network of AI
practitioners who are all sharing paid

2136
00:34:50,629 --> 00:34:50,639
practitioners who are all sharing paid
 

2137
00:34:50,639 --> 00:34:52,230
practitioners who are all sharing paid
opportunities here. Some are from me,

2138
00:34:52,230 --> 00:34:52,240
opportunities here. Some are from me,
 

2139
00:34:52,240 --> 00:34:53,829
opportunities here. Some are from me,
some are from the members themselves.

2140
00:34:53,829 --> 00:34:53,839
some are from the members themselves.
 

2141
00:34:53,839 --> 00:34:55,270
some are from the members themselves.
So, if you're looking for a partner in

2142
00:34:55,270 --> 00:34:55,280
So, if you're looking for a partner in
 

2143
00:34:55,280 --> 00:34:56,950
So, if you're looking for a partner in
this space, then this is a good way to

2144
00:34:56,950 --> 00:34:56,960
this space, then this is a good way to
 

2145
00:34:56,960 --> 00:34:58,790
this space, then this is a good way to
scout for one because if you just attend

2146
00:34:58,790 --> 00:34:58,800
scout for one because if you just attend
 

2147
00:34:58,800 --> 00:35:00,310
scout for one because if you just attend
one of our events, you'll be able to

2148
00:35:00,310 --> 00:35:00,320
one of our events, you'll be able to
 

2149
00:35:00,320 --> 00:35:02,310
one of our events, you'll be able to
exchange ideas plus network with these

2150
00:35:02,310 --> 00:35:02,320
exchange ideas plus network with these
 

2151
00:35:02,320 --> 00:35:04,230
exchange ideas plus network with these
AI practitioners across the globe as

2152
00:35:04,230 --> 00:35:04,240
AI practitioners across the globe as
 

2153
00:35:04,240 --> 00:35:06,069
AI practitioners across the globe as
well. So, if having that community

2154
00:35:06,069 --> 00:35:06,079
well. So, if having that community
 

2155
00:35:06,079 --> 00:35:07,990
well. So, if having that community
interests you and you want access to

2156
00:35:07,990 --> 00:35:08,000
interests you and you want access to
 

2157
00:35:08,000 --> 00:35:10,069
interests you and you want access to
more step-by-step tutorials, all with

2158
00:35:10,069 --> 00:35:10,079
more step-by-step tutorials, all with
 

2159
00:35:10,079 --> 00:35:11,990
more step-by-step tutorials, all with
ready to load automation blueprints

2160
00:35:11,990 --> 00:35:12,000
ready to load automation blueprints
 

2161
00:35:12,000 --> 00:35:13,750
ready to load automation blueprints
organized for you, then check out the

2162
00:35:13,750 --> 00:35:13,760
organized for you, then check out the
 

2163
00:35:13,760 --> 00:35:15,670
organized for you, then check out the
RoboNuggets community if you want to be

2164
00:35:15,670 --> 00:35:15,680
RoboNuggets community if you want to be
 

2165
00:35:15,680 --> 00:35:17,510
RoboNuggets community if you want to be
part of that movement. That's it for

2166
00:35:17,510 --> 00:35:17,520
part of that movement. That's it for
 

2167
00:35:17,520 --> 00:35:19,190
part of that movement. That's it for
this one. See you guys next time. Thank

2168
00:35:19,190 --> 00:35:19,200
this one. See you guys next time. Thank
 

2169
00:35:19,200 --> 00:35:22,200
this one. See you guys next time. Thank
you.

