1
00:00:00,160 --> 00:00:02,710

Let's try the calculator. Very pretty.

2
00:00:02,710 --> 00:00:02,720
Let's try the calculator. Very pretty.
 

3
00:00:02,720 --> 00:00:05,190
Let's try the calculator. Very pretty.
This is like Windows XP looking and I

4
00:00:05,190 --> 00:00:05,200
This is like Windows XP looking and I
 

5
00:00:05,200 --> 00:00:06,510
This is like Windows XP looking and I
dig

6
00:00:06,510 --> 00:00:06,520
dig
 

7
00:00:06,520 --> 00:00:08,790
dig
it. Today we're going to be doing some

8
00:00:08,790 --> 00:00:08,800
it. Today we're going to be doing some
 

9
00:00:08,800 --> 00:00:11,190
it. Today we're going to be doing some
head-to-head testing between four very

10
00:00:11,190 --> 00:00:11,200
head-to-head testing between four very
 

11
00:00:11,200 --> 00:00:13,070
head-to-head testing between four very
state-of-the-art and highly regarded

12
00:00:13,070 --> 00:00:13,080
state-of-the-art and highly regarded
 

13
00:00:13,080 --> 00:00:14,749
state-of-the-art and highly regarded
LLMs.

14
00:00:14,749 --> 00:00:14,759
LLMs.
 

15
00:00:14,759 --> 00:00:18,870
LLMs.
ChatGPT03, Google Gemini 2.5 Pro, <PERSON>

16
00:00:18,870 --> 00:00:18,880
ChatGPT03, Google Gemini 2.5 Pro, Claude
 

17
00:00:18,880 --> 00:00:23,029
ChatGPT03, Google Gemini 2.5 Pro, Claude
Opus 4, and DeepSeek R10528, which is

18
00:00:23,029 --> 00:00:23,039
Opus 4, and DeepSeek R10528, which is
 

19
00:00:23,039 --> 00:00:25,429
Opus 4, and DeepSeek R10528, which is
the newly updated variant of Deepseek.

20
00:00:25,429 --> 00:00:25,439
the newly updated variant of Deepseek.
 

21
00:00:25,439 --> 00:00:27,109
the newly updated variant of Deepseek.
Now, for this testing, it is going to be

22
00:00:27,109 --> 00:00:27,119
Now, for this testing, it is going to be
 

23
00:00:27,119 --> 00:00:29,269
Now, for this testing, it is going to be
extremely simple. I will provide every

24
00:00:29,269 --> 00:00:29,279
extremely simple. I will provide every
 

25
00:00:29,279 --> 00:00:31,669
extremely simple. I will provide every
LLM with the same exact prompt, which is

26
00:00:31,669 --> 00:00:31,679
LLM with the same exact prompt, which is
 

27
00:00:31,679 --> 00:00:34,229
LLM with the same exact prompt, which is
essentially to use JavaScript, HTML, and

28
00:00:34,229 --> 00:00:34,239
essentially to use JavaScript, HTML, and
 

29
00:00:34,239 --> 00:00:37,030
essentially to use JavaScript, HTML, and
CSS to make a website that emulates an

30
00:00:37,030 --> 00:00:37,040
CSS to make a website that emulates an
 

31
00:00:37,040 --> 00:00:39,270
CSS to make a website that emulates an
actual operating system. The user should

32
00:00:39,270 --> 00:00:39,280
actual operating system. The user should
 

33
00:00:39,280 --> 00:00:41,190
actual operating system. The user should
be able to go on the website and click

34
00:00:41,190 --> 00:00:41,200
be able to go on the website and click
 

35
00:00:41,200 --> 00:00:43,350
be able to go on the website and click
icons and navigate around as they would

36
00:00:43,350 --> 00:00:43,360
icons and navigate around as they would
 

37
00:00:43,360 --> 00:00:44,950
icons and navigate around as they would
in an actual operating system

38
00:00:44,950 --> 00:00:44,960
in an actual operating system
 

39
00:00:44,960 --> 00:00:47,029
in an actual operating system
environment. Now, this is going to be

40
00:00:47,029 --> 00:00:47,039
environment. Now, this is going to be
 

41
00:00:47,039 --> 00:00:49,510
environment. Now, this is going to be
very simple, and I may give the LLM one

42
00:00:49,510 --> 00:00:49,520
very simple, and I may give the LLM one
 

43
00:00:49,520 --> 00:00:51,590
very simple, and I may give the LLM one
or two chances to remedy any potential

44
00:00:51,590 --> 00:00:51,600
or two chances to remedy any potential
 

45
00:00:51,600 --> 00:00:53,670
or two chances to remedy any potential
issues that may pop up, but aside from

46
00:00:53,670 --> 00:00:53,680
issues that may pop up, but aside from
 

47
00:00:53,680 --> 00:00:55,430
issues that may pop up, but aside from
that, I want to see their ability to

48
00:00:55,430 --> 00:00:55,440
that, I want to see their ability to
 

49
00:00:55,440 --> 00:00:58,310
that, I want to see their ability to
kind of oneshot this, if you will. And

50
00:00:58,310 --> 00:00:58,320
kind of oneshot this, if you will. And
 

51
00:00:58,320 --> 00:00:59,910
kind of oneshot this, if you will. And
with that, we're just kind of going to

52
00:00:59,910 --> 00:00:59,920
with that, we're just kind of going to
 

53
00:00:59,920 --> 00:01:01,910
with that, we're just kind of going to
jump into it. Now, I suppose first and

54
00:01:01,910 --> 00:01:01,920
jump into it. Now, I suppose first and
 

55
00:01:01,920 --> 00:01:03,630
jump into it. Now, I suppose first and
foremost, we can just start with chat

56
00:01:03,630 --> 00:01:03,640
foremost, we can just start with chat
 

57
00:01:03,640 --> 00:01:06,870
foremost, we can just start with chat
GPT03 as the tab is open right here. And

58
00:01:06,870 --> 00:01:06,880
GPT03 as the tab is open right here. And
 

59
00:01:06,880 --> 00:01:08,550
GPT03 as the tab is open right here. And
I'm just going to paste the prompt in.

60
00:01:08,550 --> 00:01:08,560
I'm just going to paste the prompt in.
 

61
00:01:08,560 --> 00:01:10,469
I'm just going to paste the prompt in.
Go ahead and click. And of course, we

62
00:01:10,469 --> 00:01:10,479
Go ahead and click. And of course, we
 

63
00:01:10,479 --> 00:01:12,310
Go ahead and click. And of course, we
will take a look at any thinking traces

64
00:01:12,310 --> 00:01:12,320
will take a look at any thinking traces
 

65
00:01:12,320 --> 00:01:14,070
will take a look at any thinking traces
that do appear for any of the models

66
00:01:14,070 --> 00:01:14,080
that do appear for any of the models
 

67
00:01:14,080 --> 00:01:15,830
that do appear for any of the models
that actually generate this. Now,

68
00:01:15,830 --> 00:01:15,840
that actually generate this. Now,
 

69
00:01:15,840 --> 00:01:17,510
that actually generate this. Now,
normally I like to be rather forgiving

70
00:01:17,510 --> 00:01:17,520
normally I like to be rather forgiving
 

71
00:01:17,520 --> 00:01:19,830
normally I like to be rather forgiving
in terms of working with LLMs, but being

72
00:01:19,830 --> 00:01:19,840
in terms of working with LLMs, but being
 

73
00:01:19,840 --> 00:01:21,590
in terms of working with LLMs, but being
that a lot of these are pretty highly

74
00:01:21,590 --> 00:01:21,600
that a lot of these are pretty highly
 

75
00:01:21,600 --> 00:01:23,670
that a lot of these are pretty highly
regarded and highly priced, I may go

76
00:01:23,670 --> 00:01:23,680
regarded and highly priced, I may go
 

77
00:01:23,680 --> 00:01:26,230
regarded and highly priced, I may go
Gordon Ramsay on these LLMs perhaps. So,

78
00:01:26,230 --> 00:01:26,240
Gordon Ramsay on these LLMs perhaps. So,
 

79
00:01:26,240 --> 00:01:27,670
Gordon Ramsay on these LLMs perhaps. So,
we can see that it's thinking right

80
00:01:27,670 --> 00:01:27,680
we can see that it's thinking right
 

81
00:01:27,680 --> 00:01:29,749
we can see that it's thinking right
here. And I should probably zoom in on

82
00:01:29,749 --> 00:01:29,759
here. And I should probably zoom in on
 

83
00:01:29,759 --> 00:01:31,350
here. And I should probably zoom in on
this page just a slight bit. So, I will

84
00:01:31,350 --> 00:01:31,360
this page just a slight bit. So, I will
 

85
00:01:31,360 --> 00:01:33,030
this page just a slight bit. So, I will
go ahead and do that real quick. I do

86
00:01:33,030 --> 00:01:33,040
go ahead and do that real quick. I do
 

87
00:01:33,040 --> 00:01:34,550
go ahead and do that real quick. I do
apologize. There we go. That's much

88
00:01:34,550 --> 00:01:34,560
apologize. There we go. That's much
 

89
00:01:34,560 --> 00:01:36,870
apologize. There we go. That's much
better. All right. Okay. So, it thought

90
00:01:36,870 --> 00:01:36,880
better. All right. Okay. So, it thought
 

91
00:01:36,880 --> 00:01:39,510
better. All right. Okay. So, it thought
for 24 seconds. It opens up the result

92
00:01:39,510 --> 00:01:39,520
for 24 seconds. It opens up the result
 

93
00:01:39,520 --> 00:01:41,749
for 24 seconds. It opens up the result
in its little artifact window. It is

94
00:01:41,749 --> 00:01:41,759
in its little artifact window. It is
 

95
00:01:41,759 --> 00:01:43,749
in its little artifact window. It is
naming this web OS, which is

96
00:01:43,749 --> 00:01:43,759
naming this web OS, which is
 

97
00:01:43,759 --> 00:01:45,830
naming this web OS, which is
interesting. And it is coding at light

98
00:01:45,830 --> 00:01:45,840
interesting. And it is coding at light
 

99
00:01:45,840 --> 00:01:47,910
interesting. And it is coding at light
speed. No, I'm kidding. But, you know,

100
00:01:47,910 --> 00:01:47,920
speed. No, I'm kidding. But, you know,
 

101
00:01:47,920 --> 00:01:49,749
speed. No, I'm kidding. But, you know,
it's pretty quick, so that's good. All

102
00:01:49,749 --> 00:01:49,759
it's pretty quick, so that's good. All
 

103
00:01:49,759 --> 00:01:51,350
it's pretty quick, so that's good. All
right. The result has been generated

104
00:01:51,350 --> 00:01:51,360
right. The result has been generated
 

105
00:01:51,360 --> 00:01:53,749
right. The result has been generated
here in one single HTML file, which I

106
00:01:53,749 --> 00:01:53,759
here in one single HTML file, which I
 

107
00:01:53,759 --> 00:01:55,109
here in one single HTML file, which I
don't mind because then I don't have to

108
00:01:55,109 --> 00:01:55,119
don't mind because then I don't have to
 

109
00:01:55,119 --> 00:01:56,749
don't mind because then I don't have to
organize multiple

110
00:01:56,749 --> 00:01:56,759
organize multiple
 

111
00:01:56,759 --> 00:02:01,109
organize multiple
files. All right. Well, we do have a

112
00:02:01,109 --> 00:02:01,119
files. All right. Well, we do have a
 

113
00:02:01,119 --> 00:02:03,030
files. All right. Well, we do have a
little operating system looking window

114
00:02:03,030 --> 00:02:03,040
little operating system looking window
 

115
00:02:03,040 --> 00:02:06,069
little operating system looking window
here. I suppose there is a system clock.

116
00:02:06,069 --> 00:02:06,079
here. I suppose there is a system clock.
 

117
00:02:06,079 --> 00:02:09,430
here. I suppose there is a system clock.
Um, when I click on these, oh, okay, it

118
00:02:09,430 --> 00:02:09,440
Um, when I click on these, oh, okay, it
 

119
00:02:09,440 --> 00:02:11,830
Um, when I click on these, oh, okay, it
did open a file browser, a vacation

120
00:02:11,830 --> 00:02:11,840
did open a file browser, a vacation
 

121
00:02:11,840 --> 00:02:14,949
did open a file browser, a vacation
image, an MP3, and a zip. So, it kind of

122
00:02:14,949 --> 00:02:14,959
image, an MP3, and a zip. So, it kind of
 

123
00:02:14,959 --> 00:02:17,030
image, an MP3, and a zip. So, it kind of
went very hard on emulating many

124
00:02:17,030 --> 00:02:17,040
went very hard on emulating many
 

125
00:02:17,040 --> 00:02:18,710
went very hard on emulating many
different file types, which I suppose is

126
00:02:18,710 --> 00:02:18,720
different file types, which I suppose is
 

127
00:02:18,720 --> 00:02:20,470
different file types, which I suppose is
good. There does seem to be somewhat of

128
00:02:20,470 --> 00:02:20,480
good. There does seem to be somewhat of
 

129
00:02:20,480 --> 00:02:23,030
good. There does seem to be somewhat of
a kind of Mac OS browser window looking

130
00:02:23,030 --> 00:02:23,040
a kind of Mac OS browser window looking
 

131
00:02:23,040 --> 00:02:25,110
a kind of Mac OS browser window looking
aesthetic here. Aside from that, there

132
00:02:25,110 --> 00:02:25,120
aesthetic here. Aside from that, there
 

133
00:02:25,120 --> 00:02:27,670
aesthetic here. Aside from that, there
is a gradient in the background. Um, I

134
00:02:27,670 --> 00:02:27,680
is a gradient in the background. Um, I
 

135
00:02:27,680 --> 00:02:30,630
is a gradient in the background. Um, I
don't see a start menu here. These do

136
00:02:30,630 --> 00:02:30,640
don't see a start menu here. These do
 

137
00:02:30,640 --> 00:02:33,509
don't see a start menu here. These do
seem a bit slow to respond. Oh, okay. We

138
00:02:33,509 --> 00:02:33,519
seem a bit slow to respond. Oh, okay. We
 

139
00:02:33,519 --> 00:02:36,229
seem a bit slow to respond. Oh, okay. We
do have about. Can I reposition the

140
00:02:36,229 --> 00:02:36,239
do have about. Can I reposition the
 

141
00:02:36,239 --> 00:02:38,630
do have about. Can I reposition the
windows? I can. Can I resize the

142
00:02:38,630 --> 00:02:38,640
windows? I can. Can I resize the
 

143
00:02:38,640 --> 00:02:41,589
windows? I can. Can I resize the
windows? Not necessarily. All right.

144
00:02:41,589 --> 00:02:41,599
windows? Not necessarily. All right.
 

145
00:02:41,599 --> 00:02:43,270
windows? Not necessarily. All right.
Tells me a little bit about it. Drag

146
00:02:43,270 --> 00:02:43,280
Tells me a little bit about it. Drag
 

147
00:02:43,280 --> 00:02:44,710
Tells me a little bit about it. Drag
windows, open apps, and enjoy the

148
00:02:44,710 --> 00:02:44,720
windows, open apps, and enjoy the
 

149
00:02:44,720 --> 00:02:47,670
windows, open apps, and enjoy the
pixelated freedom. Thank you. All right.

150
00:02:47,670 --> 00:02:47,680
pixelated freedom. Thank you. All right.
 

151
00:02:47,680 --> 00:02:48,589
pixelated freedom. Thank you. All right.
So,

152
00:02:48,589 --> 00:02:48,599
So,
 

153
00:02:48,599 --> 00:02:51,190
So,
uh, and it seems like this is just

154
00:02:51,190 --> 00:02:51,200
uh, and it seems like this is just
 

155
00:02:51,200 --> 00:02:55,430
uh, and it seems like this is just
supposed to be images for these logos,

156
00:02:55,430 --> 00:02:55,440
supposed to be images for these logos,
 

157
00:02:55,440 --> 00:02:57,030
supposed to be images for these logos,
uh, for these icons, but it was replaced

158
00:02:57,030 --> 00:02:57,040
uh, for these icons, but it was replaced
 

159
00:02:57,040 --> 00:02:58,790
uh, for these icons, but it was replaced
with text, assuming it was not able to

160
00:02:58,790 --> 00:02:58,800
with text, assuming it was not able to
 

161
00:02:58,800 --> 00:03:01,229
with text, assuming it was not able to
get the images working. Okay. Well,

162
00:03:01,229 --> 00:03:01,239
get the images working. Okay. Well,
 

163
00:03:01,239 --> 00:03:04,229
get the images working. Okay. Well,
overall, it works, which is obviously a

164
00:03:04,229 --> 00:03:04,239
overall, it works, which is obviously a
 

165
00:03:04,239 --> 00:03:06,309
overall, it works, which is obviously a
positive. However, I would say that's

166
00:03:06,309 --> 00:03:06,319
positive. However, I would say that's
 

167
00:03:06,319 --> 00:03:08,070
positive. However, I would say that's
kind of the extent of a lot of the good

168
00:03:08,070 --> 00:03:08,080
kind of the extent of a lot of the good
 

169
00:03:08,080 --> 00:03:10,229
kind of the extent of a lot of the good
things I have to say about it. It is

170
00:03:10,229 --> 00:03:10,239
things I have to say about it. It is
 

171
00:03:10,239 --> 00:03:13,670
things I have to say about it. It is
kind of, as the youth would say, mid. So

172
00:03:13,670 --> 00:03:13,680
kind of, as the youth would say, mid. So
 

173
00:03:13,680 --> 00:03:15,190
kind of, as the youth would say, mid. So
let's just go ahead and now try this

174
00:03:15,190 --> 00:03:15,200
let's just go ahead and now try this
 

175
00:03:15,200 --> 00:03:18,869
let's just go ahead and now try this
with Anthropics Claude Opus 4 and we

176
00:03:18,869 --> 00:03:18,879
with Anthropics Claude Opus 4 and we
 

177
00:03:18,879 --> 00:03:21,030
with Anthropics Claude Opus 4 and we
will go ahead and press enter here. Also

178
00:03:21,030 --> 00:03:21,040
will go ahead and press enter here. Also
 

179
00:03:21,040 --> 00:03:22,790
will go ahead and press enter here. Also
I suppose this gives us a little insight

180
00:03:22,790 --> 00:03:22,800
I suppose this gives us a little insight
 

181
00:03:22,800 --> 00:03:25,270
I suppose this gives us a little insight
into the actual kind of like aesthetic

182
00:03:25,270 --> 00:03:25,280
into the actual kind of like aesthetic
 

183
00:03:25,280 --> 00:03:27,910
into the actual kind of like aesthetic
of using either one of these AIs in

184
00:03:27,910 --> 00:03:27,920
of using either one of these AIs in
 

185
00:03:27,920 --> 00:03:29,350
of using either one of these AIs in
terms of the way the web interface

186
00:03:29,350 --> 00:03:29,360
terms of the way the web interface
 

187
00:03:29,360 --> 00:03:31,910
terms of the way the web interface
looks. Claude is definitely going a

188
00:03:31,910 --> 00:03:31,920
looks. Claude is definitely going a
 

189
00:03:31,920 --> 00:03:33,910
looks. Claude is definitely going a
little slower, but it is drafting its

190
00:03:33,910 --> 00:03:33,920
little slower, but it is drafting its
 

191
00:03:33,920 --> 00:03:35,830
little slower, but it is drafting its
artifact here and will undoubtedly open

192
00:03:35,830 --> 00:03:35,840
artifact here and will undoubtedly open
 

193
00:03:35,840 --> 00:03:37,670
artifact here and will undoubtedly open
up some side window as well as it begins

194
00:03:37,670 --> 00:03:37,680
up some side window as well as it begins
 

195
00:03:37,680 --> 00:03:40,869
up some side window as well as it begins
to code, which is okay. and we'll just

196
00:03:40,869 --> 00:03:40,879
to code, which is okay. and we'll just
 

197
00:03:40,879 --> 00:03:43,750
to code, which is okay. and we'll just
kind of let it go and see what comes up.

198
00:03:43,750 --> 00:03:43,760
kind of let it go and see what comes up.
 

199
00:03:43,760 --> 00:03:45,830
kind of let it go and see what comes up.
I will say the script that Claude is

200
00:03:45,830 --> 00:03:45,840
I will say the script that Claude is
 

201
00:03:45,840 --> 00:03:48,869
I will say the script that Claude is
generating is extremely lengthy and it's

202
00:03:48,869 --> 00:03:48,879
generating is extremely lengthy and it's
 

203
00:03:48,879 --> 00:03:50,710
generating is extremely lengthy and it's
not necessarily the fastest, so it's

204
00:03:50,710 --> 00:03:50,720
not necessarily the fastest, so it's
 

205
00:03:50,720 --> 00:03:53,110
not necessarily the fastest, so it's
kind of giving me like LLM on Raspberry

206
00:03:53,110 --> 00:03:53,120
kind of giving me like LLM on Raspberry
 

207
00:03:53,120 --> 00:03:55,750
kind of giving me like LLM on Raspberry
Pi 4B token speed vibes, but aside from

208
00:03:55,750 --> 00:03:55,760
Pi 4B token speed vibes, but aside from
 

209
00:03:55,760 --> 00:03:58,229
Pi 4B token speed vibes, but aside from
that, it looks to be pretty intricate.

210
00:03:58,229 --> 00:03:58,239
that, it looks to be pretty intricate.
 

211
00:03:58,239 --> 00:04:01,030
that, it looks to be pretty intricate.
So, I do have high hopes for what we're

212
00:04:01,030 --> 00:04:01,040
So, I do have high hopes for what we're
 

213
00:04:01,040 --> 00:04:03,670
So, I do have high hopes for what we're
hopefully soon going to see here. It

214
00:04:03,670 --> 00:04:03,680
hopefully soon going to see here. It
 

215
00:04:03,680 --> 00:04:05,110
hopefully soon going to see here. It
just finished and then immediately

216
00:04:05,110 --> 00:04:05,120
just finished and then immediately
 

217
00:04:05,120 --> 00:04:06,710
just finished and then immediately
started running it in the artifact

218
00:04:06,710 --> 00:04:06,720
started running it in the artifact
 

219
00:04:06,720 --> 00:04:08,390
started running it in the artifact
window, which has kind of screwed up my

220
00:04:08,390 --> 00:04:08,400
window, which has kind of screwed up my
 

221
00:04:08,400 --> 00:04:11,589
window, which has kind of screwed up my
test. But aside from that, our first

222
00:04:11,589 --> 00:04:11,599
test. But aside from that, our first
 

223
00:04:11,599 --> 00:04:13,190
test. But aside from that, our first
look there does appear to be quite good.

224
00:04:13,190 --> 00:04:13,200
look there does appear to be quite good.
 

225
00:04:13,200 --> 00:04:15,030
look there does appear to be quite good.
So I am just going to go ahead and copy

226
00:04:15,030 --> 00:04:15,040
So I am just going to go ahead and copy
 

227
00:04:15,040 --> 00:04:17,590
So I am just going to go ahead and copy
this and save it into an HTML file which

228
00:04:17,590 --> 00:04:17,600
this and save it into an HTML file which
 

229
00:04:17,600 --> 00:04:21,469
this and save it into an HTML file which
we can then test in our larger browser

230
00:04:21,469 --> 00:04:21,479
we can then test in our larger browser
 

231
00:04:21,479 --> 00:04:22,990
we can then test in our larger browser
window. All

232
00:04:22,990 --> 00:04:23,000
window. All
 

233
00:04:23,000 --> 00:04:27,110
window. All
right, this looks well it's quite simple

234
00:04:27,110 --> 00:04:27,120
right, this looks well it's quite simple
 

235
00:04:27,120 --> 00:04:29,430
right, this looks well it's quite simple
but it's elegant. The file folders do

236
00:04:29,430 --> 00:04:29,440
but it's elegant. The file folders do
 

237
00:04:29,440 --> 00:04:30,870
but it's elegant. The file folders do
work. You know, I did this when I was

238
00:04:30,870 --> 00:04:30,880
work. You know, I did this when I was
 

239
00:04:30,880 --> 00:04:32,790
work. You know, I did this when I was
testing DeepSeek where I became enamored

240
00:04:32,790 --> 00:04:32,800
testing DeepSeek where I became enamored
 

241
00:04:32,800 --> 00:04:34,230
testing DeepSeek where I became enamored
with its aesthetic and then when I went

242
00:04:34,230 --> 00:04:34,240
with its aesthetic and then when I went
 

243
00:04:34,240 --> 00:04:35,990
with its aesthetic and then when I went
to actually test it, it didn't work. So

244
00:04:35,990 --> 00:04:36,000
to actually test it, it didn't work. So
 

245
00:04:36,000 --> 00:04:37,430
to actually test it, it didn't work. So
I probably shouldn't make that mistake

246
00:04:37,430 --> 00:04:37,440
I probably shouldn't make that mistake
 

247
00:04:37,440 --> 00:04:40,310
I probably shouldn't make that mistake
again. settings. All right. Well, we

248
00:04:40,310 --> 00:04:40,320
again. settings. All right. Well, we
 

249
00:04:40,320 --> 00:04:44,749
again. settings. All right. Well, we
don't need settings. File browser text

250
00:04:44,749 --> 00:04:44,759
don't need settings. File browser text
 

251
00:04:44,759 --> 00:04:47,070
don't need settings. File browser text
editor. Maybe the

252
00:04:47,070 --> 00:04:47,080
editor. Maybe the
 

253
00:04:47,080 --> 00:04:49,510
editor. Maybe the
start. Okay. I think it might be another

254
00:04:49,510 --> 00:04:49,520
start. Okay. I think it might be another
 

255
00:04:49,520 --> 00:04:53,070
start. Okay. I think it might be another
one of those scenarios where it

256
00:04:53,070 --> 00:04:53,080
one of those scenarios where it
 

257
00:04:53,080 --> 00:04:55,430
one of those scenarios where it
essentially looks fantastic, but

258
00:04:55,430 --> 00:04:55,440
essentially looks fantastic, but
 

259
00:04:55,440 --> 00:04:57,550
essentially looks fantastic, but
functionally is not necessarily

260
00:04:57,550 --> 00:04:57,560
functionally is not necessarily
 

261
00:04:57,560 --> 00:05:00,230
functionally is not necessarily
functional. Um, okay. Here's what I'm

262
00:05:00,230 --> 00:05:00,240
functional. Um, okay. Here's what I'm
 

263
00:05:00,240 --> 00:05:01,350
functional. Um, okay. Here's what I'm
going to do because I like to hedge

264
00:05:01,350 --> 00:05:01,360
going to do because I like to hedge
 

265
00:05:01,360 --> 00:05:03,030
going to do because I like to hedge
against the potential of this not

266
00:05:03,030 --> 00:05:03,040
against the potential of this not
 

267
00:05:03,040 --> 00:05:04,790
against the potential of this not
playing nice with specific versions of

268
00:05:04,790 --> 00:05:04,800
playing nice with specific versions of
 

269
00:05:04,800 --> 00:05:06,629
playing nice with specific versions of
browsers. So, I will just go ahead and

270
00:05:06,629 --> 00:05:06,639
browsers. So, I will just go ahead and
 

271
00:05:06,639 --> 00:05:08,909
browsers. So, I will just go ahead and
open this in Google Chrome as

272
00:05:08,909 --> 00:05:08,919
open this in Google Chrome as
 

273
00:05:08,919 --> 00:05:11,990
open this in Google Chrome as
well. And unfortunately, we don't

274
00:05:11,990 --> 00:05:12,000
well. And unfortunately, we don't
 

275
00:05:12,000 --> 00:05:15,670
well. And unfortunately, we don't
actually get any functionality here. So,

276
00:05:15,670 --> 00:05:15,680
actually get any functionality here. So,
 

277
00:05:15,680 --> 00:05:19,029
actually get any functionality here. So,
all right. Well, again, I'm not going to

278
00:05:19,029 --> 00:05:19,039
all right. Well, again, I'm not going to
 

279
00:05:19,039 --> 00:05:20,390
all right. Well, again, I'm not going to
really say much. I guess we're just

280
00:05:20,390 --> 00:05:20,400
really say much. I guess we're just
 

281
00:05:20,400 --> 00:05:21,990
really say much. I guess we're just
going to continue the testing, but that

282
00:05:21,990 --> 00:05:22,000
going to continue the testing, but that
 

283
00:05:22,000 --> 00:05:23,990
going to continue the testing, but that
is somewhat of a tease because this is

284
00:05:23,990 --> 00:05:24,000
is somewhat of a tease because this is
 

285
00:05:24,000 --> 00:05:25,830
is somewhat of a tease because this is
beautiful. I will, you know what I will

286
00:05:25,830 --> 00:05:25,840
beautiful. I will, you know what I will
 

287
00:05:25,840 --> 00:05:27,590
beautiful. I will, you know what I will
do? Just to give the benefit of the

288
00:05:27,590 --> 00:05:27,600
do? Just to give the benefit of the
 

289
00:05:27,600 --> 00:05:29,590
do? Just to give the benefit of the
doubt, I will go ahead and just put this

290
00:05:29,590 --> 00:05:29,600
doubt, I will go ahead and just put this
 

291
00:05:29,600 --> 00:05:31,990
doubt, I will go ahead and just put this
back in this weird little thing here.

292
00:05:31,990 --> 00:05:32,000
back in this weird little thing here.
 

293
00:05:32,000 --> 00:05:33,510
back in this weird little thing here.
Okay. and just verify that the

294
00:05:33,510 --> 00:05:33,520
Okay. and just verify that the
 

295
00:05:33,520 --> 00:05:35,590
Okay. and just verify that the
functionality also does not exist from

296
00:05:35,590 --> 00:05:35,600
functionality also does not exist from
 

297
00:05:35,600 --> 00:05:38,230
functionality also does not exist from
within the artifact window live preview.

298
00:05:38,230 --> 00:05:38,240
within the artifact window live preview.
 

299
00:05:38,240 --> 00:05:41,830
within the artifact window live preview.
So, okay. All right. So, these don't

300
00:05:41,830 --> 00:05:41,840
So, okay. All right. So, these don't
 

301
00:05:41,840 --> 00:05:43,749
So, okay. All right. So, these don't
error out, but the start menu does, and

302
00:05:43,749 --> 00:05:43,759
error out, but the start menu does, and
 

303
00:05:43,759 --> 00:05:45,749
error out, but the start menu does, and
it just gives us the console error right

304
00:05:45,749 --> 00:05:45,759
it just gives us the console error right
 

305
00:05:45,759 --> 00:05:47,670
it just gives us the console error right
there. All right. Let's just hop into

306
00:05:47,670 --> 00:05:47,680
there. All right. Let's just hop into
 

307
00:05:47,680 --> 00:05:50,670
there. All right. Let's just hop into
the next contender. Deepseek

308
00:05:50,670 --> 00:05:50,680
the next contender. Deepseek
 

309
00:05:50,680 --> 00:05:53,469
the next contender. Deepseek
R10528 network error. Please try again

310
00:05:53,469 --> 00:05:53,479
R10528 network error. Please try again
 

311
00:05:53,479 --> 00:05:55,990
R10528 network error. Please try again
later. We're now trying this with Google

312
00:05:55,990 --> 00:05:56,000
later. We're now trying this with Google
 

313
00:05:56,000 --> 00:05:58,469
later. We're now trying this with Google
Gemini 2.5 Pro. We'll just let it run

314
00:05:58,469 --> 00:05:58,479
Gemini 2.5 Pro. We'll just let it run
 

315
00:05:58,479 --> 00:06:00,469
Gemini 2.5 Pro. We'll just let it run
and then we will see. Now, this is quite

316
00:06:00,469 --> 00:06:00,479
and then we will see. Now, this is quite
 

317
00:06:00,479 --> 00:06:03,270
and then we will see. Now, this is quite
interesting where um it does appear that

318
00:06:03,270 --> 00:06:03,280
interesting where um it does appear that
 

319
00:06:03,280 --> 00:06:05,430
interesting where um it does appear that
it's still thinking. No, maybe not.

320
00:06:05,430 --> 00:06:05,440
it's still thinking. No, maybe not.
 

321
00:06:05,440 --> 00:06:07,430
it's still thinking. No, maybe not.
Okay, so it didn't think very long and

322
00:06:07,430 --> 00:06:07,440
Okay, so it didn't think very long and
 

323
00:06:07,440 --> 00:06:08,870
Okay, so it didn't think very long and
it kind of just started spinning out the

324
00:06:08,870 --> 00:06:08,880
it kind of just started spinning out the
 

325
00:06:08,880 --> 00:06:10,790
it kind of just started spinning out the
script immediately. From what I'm seeing

326
00:06:10,790 --> 00:06:10,800
script immediately. From what I'm seeing
 

327
00:06:10,800 --> 00:06:12,950
script immediately. From what I'm seeing
here, it is the only one so far that is

328
00:06:12,950 --> 00:06:12,960
here, it is the only one so far that is
 

329
00:06:12,960 --> 00:06:15,029
here, it is the only one so far that is
actually doing this in separate scripts,

330
00:06:15,029 --> 00:06:15,039
actually doing this in separate scripts,
 

331
00:06:15,039 --> 00:06:17,430
actually doing this in separate scripts,
which I'm not necessarily opposed to as

332
00:06:17,430 --> 00:06:17,440
which I'm not necessarily opposed to as
 

333
00:06:17,440 --> 00:06:19,189
which I'm not necessarily opposed to as
I suppose for cleanliness and actually

334
00:06:19,189 --> 00:06:19,199
I suppose for cleanliness and actually
 

335
00:06:19,199 --> 00:06:21,350
I suppose for cleanliness and actually
being able to understand what exists in

336
00:06:21,350 --> 00:06:21,360
being able to understand what exists in
 

337
00:06:21,360 --> 00:06:24,710
being able to understand what exists in
these scripts. This is a better way to

338
00:06:24,710 --> 00:06:24,720
these scripts. This is a better way to
 

339
00:06:24,720 --> 00:06:28,950
these scripts. This is a better way to
go about doing this. This is quite fast.

340
00:06:28,950 --> 00:06:28,960
go about doing this. This is quite fast.
 

341
00:06:28,960 --> 00:06:32,390
go about doing this. This is quite fast.
This is definitely more on par with the

342
00:06:32,390 --> 00:06:32,400
This is definitely more on par with the
 

343
00:06:32,400 --> 00:06:36,390
This is definitely more on par with the
chat GPG03 speed. Claude was a bit slow.

344
00:06:36,390 --> 00:06:36,400
chat GPG03 speed. Claude was a bit slow.
 

345
00:06:36,400 --> 00:06:38,629
chat GPG03 speed. Claude was a bit slow.
Um, Deepseek was having some server

346
00:06:38,629 --> 00:06:38,639
Um, Deepseek was having some server
 

347
00:06:38,639 --> 00:06:40,230
Um, Deepseek was having some server
errors right now, so hopefully that will

348
00:06:40,230 --> 00:06:40,240
errors right now, so hopefully that will
 

349
00:06:40,240 --> 00:06:42,150
errors right now, so hopefully that will
get remedied cuz I would like to film

350
00:06:42,150 --> 00:06:42,160
get remedied cuz I would like to film
 

351
00:06:42,160 --> 00:06:44,070
get remedied cuz I would like to film
the video with it. But all right, this

352
00:06:44,070 --> 00:06:44,080
the video with it. But all right, this
 

353
00:06:44,080 --> 00:06:46,230
the video with it. But all right, this
is done. So that was extremely fast. All

354
00:06:46,230 --> 00:06:46,240
is done. So that was extremely fast. All
 

355
00:06:46,240 --> 00:06:48,790
is done. So that was extremely fast. All
right, we have the Gemini browser OS

356
00:06:48,790 --> 00:06:48,800
right, we have the Gemini browser OS
 

357
00:06:48,800 --> 00:06:50,710
right, we have the Gemini browser OS
result all set. So we're just going to

358
00:06:50,710 --> 00:06:50,720
result all set. So we're just going to
 

359
00:06:50,720 --> 00:06:54,510
result all set. So we're just going to
go ahead and take a peek at it. Okay.

360
00:06:54,510 --> 00:06:54,520
go ahead and take a peek at it. Okay.
 

361
00:06:54,520 --> 00:06:57,270
go ahead and take a peek at it. Okay.
Well, all right. So, what I noticed in

362
00:06:57,270 --> 00:06:57,280
Well, all right. So, what I noticed in
 

363
00:06:57,280 --> 00:06:59,350
Well, all right. So, what I noticed in
the actual generation, it was supposed

364
00:06:59,350 --> 00:06:59,360
the actual generation, it was supposed
 

365
00:06:59,360 --> 00:07:01,510
the actual generation, it was supposed
to just use some stock photo for the

366
00:07:01,510 --> 00:07:01,520
to just use some stock photo for the
 

367
00:07:01,520 --> 00:07:03,510
to just use some stock photo for the
background if I did not actually supply

368
00:07:03,510 --> 00:07:03,520
background if I did not actually supply
 

369
00:07:03,520 --> 00:07:05,749
background if I did not actually supply
it with one, which obviously does not

370
00:07:05,749 --> 00:07:05,759
it with one, which obviously does not
 

371
00:07:05,759 --> 00:07:07,909
it with one, which obviously does not
seem to have loaded. So, this kind of

372
00:07:07,909 --> 00:07:07,919
seem to have loaded. So, this kind of
 

373
00:07:07,919 --> 00:07:09,670
seem to have loaded. So, this kind of
white on white here is really messing up

374
00:07:09,670 --> 00:07:09,680
white on white here is really messing up
 

375
00:07:09,680 --> 00:07:12,150
white on white here is really messing up
the test. I suppose I'll check

376
00:07:12,150 --> 00:07:12,160
the test. I suppose I'll check
 

377
00:07:12,160 --> 00:07:14,150
the test. I suppose I'll check
functionality before I decide what to do

378
00:07:14,150 --> 00:07:14,160
functionality before I decide what to do
 

379
00:07:14,160 --> 00:07:18,070
functionality before I decide what to do
here. Let's let's open Notepad. Oh,

380
00:07:18,070 --> 00:07:18,080
here. Let's let's open Notepad. Oh,
 

381
00:07:18,080 --> 00:07:21,350
here. Let's let's open Notepad. Oh,
okay. So, this actually does work if I

382
00:07:21,350 --> 00:07:21,360
okay. So, this actually does work if I
 

383
00:07:21,360 --> 00:07:23,870
okay. So, this actually does work if I
try to All right.

384
00:07:23,870 --> 00:07:23,880
try to All right.
 

385
00:07:23,880 --> 00:07:25,830
try to All right.
Well, okay. Okay, it doesn't let me

386
00:07:25,830 --> 00:07:25,840
Well, okay. Okay, it doesn't let me
 

387
00:07:25,840 --> 00:07:28,070
Well, okay. Okay, it doesn't let me
expand, but it does let me drag. All

388
00:07:28,070 --> 00:07:28,080
expand, but it does let me drag. All
 

389
00:07:28,080 --> 00:07:29,990
expand, but it does let me drag. All
right, let's now try DeepSeek and see

390
00:07:29,990 --> 00:07:30,000
right, let's now try DeepSeek and see
 

391
00:07:30,000 --> 00:07:32,070
right, let's now try DeepSeek and see
how it does here. I have noticed that

392
00:07:32,070 --> 00:07:32,080
how it does here. I have noticed that
 

393
00:07:32,080 --> 00:07:33,909
how it does here. I have noticed that
there have been some server errors that

394
00:07:33,909 --> 00:07:33,919
there have been some server errors that
 

395
00:07:33,919 --> 00:07:35,189
there have been some server errors that
have been popping up when I've been

396
00:07:35,189 --> 00:07:35,199
have been popping up when I've been
 

397
00:07:35,199 --> 00:07:37,189
have been popping up when I've been
trying this with DeepSeek. So, I really

398
00:07:37,189 --> 00:07:37,199
trying this with DeepSeek. So, I really
 

399
00:07:37,199 --> 00:07:39,430
trying this with DeepSeek. So, I really
hope this works as the whole reason I

400
00:07:39,430 --> 00:07:39,440
hope this works as the whole reason I
 

401
00:07:39,440 --> 00:07:41,270
hope this works as the whole reason I
came up with this test actually was just

402
00:07:41,270 --> 00:07:41,280
came up with this test actually was just
 

403
00:07:41,280 --> 00:07:43,350
came up with this test actually was just
in testing the newly updated DeepSseek

404
00:07:43,350 --> 00:07:43,360
in testing the newly updated DeepSseek
 

405
00:07:43,360 --> 00:07:45,670
in testing the newly updated DeepSseek
R1 model. And the result that it

406
00:07:45,670 --> 00:07:45,680
R1 model. And the result that it
 

407
00:07:45,680 --> 00:07:47,830
R1 model. And the result that it
produced was aesthetically, it was

408
00:07:47,830 --> 00:07:47,840
produced was aesthetically, it was
 

409
00:07:47,840 --> 00:07:49,990
produced was aesthetically, it was
Windows 95 aesthetic. It even actually

410
00:07:49,990 --> 00:07:50,000
Windows 95 aesthetic. It even actually
 

411
00:07:50,000 --> 00:07:51,830
Windows 95 aesthetic. It even actually
mentioned that it was a Windows 95

412
00:07:51,830 --> 00:07:51,840
mentioned that it was a Windows 95
 

413
00:07:51,840 --> 00:07:53,670
mentioned that it was a Windows 95
aesthetic. It looked awesome but

414
00:07:53,670 --> 00:07:53,680
aesthetic. It looked awesome but
 

415
00:07:53,680 --> 00:07:55,749
aesthetic. It looked awesome but
unfortunately kind of like the anthropic

416
00:07:55,749 --> 00:07:55,759
unfortunately kind of like the anthropic
 

417
00:07:55,759 --> 00:07:57,990
unfortunately kind of like the anthropic
claude result. It looked beautiful but

418
00:07:57,990 --> 00:07:58,000
claude result. It looked beautiful but
 

419
00:07:58,000 --> 00:08:00,150
claude result. It looked beautiful but
nothing functionally happened. So I just

420
00:08:00,150 --> 00:08:00,160
nothing functionally happened. So I just
 

421
00:08:00,160 --> 00:08:01,670
nothing functionally happened. So I just
hope that it actually finishes the

422
00:08:01,670 --> 00:08:01,680
hope that it actually finishes the
 

423
00:08:01,680 --> 00:08:03,830
hope that it actually finishes the
generation here without any server

424
00:08:03,830 --> 00:08:03,840
generation here without any server
 

425
00:08:03,840 --> 00:08:05,670
generation here without any server
errors and then we'll have a full

426
00:08:05,670 --> 00:08:05,680
errors and then we'll have a full
 

427
00:08:05,680 --> 00:08:08,869
errors and then we'll have a full
continuity test. All right. Fortunately

428
00:08:08,869 --> 00:08:08,879
continuity test. All right. Fortunately
 

429
00:08:08,879 --> 00:08:10,629
continuity test. All right. Fortunately
the deepseek server error did not

430
00:08:10,629 --> 00:08:10,639
the deepseek server error did not
 

431
00:08:10,639 --> 00:08:12,469
the deepseek server error did not
persist because we do have a fully

432
00:08:12,469 --> 00:08:12,479
persist because we do have a fully
 

433
00:08:12,479 --> 00:08:14,469
persist because we do have a fully
generated script right here. It did do

434
00:08:14,469 --> 00:08:14,479
generated script right here. It did do
 

435
00:08:14,479 --> 00:08:16,790
generated script right here. It did do
it all contained within one HTML script

436
00:08:16,790 --> 00:08:16,800
it all contained within one HTML script
 

437
00:08:16,800 --> 00:08:19,110
it all contained within one HTML script
which three out of the four did. Um

438
00:08:19,110 --> 00:08:19,120
which three out of the four did. Um
 

439
00:08:19,120 --> 00:08:21,430
which three out of the four did. Um
everyone except Google Gemini Pro. It

440
00:08:21,430 --> 00:08:21,440
everyone except Google Gemini Pro. It
 

441
00:08:21,440 --> 00:08:22,950
everyone except Google Gemini Pro. It
tells us a little bit about this and

442
00:08:22,950 --> 00:08:22,960
tells us a little bit about this and
 

443
00:08:22,960 --> 00:08:24,309
tells us a little bit about this and
we're basically just going to go ahead

444
00:08:24,309 --> 00:08:24,319
we're basically just going to go ahead
 

445
00:08:24,319 --> 00:08:26,029
we're basically just going to go ahead
now and run

446
00:08:26,029 --> 00:08:26,039
now and run
 

447
00:08:26,039 --> 00:08:29,110
now and run
this. All right. Now for our DeepSseek

448
00:08:29,110 --> 00:08:29,120
this. All right. Now for our DeepSseek
 

449
00:08:29,120 --> 00:08:33,029
this. All right. Now for our DeepSseek
result here. Whoa. Whoa. This is quite

450
00:08:33,029 --> 00:08:33,039
result here. Whoa. Whoa. This is quite
 

451
00:08:33,039 --> 00:08:34,230
result here. Whoa. Whoa. This is quite
interesting. And something that I

452
00:08:34,230 --> 00:08:34,240
interesting. And something that I
 

453
00:08:34,240 --> 00:08:37,670
interesting. And something that I
noticed this does where it you see how

454
00:08:37,670 --> 00:08:37,680
noticed this does where it you see how
 

455
00:08:37,680 --> 00:08:39,589
noticed this does where it you see how
it's kind of like contained within a

456
00:08:39,589 --> 00:08:39,599
it's kind of like contained within a
 

457
00:08:39,599 --> 00:08:41,750
it's kind of like contained within a
nice little gradient image right here.

458
00:08:41,750 --> 00:08:41,760
nice little gradient image right here.
 

459
00:08:41,760 --> 00:08:43,110
nice little gradient image right here.
All right. This is very interesting.

460
00:08:43,110 --> 00:08:43,120
All right. This is very interesting.
 

461
00:08:43,120 --> 00:08:45,190
All right. This is very interesting.
Again, I got fooled by this yesterday.

462
00:08:45,190 --> 00:08:45,200
Again, I got fooled by this yesterday.
 

463
00:08:45,200 --> 00:08:46,870
Again, I got fooled by this yesterday.
So, I'm going to check functionality

464
00:08:46,870 --> 00:08:46,880
So, I'm going to check functionality
 

465
00:08:46,880 --> 00:08:50,310
So, I'm going to check functionality
before I start to fanboy out browser

466
00:08:50,310 --> 00:08:50,320
before I start to fanboy out browser
 

467
00:08:50,320 --> 00:08:52,470
before I start to fanboy out browser
based OS. It tells me about it. Get

468
00:08:52,470 --> 00:08:52,480
based OS. It tells me about it. Get
 

469
00:08:52,480 --> 00:08:54,389
based OS. It tells me about it. Get
started. Yes, that went away. All right,

470
00:08:54,389 --> 00:08:54,399
started. Yes, that went away. All right,
 

471
00:08:54,399 --> 00:08:56,070
started. Yes, that went away. All right,
good. That didn't happen yesterday. All

472
00:08:56,070 --> 00:08:56,080
good. That didn't happen yesterday. All
 

473
00:08:56,080 --> 00:08:58,389
good. That didn't happen yesterday. All
right, let's just uh let's try the

474
00:08:58,389 --> 00:08:58,399
right, let's just uh let's try the
 

475
00:08:58,399 --> 00:09:00,550
right, let's just uh let's try the
calculator. Oh, very pretty. This is

476
00:09:00,550 --> 00:09:00,560
calculator. Oh, very pretty. This is
 

477
00:09:00,560 --> 00:09:03,949
calculator. Oh, very pretty. This is
like Windows XP looking. And I dig

478
00:09:03,949 --> 00:09:03,959
like Windows XP looking. And I dig
 

479
00:09:03,959 --> 00:09:09,030
like Windows XP looking. And I dig
it. Uh let's do 8 + 6, which is

480
00:09:09,030 --> 00:09:09,040
it. Uh let's do 8 + 6, which is
 

481
00:09:09,040 --> 00:09:12,230
it. Uh let's do 8 + 6, which is
obviously 14. Nice. Good. Very good. All

482
00:09:12,230 --> 00:09:12,240
obviously 14. Nice. Good. Very good. All
 

483
00:09:12,240 --> 00:09:14,070
obviously 14. Nice. Good. Very good. All
right, let's do something harder. Let's

484
00:09:14,070 --> 00:09:14,080
right, let's do something harder. Let's
 

485
00:09:14,080 --> 00:09:20,550
right, let's do something harder. Let's
do 7 * 9 63. All right. 8 4 * 8 9 36.

486
00:09:20,550 --> 00:09:20,560
do 7 * 9 63. All right. 8 4 * 8 9 36.
 

487
00:09:20,560 --> 00:09:22,790
do 7 * 9 63. All right. 8 4 * 8 9 36.
Very good. Very good. All right.

488
00:09:22,790 --> 00:09:22,800
Very good. Very good. All right.
 

489
00:09:22,800 --> 00:09:25,030
Very good. Very good. All right.
Notepad. This is a notepad. Oh, let's

490
00:09:25,030 --> 00:09:25,040
Notepad. This is a notepad. Oh, let's
 

491
00:09:25,040 --> 00:09:28,230
Notepad. This is a notepad. Oh, let's
check. Okay. Uh, we can drag. It does

492
00:09:28,230 --> 00:09:28,240
check. Okay. Uh, we can drag. It does
 

493
00:09:28,240 --> 00:09:29,310
check. Okay. Uh, we can drag. It does
full

494
00:09:29,310 --> 00:09:29,320
full
 

495
00:09:29,320 --> 00:09:31,829
full
screen. The notepad thing here is kind

496
00:09:31,829 --> 00:09:31,839
screen. The notepad thing here is kind
 

497
00:09:31,839 --> 00:09:34,070
screen. The notepad thing here is kind
of a little concatenated in terms of how

498
00:09:34,070 --> 00:09:34,080
of a little concatenated in terms of how
 

499
00:09:34,080 --> 00:09:35,829
of a little concatenated in terms of how
much space there was here. Free, but

500
00:09:35,829 --> 00:09:35,839
much space there was here. Free, but
 

501
00:09:35,839 --> 00:09:37,829
much space there was here. Free, but
there is some nice translucence to these

502
00:09:37,829 --> 00:09:37,839
there is some nice translucence to these
 

503
00:09:37,839 --> 00:09:39,670
there is some nice translucence to these
windows as well. Maybe there's like kind

504
00:09:39,670 --> 00:09:39,680
windows as well. Maybe there's like kind
 

505
00:09:39,680 --> 00:09:41,670
windows as well. Maybe there's like kind
of like a Vista Arrow theme going on

506
00:09:41,670 --> 00:09:41,680
of like a Vista Arrow theme going on
 

507
00:09:41,680 --> 00:09:43,430
of like a Vista Arrow theme going on
here as well, which I was honestly a big

508
00:09:43,430 --> 00:09:43,440
here as well, which I was honestly a big
 

509
00:09:43,440 --> 00:09:46,710
here as well, which I was honestly a big
fan of. Um, I do have very polarizing

510
00:09:46,710 --> 00:09:46,720
fan of. Um, I do have very polarizing
 

511
00:09:46,720 --> 00:09:49,110
fan of. Um, I do have very polarizing
takes on Microsoft OSS. All right, web

512
00:09:49,110 --> 00:09:49,120
takes on Microsoft OSS. All right, web
 

513
00:09:49,120 --> 00:09:50,710
takes on Microsoft OSS. All right, web
browser doesn't do anything. File

514
00:09:50,710 --> 00:09:50,720
browser doesn't do anything. File
 

515
00:09:50,720 --> 00:09:52,870
browser doesn't do anything. File
Explorer, very good. The icons look

516
00:09:52,870 --> 00:09:52,880
Explorer, very good. The icons look
 

517
00:09:52,880 --> 00:09:55,350
Explorer, very good. The icons look
really good. And I kind of saw this with

518
00:09:55,350 --> 00:09:55,360
really good. And I kind of saw this with
 

519
00:09:55,360 --> 00:09:57,590
really good. And I kind of saw this with
the chat GPT result where it gave us a

520
00:09:57,590 --> 00:09:57,600
the chat GPT result where it gave us a
 

521
00:09:57,600 --> 00:09:59,750
the chat GPT result where it gave us a
bunch of different sample file types.

522
00:09:59,750 --> 00:09:59,760
bunch of different sample file types.
 

523
00:09:59,760 --> 00:10:01,190
bunch of different sample file types.
All right, so let's just go ahead and

524
00:10:01,190 --> 00:10:01,200
All right, so let's just go ahead and
 

525
00:10:01,200 --> 00:10:02,790
All right, so let's just go ahead and
take a look at all of our results

526
00:10:02,790 --> 00:10:02,800
take a look at all of our results
 

527
00:10:02,800 --> 00:10:04,870
take a look at all of our results
starting from the first to the last um

528
00:10:04,870 --> 00:10:04,880
starting from the first to the last um
 

529
00:10:04,880 --> 00:10:06,630
starting from the first to the last um
in chronological order of when they were

530
00:10:06,630 --> 00:10:06,640
in chronological order of when they were
 

531
00:10:06,640 --> 00:10:09,110
in chronological order of when they were
generated, not in like judging. So this

532
00:10:09,110 --> 00:10:09,120
generated, not in like judging. So this
 

533
00:10:09,120 --> 00:10:11,710
generated, not in like judging. So this
was the chat GPT03

534
00:10:11,710 --> 00:10:11,720
was the chat GPT03
 

535
00:10:11,720 --> 00:10:15,030
was the chat GPT03
result. Positives, the clock worked. Um

536
00:10:15,030 --> 00:10:15,040
result. Positives, the clock worked. Um
 

537
00:10:15,040 --> 00:10:16,790
result. Positives, the clock worked. Um
when you actually clicked on these, the

538
00:10:16,790 --> 00:10:16,800
when you actually clicked on these, the
 

539
00:10:16,800 --> 00:10:18,710
when you actually clicked on these, the
associated thing that should have popped

540
00:10:18,710 --> 00:10:18,720
associated thing that should have popped
 

541
00:10:18,720 --> 00:10:20,870
associated thing that should have popped
up did show up. It wasn't necessarily

542
00:10:20,870 --> 00:10:20,880
up did show up. It wasn't necessarily
 

543
00:10:20,880 --> 00:10:23,110
up did show up. It wasn't necessarily
the best looking thing. You can drag and

544
00:10:23,110 --> 00:10:23,120
the best looking thing. You can drag and
 

545
00:10:23,120 --> 00:10:25,750
the best looking thing. You can drag and
move around the file uh windows, you

546
00:10:25,750 --> 00:10:25,760
move around the file uh windows, you
 

547
00:10:25,760 --> 00:10:27,430
move around the file uh windows, you
know what I mean, the browser windows.

548
00:10:27,430 --> 00:10:27,440
know what I mean, the browser windows.
 

549
00:10:27,440 --> 00:10:29,430
know what I mean, the browser windows.
And it did give us just a few sample

550
00:10:29,430 --> 00:10:29,440
And it did give us just a few sample
 

551
00:10:29,440 --> 00:10:31,350
And it did give us just a few sample
file types here and things of that sort.

552
00:10:31,350 --> 00:10:31,360
file types here and things of that sort.
 

553
00:10:31,360 --> 00:10:33,350
file types here and things of that sort.
And closing the windows does work. In

554
00:10:33,350 --> 00:10:33,360
And closing the windows does work. In
 

555
00:10:33,360 --> 00:10:35,190
And closing the windows does work. In
the same vein, the about section right

556
00:10:35,190 --> 00:10:35,200
the same vein, the about section right
 

557
00:10:35,200 --> 00:10:36,790
the same vein, the about section right
here just kind of gives us a little

558
00:10:36,790 --> 00:10:36,800
here just kind of gives us a little
 

559
00:10:36,800 --> 00:10:39,910
here just kind of gives us a little
scroll box. Kind of more of a Mac OS

560
00:10:39,910 --> 00:10:39,920
scroll box. Kind of more of a Mac OS
 

561
00:10:39,920 --> 00:10:41,750
scroll box. Kind of more of a Mac OS
like leopard kind of looking window

562
00:10:41,750 --> 00:10:41,760
like leopard kind of looking window
 

563
00:10:41,760 --> 00:10:45,829
like leopard kind of looking window
thing. Um, no start menu that I see. So

564
00:10:45,829 --> 00:10:45,839
thing. Um, no start menu that I see. So
 

565
00:10:45,839 --> 00:10:48,389
thing. Um, no start menu that I see. So
overall, uh, I'm quite displeased with

566
00:10:48,389 --> 00:10:48,399
overall, uh, I'm quite displeased with
 

567
00:10:48,399 --> 00:10:50,069
overall, uh, I'm quite displeased with
this result, but functionally it did

568
00:10:50,069 --> 00:10:50,079
this result, but functionally it did
 

569
00:10:50,079 --> 00:10:52,550
this result, but functionally it did
work very well, at least in the few

570
00:10:52,550 --> 00:10:52,560
work very well, at least in the few
 

571
00:10:52,560 --> 00:10:55,350
work very well, at least in the few
functions it had. Next up, we had and

572
00:10:55,350 --> 00:10:55,360
functions it had. Next up, we had and
 

573
00:10:55,360 --> 00:10:56,790
functions it had. Next up, we had and
something interesting we can see right

574
00:10:56,790 --> 00:10:56,800
something interesting we can see right
 

575
00:10:56,800 --> 00:10:59,030
something interesting we can see right
here is just the actual way they named

576
00:10:59,030 --> 00:10:59,040
here is just the actual way they named
 

577
00:10:59,040 --> 00:11:01,030
here is just the actual way they named
the tab. So, this is I know probably

578
00:11:01,030 --> 00:11:01,040
the tab. So, this is I know probably
 

579
00:11:01,040 --> 00:11:02,389
the tab. So, this is I know probably
you'll be like that's a really stupid

580
00:11:02,389 --> 00:11:02,399
you'll be like that's a really stupid
 

581
00:11:02,399 --> 00:11:04,389
you'll be like that's a really stupid
thing to focus on, but it is kind of

582
00:11:04,389 --> 00:11:04,399
thing to focus on, but it is kind of
 

583
00:11:04,399 --> 00:11:07,110
thing to focus on, but it is kind of
attention to detail. Although nitpicky,

584
00:11:07,110 --> 00:11:07,120
attention to detail. Although nitpicky,
 

585
00:11:07,120 --> 00:11:08,630
attention to detail. Although nitpicky,
it's something I like to kind of see

586
00:11:08,630 --> 00:11:08,640
it's something I like to kind of see
 

587
00:11:08,640 --> 00:11:10,949
it's something I like to kind of see
what happens with. So, chat GPT just

588
00:11:10,949 --> 00:11:10,959
what happens with. So, chat GPT just
 

589
00:11:10,959 --> 00:11:13,110
what happens with. So, chat GPT just
said webOS. This right here is the

590
00:11:13,110 --> 00:11:13,120
said webOS. This right here is the
 

591
00:11:13,120 --> 00:11:15,269
said webOS. This right here is the
Claude result which said webOS desktop

592
00:11:15,269 --> 00:11:15,279
Claude result which said webOS desktop
 

593
00:11:15,279 --> 00:11:17,990
Claude result which said webOS desktop
environment. Gemini just said webOS and

594
00:11:17,990 --> 00:11:18,000
environment. Gemini just said webOS and
 

595
00:11:18,000 --> 00:11:19,910
environment. Gemini just said webOS and
then DeepSeek said webOS browser

596
00:11:19,910 --> 00:11:19,920
then DeepSeek said webOS browser
 

597
00:11:19,920 --> 00:11:21,910
then DeepSeek said webOS browser
operating system. So Claude and Deepc

598
00:11:21,910 --> 00:11:21,920
operating system. So Claude and Deepc
 

599
00:11:21,920 --> 00:11:23,509
operating system. So Claude and Deepc
kind of paid a little more attention to

600
00:11:23,509 --> 00:11:23,519
kind of paid a little more attention to
 

601
00:11:23,519 --> 00:11:26,710
kind of paid a little more attention to
the Q thing of naming the tab. This

602
00:11:26,710 --> 00:11:26,720
the Q thing of naming the tab. This
 

603
00:11:26,720 --> 00:11:29,670
the Q thing of naming the tab. This
being the Claude Opus 4 result. Very

604
00:11:29,670 --> 00:11:29,680
being the Claude Opus 4 result. Very
 

605
00:11:29,680 --> 00:11:33,269
being the Claude Opus 4 result. Very
good looking. Probably the most strictly

606
00:11:33,269 --> 00:11:33,279
good looking. Probably the most strictly
 

607
00:11:33,279 --> 00:11:35,910
good looking. Probably the most strictly
adhering to an actual operating system

608
00:11:35,910 --> 00:11:35,920
adhering to an actual operating system
 

609
00:11:35,920 --> 00:11:38,630
adhering to an actual operating system
look within a browser. The icons look

610
00:11:38,630 --> 00:11:38,640
look within a browser. The icons look
 

611
00:11:38,640 --> 00:11:41,110
look within a browser. The icons look
good. It's simple and it's elegantly

612
00:11:41,110 --> 00:11:41,120
good. It's simple and it's elegantly
 

613
00:11:41,120 --> 00:11:42,949
good. It's simple and it's elegantly
simple, I suppose one could say.

614
00:11:42,949 --> 00:11:42,959
simple, I suppose one could say.
 

615
00:11:42,959 --> 00:11:45,509
simple, I suppose one could say.
However, unfortunately, functionally uh

616
00:11:45,509 --> 00:11:45,519
However, unfortunately, functionally uh
 

617
00:11:45,519 --> 00:11:47,350
However, unfortunately, functionally uh
we were somewhat limited in that

618
00:11:47,350 --> 00:11:47,360
we were somewhat limited in that
 

619
00:11:47,360 --> 00:11:48,710
we were somewhat limited in that
although the buttons and things do have

620
00:11:48,710 --> 00:11:48,720
although the buttons and things do have
 

621
00:11:48,720 --> 00:11:51,350
although the buttons and things do have
hover effects, it unfortunately does not

622
00:11:51,350 --> 00:11:51,360
hover effects, it unfortunately does not
 

623
00:11:51,360 --> 00:11:54,230
hover effects, it unfortunately does not
have any functionality behind that, you

624
00:11:54,230 --> 00:11:54,240
have any functionality behind that, you
 

625
00:11:54,240 --> 00:11:57,430
have any functionality behind that, you
know. So, and there was no clock. So, it

626
00:11:57,430 --> 00:11:57,440
know. So, and there was no clock. So, it
 

627
00:11:57,440 --> 00:12:00,069
know. So, and there was no clock. So, it
is what it is. Now, the Google one

628
00:12:00,069 --> 00:12:00,079
is what it is. Now, the Google one
 

629
00:12:00,079 --> 00:12:04,949
is what it is. Now, the Google one
again, this was Oh, I like that. So, it

630
00:12:04,949 --> 00:12:04,959
again, this was Oh, I like that. So, it
 

631
00:12:04,959 --> 00:12:06,550
again, this was Oh, I like that. So, it
basically just tells me, hey, this

632
00:12:06,550 --> 00:12:06,560
basically just tells me, hey, this
 

633
00:12:06,560 --> 00:12:08,150
basically just tells me, hey, this
doesn't work, but here's why it doesn't

634
00:12:08,150 --> 00:12:08,160
doesn't work, but here's why it doesn't
 

635
00:12:08,160 --> 00:12:10,550
doesn't work, but here's why it doesn't
work, so you can use these instead. I'm

636
00:12:10,550 --> 00:12:10,560
work, so you can use these instead. I'm
 

637
00:12:10,560 --> 00:12:12,069
work, so you can use these instead. I'm
okay with that. Really the big thing

638
00:12:12,069 --> 00:12:12,079
okay with that. Really the big thing
 

639
00:12:12,079 --> 00:12:13,590
okay with that. Really the big thing
that let this down because this did have

640
00:12:13,590 --> 00:12:13,600
that let this down because this did have
 

641
00:12:13,600 --> 00:12:15,670
that let this down because this did have
a functional clock as well is just the

642
00:12:15,670 --> 00:12:15,680
a functional clock as well is just the
 

643
00:12:15,680 --> 00:12:18,230
a functional clock as well is just the
lack of a background image here. And it

644
00:12:18,230 --> 00:12:18,240
lack of a background image here. And it
 

645
00:12:18,240 --> 00:12:21,430
lack of a background image here. And it
would be unfair of me, I think, to kind

646
00:12:21,430 --> 00:12:21,440
would be unfair of me, I think, to kind
 

647
00:12:21,440 --> 00:12:23,590
would be unfair of me, I think, to kind
of allow this one to fix itself and put

648
00:12:23,590 --> 00:12:23,600
of allow this one to fix itself and put
 

649
00:12:23,600 --> 00:12:25,910
of allow this one to fix itself and put
in a background image when the other

650
00:12:25,910 --> 00:12:25,920
in a background image when the other
 

651
00:12:25,920 --> 00:12:27,470
in a background image when the other
three did not need

652
00:12:27,470 --> 00:12:27,480
three did not need
 

653
00:12:27,480 --> 00:12:29,990
three did not need
that. Aside from that, this one worked

654
00:12:29,990 --> 00:12:30,000
that. Aside from that, this one worked
 

655
00:12:30,000 --> 00:12:32,190
that. Aside from that, this one worked
very well. The file explorer here is

656
00:12:32,190 --> 00:12:32,200
very well. The file explorer here is
 

657
00:12:32,200 --> 00:12:34,710
very well. The file explorer here is
arguably well done. It's simple. It's

658
00:12:34,710 --> 00:12:34,720
arguably well done. It's simple. It's
 

659
00:12:34,720 --> 00:12:37,230
arguably well done. It's simple. It's
elegant. And again, this kind of as

660
00:12:37,230 --> 00:12:37,240
elegant. And again, this kind of as
 

661
00:12:37,240 --> 00:12:40,389
elegant. And again, this kind of as
ChatgPT, DeepSseek, and Google Gemini

662
00:12:40,389 --> 00:12:40,399
ChatgPT, DeepSseek, and Google Gemini
 

663
00:12:40,399 --> 00:12:42,150
ChatgPT, DeepSseek, and Google Gemini
here gave us a bunch of different sample

664
00:12:42,150 --> 00:12:42,160
here gave us a bunch of different sample
 

665
00:12:42,160 --> 00:12:45,110
here gave us a bunch of different sample
file types. So, this is okay. Again,

666
00:12:45,110 --> 00:12:45,120
file types. So, this is okay. Again,
 

667
00:12:45,120 --> 00:12:46,470
file types. So, this is okay. Again,
really the only thing that kind of lets

668
00:12:46,470 --> 00:12:46,480
really the only thing that kind of lets
 

669
00:12:46,480 --> 00:12:47,910
really the only thing that kind of lets
this down is just the lack of a

670
00:12:47,910 --> 00:12:47,920
this down is just the lack of a
 

671
00:12:47,920 --> 00:12:49,990
this down is just the lack of a
background image. And it did actually

672
00:12:49,990 --> 00:12:50,000
background image. And it did actually
 

673
00:12:50,000 --> 00:12:52,470
background image. And it did actually
try to place one in. I should mention I

674
00:12:52,470 --> 00:12:52,480
try to place one in. I should mention I
 

675
00:12:52,480 --> 00:12:54,069
try to place one in. I should mention I
did try to have it fix it, but I figured

676
00:12:54,069 --> 00:12:54,079
did try to have it fix it, but I figured
 

677
00:12:54,079 --> 00:12:55,990
did try to have it fix it, but I figured
it wasn't fair for the testing since

678
00:12:55,990 --> 00:12:56,000
it wasn't fair for the testing since
 

679
00:12:56,000 --> 00:12:58,949
it wasn't fair for the testing since
this was one shot. So, it just kind of

680
00:12:58,949 --> 00:12:58,959
this was one shot. So, it just kind of
 

681
00:12:58,959 --> 00:13:00,949
this was one shot. So, it just kind of
said the Unsplash image may just not

682
00:13:00,949 --> 00:13:00,959
said the Unsplash image may just not
 

683
00:13:00,959 --> 00:13:02,949
said the Unsplash image may just not
show up. Sometimes that can happen. So

684
00:13:02,949 --> 00:13:02,959
show up. Sometimes that can happen. So
 

685
00:13:02,959 --> 00:13:04,710
show up. Sometimes that can happen. So
it did attempt to place a background

686
00:13:04,710 --> 00:13:04,720
it did attempt to place a background
 

687
00:13:04,720 --> 00:13:06,470
it did attempt to place a background
image. It unfortunately was not

688
00:13:06,470 --> 00:13:06,480
image. It unfortunately was not
 

689
00:13:06,480 --> 00:13:08,550
image. It unfortunately was not
functional. Likely through no fault of

690
00:13:08,550 --> 00:13:08,560
functional. Likely through no fault of
 

691
00:13:08,560 --> 00:13:11,629
functional. Likely through no fault of
the model itself, but

692
00:13:11,629 --> 00:13:11,639
the model itself, but
 

693
00:13:11,639 --> 00:13:13,590
the model itself, but
unfortunately I don't know what to say,

694
00:13:13,590 --> 00:13:13,600
unfortunately I don't know what to say,
 

695
00:13:13,600 --> 00:13:17,150
unfortunately I don't know what to say,
you know. So again, up to

696
00:13:17,150 --> 00:13:17,160
you know. So again, up to
 

697
00:13:17,160 --> 00:13:19,110
you know. So again, up to
interpretation. And then finally, we

698
00:13:19,110 --> 00:13:19,120
interpretation. And then finally, we
 

699
00:13:19,120 --> 00:13:22,150
interpretation. And then finally, we
have the Deepseek R1 result where this

700
00:13:22,150 --> 00:13:22,160
have the Deepseek R1 result where this
 

701
00:13:22,160 --> 00:13:24,550
have the Deepseek R1 result where this
one was different than the rest in that

702
00:13:24,550 --> 00:13:24,560
one was different than the rest in that
 

703
00:13:24,560 --> 00:13:27,190
one was different than the rest in that
it was more of a virtual operating

704
00:13:27,190 --> 00:13:27,200
it was more of a virtual operating
 

705
00:13:27,200 --> 00:13:30,389
it was more of a virtual operating
system look to it. Is that a W is that a

706
00:13:30,389 --> 00:13:30,399
system look to it. Is that a W is that a
 

707
00:13:30,399 --> 00:13:32,350
system look to it. Is that a W is that a
Windows icon and the start

708
00:13:32,350 --> 00:13:32,360
Windows icon and the start
 

709
00:13:32,360 --> 00:13:34,389
Windows icon and the start
button? All right. Well, let's just

710
00:13:34,389 --> 00:13:34,399
button? All right. Well, let's just
 

711
00:13:34,399 --> 00:13:35,389
button? All right. Well, let's just
ignore that.

712
00:13:35,389 --> 00:13:35,399
ignore that.
 

713
00:13:35,399 --> 00:13:38,870
ignore that.
But so, it didn't full screen this like

714
00:13:38,870 --> 00:13:38,880
But so, it didn't full screen this like
 

715
00:13:38,880 --> 00:13:41,269
But so, it didn't full screen this like
all the other three models did. I'm not

716
00:13:41,269 --> 00:13:41,279
all the other three models did. I'm not
 

717
00:13:41,279 --> 00:13:42,949
all the other three models did. I'm not
sure how I feel about that. I suppose

718
00:13:42,949 --> 00:13:42,959
sure how I feel about that. I suppose
 

719
00:13:42,959 --> 00:13:44,470
sure how I feel about that. I suppose
truthfully, I would have liked it a bit

720
00:13:44,470 --> 00:13:44,480
truthfully, I would have liked it a bit
 

721
00:13:44,480 --> 00:13:46,150
truthfully, I would have liked it a bit
more if it had actually full screened

722
00:13:46,150 --> 00:13:46,160
more if it had actually full screened
 

723
00:13:46,160 --> 00:13:47,990
more if it had actually full screened
this. Kind of akin to what this looks

724
00:13:47,990 --> 00:13:48,000
this. Kind of akin to what this looks
 

725
00:13:48,000 --> 00:13:50,230
this. Kind of akin to what this looks
like here. But aside from that,

726
00:13:50,230 --> 00:13:50,240
like here. But aside from that,
 

727
00:13:50,240 --> 00:13:52,310
like here. But aside from that,
functionally it did work properly. At

728
00:13:52,310 --> 00:13:52,320
functionally it did work properly. At
 

729
00:13:52,320 --> 00:13:54,230
functionally it did work properly. At
least the Notepad, the calculator, and

730
00:13:54,230 --> 00:13:54,240
least the Notepad, the calculator, and
 

731
00:13:54,240 --> 00:13:56,629
least the Notepad, the calculator, and
the file explorer. I will say this put

732
00:13:56,629 --> 00:13:56,639
the file explorer. I will say this put
 

733
00:13:56,639 --> 00:13:59,030
the file explorer. I will say this put
more desktop icons than the other

734
00:13:59,030 --> 00:13:59,040
more desktop icons than the other
 

735
00:13:59,040 --> 00:14:01,350
more desktop icons than the other
results which well okay Claude had four

736
00:14:01,350 --> 00:14:01,360
results which well okay Claude had four
 

737
00:14:01,360 --> 00:14:02,870
results which well okay Claude had four
as well but unfortunately they didn't

738
00:14:02,870 --> 00:14:02,880
as well but unfortunately they didn't
 

739
00:14:02,880 --> 00:14:05,829
as well but unfortunately they didn't
work. So Claude and Deepseek put four

740
00:14:05,829 --> 00:14:05,839
work. So Claude and Deepseek put four
 

741
00:14:05,839 --> 00:14:08,949
work. So Claude and Deepseek put four
desktop icons here and Chrome I'm sorry

742
00:14:08,949 --> 00:14:08,959
desktop icons here and Chrome I'm sorry
 

743
00:14:08,959 --> 00:14:11,910
desktop icons here and Chrome I'm sorry
not Chrome Google Gemini 2.5 Pro and

744
00:14:11,910 --> 00:14:11,920
not Chrome Google Gemini 2.5 Pro and
 

745
00:14:11,920 --> 00:14:15,350
not Chrome Google Gemini 2.5 Pro and
Chat GPT03 only put two but they do have

746
00:14:15,350 --> 00:14:15,360
Chat GPT03 only put two but they do have
 

747
00:14:15,360 --> 00:14:17,750
Chat GPT03 only put two but they do have
a working clock. The Deepseek one okay

748
00:14:17,750 --> 00:14:17,760
a working clock. The Deepseek one okay
 

749
00:14:17,760 --> 00:14:19,509
a working clock. The Deepseek one okay
DeepSeek also has a working clock.

750
00:14:19,509 --> 00:14:19,519
DeepSeek also has a working clock.
 

751
00:14:19,519 --> 00:14:22,310
DeepSeek also has a working clock.
Unfortunately Claw did not. And again,

752
00:14:22,310 --> 00:14:22,320
Unfortunately Claw did not. And again,
 

753
00:14:22,320 --> 00:14:26,069
Unfortunately Claw did not. And again,
this one really had a nice in-depth look

754
00:14:26,069 --> 00:14:26,079
this one really had a nice in-depth look
 

755
00:14:26,079 --> 00:14:28,949
this one really had a nice in-depth look
to it in terms of the actual UI and

756
00:14:28,949 --> 00:14:28,959
to it in terms of the actual UI and
 

757
00:14:28,959 --> 00:14:31,110
to it in terms of the actual UI and
browser navigation and things like that.

758
00:14:31,110 --> 00:14:31,120
browser navigation and things like that.
 

759
00:14:31,120 --> 00:14:32,629
browser navigation and things like that.
Okay, that can go off screen, which is

760
00:14:32,629 --> 00:14:32,639
Okay, that can go off screen, which is
 

761
00:14:32,639 --> 00:14:34,870
Okay, that can go off screen, which is
bad, but there was translucence to the

762
00:14:34,870 --> 00:14:34,880
bad, but there was translucence to the
 

763
00:14:34,880 --> 00:14:37,189
bad, but there was translucence to the
windows. The icons were somewhat modern

764
00:14:37,189 --> 00:14:37,199
windows. The icons were somewhat modern
 

765
00:14:37,199 --> 00:14:41,069
windows. The icons were somewhat modern
and not necessarily like black and

766
00:14:41,069 --> 00:14:41,079
and not necessarily like black and
 

767
00:14:41,079 --> 00:14:43,670
and not necessarily like black and
white like that.

768
00:14:43,670 --> 00:14:43,680
white like that.
 

769
00:14:43,680 --> 00:14:46,350
white like that.
So overall, I will

770
00:14:46,350 --> 00:14:46,360
So overall, I will
 

771
00:14:46,360 --> 00:14:49,629
So overall, I will
say they all

772
00:14:49,629 --> 00:14:49,639
say they all
 

773
00:14:49,639 --> 00:14:53,590
say they all
produced results, which I suppose was

774
00:14:53,590 --> 00:14:53,600
produced results, which I suppose was
 

775
00:14:53,600 --> 00:14:55,509
produced results, which I suppose was
the whole purpose of this testing. But

776
00:14:55,509 --> 00:14:55,519
the whole purpose of this testing. But
 

777
00:14:55,519 --> 00:14:57,030
the whole purpose of this testing. But
really, this was just kind of

778
00:14:57,030 --> 00:14:57,040
really, this was just kind of
 

779
00:14:57,040 --> 00:14:58,870
really, this was just kind of
interesting because this is definitely a

780
00:14:58,870 --> 00:14:58,880
interesting because this is definitely a
 

781
00:14:58,880 --> 00:15:01,189
interesting because this is definitely a
more in-depth prompt and test to have

782
00:15:01,189 --> 00:15:01,199
more in-depth prompt and test to have
 

783
00:15:01,199 --> 00:15:03,990
more in-depth prompt and test to have
here than my kind of typical in local

784
00:15:03,990 --> 00:15:04,000
here than my kind of typical in local
 

785
00:15:04,000 --> 00:15:05,590
here than my kind of typical in local
LLM testing where I'm just like, "Hey,

786
00:15:05,590 --> 00:15:05,600
LLM testing where I'm just like, "Hey,
 

787
00:15:05,600 --> 00:15:07,670
LLM testing where I'm just like, "Hey,
make a retro Python game." because these

788
00:15:07,670 --> 00:15:07,680
make a retro Python game." because these
 

789
00:15:07,680 --> 00:15:10,150
make a retro Python game." because these
models are some of them very expensive,

790
00:15:10,150 --> 00:15:10,160
models are some of them very expensive,
 

791
00:15:10,160 --> 00:15:13,189
models are some of them very expensive,
state-of-the-art, and supposedly very

792
00:15:13,189 --> 00:15:13,199
state-of-the-art, and supposedly very
 

793
00:15:13,199 --> 00:15:15,750
state-of-the-art, and supposedly very
capable. I will say, and being that I

794
00:15:15,750 --> 00:15:15,760
capable. I will say, and being that I
 

795
00:15:15,760 --> 00:15:18,230
capable. I will say, and being that I
have a main focus on open source here,

796
00:15:18,230 --> 00:15:18,240
have a main focus on open source here,
 

797
00:15:18,240 --> 00:15:19,990
have a main focus on open source here,
there is only one model here that I

798
00:15:19,990 --> 00:15:20,000
there is only one model here that I
 

799
00:15:20,000 --> 00:15:21,430
there is only one model here that I
could actually go and download and run

800
00:15:21,430 --> 00:15:21,440
could actually go and download and run
 

801
00:15:21,440 --> 00:15:23,509
could actually go and download and run
on my local machine, assuming I had a

802
00:15:23,509 --> 00:15:23,519
on my local machine, assuming I had a
 

803
00:15:23,519 --> 00:15:24,790
on my local machine, assuming I had a
machine that could run the model, which

804
00:15:24,790 --> 00:15:24,800
machine that could run the model, which
 

805
00:15:24,800 --> 00:15:27,030
machine that could run the model, which
I don't, and that is Deepseek. So,

806
00:15:27,030 --> 00:15:27,040
I don't, and that is Deepseek. So,
 

807
00:15:27,040 --> 00:15:29,990
I don't, and that is Deepseek. So,
essentially, this one is open- source.

808
00:15:29,990 --> 00:15:30,000
essentially, this one is open- source.
 

809
00:15:30,000 --> 00:15:32,550
essentially, this one is open- source.
The rest are not. So, that is also kind

810
00:15:32,550 --> 00:15:32,560
The rest are not. So, that is also kind
 

811
00:15:32,560 --> 00:15:34,470
The rest are not. So, that is also kind
of something to consider massively here.

812
00:15:34,470 --> 00:15:34,480
of something to consider massively here.
 

813
00:15:34,480 --> 00:15:37,030
of something to consider massively here.
But aside from that, all of these models

814
00:15:37,030 --> 00:15:37,040
But aside from that, all of these models
 

815
00:15:37,040 --> 00:15:39,110
But aside from that, all of these models
would be able to fix these results and

816
00:15:39,110 --> 00:15:39,120
would be able to fix these results and
 

817
00:15:39,120 --> 00:15:40,870
would be able to fix these results and
make them what you wanted. Obviously, in

818
00:15:40,870 --> 00:15:40,880
make them what you wanted. Obviously, in
 

819
00:15:40,880 --> 00:15:42,790
make them what you wanted. Obviously, in
a real world development scenario, you

820
00:15:42,790 --> 00:15:42,800
a real world development scenario, you
 

821
00:15:42,800 --> 00:15:44,470
a real world development scenario, you
would not be oneshot testing things like

822
00:15:44,470 --> 00:15:44,480
would not be oneshot testing things like
 

823
00:15:44,480 --> 00:15:46,069
would not be oneshot testing things like
this. You would basically start with

824
00:15:46,069 --> 00:15:46,079
this. You would basically start with
 

825
00:15:46,079 --> 00:15:47,430
this. You would basically start with
something like this, and then go back to

826
00:15:47,430 --> 00:15:47,440
something like this, and then go back to
 

827
00:15:47,440 --> 00:15:49,189
something like this, and then go back to
the model and say, "Okay, here's what I

828
00:15:49,189 --> 00:15:49,199
the model and say, "Okay, here's what I
 

829
00:15:49,199 --> 00:15:50,629
the model and say, "Okay, here's what I
like. Here's what I want you to change,

830
00:15:50,629 --> 00:15:50,639
like. Here's what I want you to change,
 

831
00:15:50,639 --> 00:15:51,749
like. Here's what I want you to change,
and then you would just kind of

832
00:15:51,749 --> 00:15:51,759
and then you would just kind of
 

833
00:15:51,759 --> 00:15:55,110
and then you would just kind of
iteratively develop it into a more

834
00:15:55,110 --> 00:15:55,120
iteratively develop it into a more
 

835
00:15:55,120 --> 00:15:56,949
iteratively develop it into a more
aesthetically pleasing and functional

836
00:15:56,949 --> 00:15:56,959
aesthetically pleasing and functional
 

837
00:15:56,959 --> 00:15:59,430
aesthetically pleasing and functional
result." With that, I suppose that is

838
00:15:59,430 --> 00:15:59,440
result." With that, I suppose that is
 

839
00:15:59,440 --> 00:16:01,910
result." With that, I suppose that is
going to kind of sum up and end this

840
00:16:01,910 --> 00:16:01,920
going to kind of sum up and end this
 

841
00:16:01,920 --> 00:16:03,509
going to kind of sum up and end this
quick little video. I wanted to just

842
00:16:03,509 --> 00:16:03,519
quick little video. I wanted to just
 

843
00:16:03,519 --> 00:16:05,189
quick little video. I wanted to just
test all of these in some form of

844
00:16:05,189 --> 00:16:05,199
test all of these in some form of
 

845
00:16:05,199 --> 00:16:07,189
test all of these in some form of
obscure head-to-head with the same

846
00:16:07,189 --> 00:16:07,199
obscure head-to-head with the same
 

847
00:16:07,199 --> 00:16:10,949
obscure head-to-head with the same
prompt. And yeah, so thank you for

848
00:16:10,949 --> 00:16:10,959
prompt. And yeah, so thank you for
 

849
00:16:10,959 --> 00:16:12,710
prompt. And yeah, so thank you for
watching and if you have any questions,

850
00:16:12,710 --> 00:16:12,720
watching and if you have any questions,
 

851
00:16:12,720 --> 00:16:14,150
watching and if you have any questions,
please feel free to leave them in the

852
00:16:14,150 --> 00:16:14,160
please feel free to leave them in the
 

853
00:16:14,160 --> 00:16:16,320
please feel free to leave them in the
comments.

