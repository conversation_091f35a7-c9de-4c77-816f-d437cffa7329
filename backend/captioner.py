import subprocess
from typing import List
from utils import logger # Import logger directly

def burn_captions(clip_paths: List[str], srt_path: str) -> List[str]:
    """Burns subtitles from an SRT file onto a list of video clips using FFmpeg.

    Args:
        clip_paths (List[str]): A list of paths to the input video clips.
        srt_path (str): The path to the SRT subtitle file.

    Returns:
        List[str]: A list of paths to the video clips with burned captions.
    """
    out_paths = []
    logger.info(f"Starting to burn captions from '{srt_path}' onto {len(clip_paths)} clip(s).")
    for idx, path in enumerate(clip_paths):
        out_path = path.replace(".mp4", "_captioned.mp4")
        logger.info(f"Burning captions for clip {idx+1}/{len(clip_paths)}: '{path}' -> '{out_path}'")
        
        # TODO: Review and enhance srt_path escaping for FFmpeg robustness.
        ffmpeg_command = [
            "ffmpeg", "-y", 
            "-i", path, 
            "-vf", f"subtitles='{srt_path}'",  # Path escaping might be needed here
            "-c:a", "copy", 
            out_path
        ]
        
        try:
            subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True)
            logger.info(f"Successfully burned captions for '{out_path}'.")
            out_paths.append(out_path)
        except subprocess.CalledProcessError as e:
            logger.error(f"[FFmpeg] Error burning captions for clip '{path}'.")
            logger.error(f"[FFmpeg] Command: {' '.join(ffmpeg_command)}")
            logger.error(f"[FFmpeg] STDOUT: {e.stdout}")
            logger.error(f"[FFmpeg] STDERR: {e.stderr}")
            # Decide if we should skip this clip and continue, or raise the error
            # For now, continuing to the next clip by not re-raising
            # If one clip failing should stop all, then re-raise e
            
    logger.info(f"Finished burning captions. {len(out_paths)} clips processed.")
    return out_paths