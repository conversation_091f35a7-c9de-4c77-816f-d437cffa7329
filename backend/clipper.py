import subprocess
import os
from typing import List, Dict
from utils import logger, parse_time

# TODO: Centralize OUTPUT_DIR configuration (e.g., from a config module or passed as arg)
OUTPUT_BASE_DIR = "./outputs"

def create_clips(video_path: str, timestamp_list: List[Dict[str, str]], job_id: str) -> List[str]:
    """Creates multiple video clips from a single video file based on timestamps.

    Args:
        video_path (str): The path to the source video file.
        timestamp_list (List[Dict[str, str]]): A list of dictionaries, where each 
            dictionary must have "start" and "end" keys with time strings 
            (e.g., "00:01:12,345" or "00:01:12.345").
        job_id (str): A unique identifier for the job, used in output filenames.

    Returns:
        List[str]: A list of paths to the created video clips.
    """
    clips = []
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True) # Ensure base output directory exists
    logger.info(f"Starting to create {len(timestamp_list)} clips for job '{job_id}' from '{video_path}'.")

    for i, entry in enumerate(timestamp_list):
        try:
            raw_start_time = entry["start"]
            raw_end_time = entry["end"]
            
            # Convert string timestamps to float seconds for FFmpeg
            start_sec = parse_time(raw_start_time)
            end_sec = parse_time(raw_end_time)

            if start_sec >= end_sec:
                logger.warning(f"Skipping clip {i+1} for job '{job_id}': start time ({start_sec}s) is not before end time ({end_sec}s).")
                continue

            clip_filename = f"{job_id}_clip_{i+1}.mp4"
            out_path = os.path.join(OUTPUT_BASE_DIR, clip_filename)
            
            logger.info(f"Creating clip {i+1}/{len(timestamp_list)}: '{out_path}' (Start: {start_sec:.2f}s, End: {end_sec:.2f}s)")

            # TODO: Parameterize scale (720:1280) and codec settings if flexibility is needed.
            ffmpeg_command = [
                "ffmpeg", "-y",
                "-ss", str(start_sec),    # Use float seconds for -ss
                "-i", video_path,         # Input after -ss for faster seeking
                "-to", str(end_sec),      # Use float seconds for -to
                "-vf", "scale=720:1280", 
                "-c:v", "libx264", "-crf", "22", "-preset", "fast",
                "-c:a", "aac", "-b:a", "128k", 
                "-movflags", "+faststart", # Added for web streaming
                out_path
            ]

            subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True)
            logger.info(f"Successfully created clip: '{out_path}'.")
            clips.append(out_path)
        except KeyError as e:
            logger.error(f"Skipping clip creation for entry {entry} due to missing key: {e}")
            continue
        except ValueError as e: # Catch errors from parse_time
            logger.error(f"Skipping clip creation for entry {entry} due to invalid time format: {e}")
            continue
        except subprocess.CalledProcessError as e:
            logger.error(f"[FFmpeg] Error creating clip '{out_path if 'out_path' in locals() else 'unknown'}'.")
            logger.error(f"[FFmpeg] Command: {' '.join(ffmpeg_command if 'ffmpeg_command' in locals() else [])}")
            logger.error(f"[FFmpeg] STDOUT: {e.stdout}")
            logger.error(f"[FFmpeg] STDERR: {e.stderr}")
            continue # Continue to the next clip
        except Exception as e:
            logger.error(f"Unexpected error creating clip for entry {entry}: {e}")
            continue
            
    logger.info(f"Finished creating clips for job '{job_id}'. {len(clips)} clips created.")
    return clips