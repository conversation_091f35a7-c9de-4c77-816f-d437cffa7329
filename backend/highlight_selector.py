import os
import re
import json
import requests
from utils import (
    chunk_srt, parse_time, parse_srt_timecode, 
    extract_srt_segment, logger, call_dashscope_qwen
)
from video_editor import generate_clip_with_effects
from dotenv import load_dotenv
from typing import List, Dict, Any, Tuple, Optional

load_dotenv()

# ---- System Prompts ----
SYSTEM_PROMPTS: Dict[str, str] = {
    "tiktok": """
You are a viral content strategist for TikTok Shorts. Extract 15–60s segments from transcript that:
- Hook in the first 3 seconds
- Are emotionally charged, surprising, or relatable
- Can stand alone
Respond ONLY as JSON in this format:
[
  {"start": "00:01:12", "end": "00:01:38", "reason": "Funny contradiction with a strong hook"}
]
Transcript:
{chunk}
""",
    "youtube": """
You are a YouTube Shorts editor. Extract 15–60s segments that:
- Begin with curiosity
- End with payoff
- Have an emotional or controversial arc
Respond ONLY as JSO<PERSON> in this format:
[
  {"start": "00:00:44", "end": "00:01:12", "reason": "Bold statement leading to powerful insight"}
]
Transcript:
{chunk}
""",
    "reels": """
You are an Instagram Reels editor. Extract up to 3 segments (15–60s) that:
- Are expressive, personal, and emotionally resonant
- Appeal to age 20–40
Respond ONLY as JSON in this format:
[
  {"start": "00:05:11", "end": "00:05:42", "reason": "Raw personal moment with emotional impact"}
]
Transcript:
{chunk}
"""
}

# ---- Title Generation Prompt ----
TITLE_PROMPT: str = """
You are a viral video title expert. From this transcript, write a short title (max 12 words) that:
- Stops scrolling
- Creates curiosity or emotion
- Works for TikTok/Shorts/Reels

Title:
"""

# ---- Main Functionality ----
def get_clip_timestamps_from_transcript(srt_text: str, platform: str = "tiktok") -> List[Dict[str, str]]:
    """Extracts potential video clip timestamps from an SRT transcript using Qwen (Dashscope).

    Args:
        srt_text (str): The full SRT transcript.
        platform (str, optional): The target platform (e.g., "tiktok", "youtube"). 
                                Defaults to "tiktok".

    Returns:
        List[Dict[str, str]]: A list of dictionaries, each with "start", "end", 
                              and "reason" for the clip. Sorted by start time. Returns empty list on error.
    """
    qwen_api_key = os.getenv('QWEN_API_KEY')
    if not qwen_api_key:
        logger.error("QWEN_API_KEY not found. Cannot get clip timestamps.")
        return []

    chunks = chunk_srt(srt_text)
    all_clips: List[Dict[str, str]] = []
    prompt_template = SYSTEM_PROMPTS.get(platform, SYSTEM_PROMPTS["tiktok"])

    for i, chunk in enumerate(chunks):
        logger.info(f"[LLM] Processing chunk {i+1}/{len(chunks)} for {platform.upper()} (Dashscope Qwen)...")
        current_prompt_text = prompt_template.replace("{chunk}", chunk.strip())
        
        # Use utils.call_dashscope_qwen, expecting a JSON list of dicts in string form
        response_content = call_dashscope_qwen(
            api_key=qwen_api_key,
            model_name="qwen-turbo", # Assuming qwen-turbo for this task as well
            system_prompt=current_prompt_text, # The system prompt for this model contains the instructions & output format
            user_prompt=chunk.strip(), # The user prompt is the chunk itself as per original logic
            is_json_response_expected=True
        )

        if response_content and isinstance(response_content, list):
            # Ensure all elements are dicts, as expected by subsequent code
            validated_clips_from_chunk = [clip for clip in response_content if isinstance(clip, dict)]
            all_clips.extend(validated_clips_from_chunk)
            if len(validated_clips_from_chunk) != len(response_content):
                logger.warning("Some elements in the response were not dictionaries and were filtered out.")
        elif response_content: # It was not a list, but not None
             logger.warning(f"call_dashscope_qwen did not return a list for chunk {i+1}. Got: {type(response_content)}. Content: {str(response_content)[:200]}...")
        # If response_content is None, error already logged by call_dashscope_qwen

    def clip_duration(c: Dict[str, str]) -> float: 
        return parse_time(c["end"]) - parse_time(c["start"])
    
    valid_clips: List[Dict[str, str]] = []
    for c_idx, c in enumerate(all_clips):
        try:
            if not isinstance(c, dict) or not all(k in c for k in ["start", "end"]):
                logger.warning(f"Skipping clip entry #{c_idx} due to missing keys or incorrect type: {c}")
                continue
            duration = clip_duration(c)
            if 15 <= duration <= 60:
                valid_clips.append(c)
        except Exception as e:
            logger.warning(f"Skipping clip entry #{c_idx} ({c}) due to error processing: {e}")
            continue
            
    return sorted(valid_clips, key=lambda x: parse_time(x["start"])) 


def _generate_title_with_dashscope(transcript_chunk: str, qwen_api_key: str) -> str:
    """Generates a title for a transcript chunk using Qwen (Dashscope).
       Helper function for process_and_render_clips.
    """
    if not qwen_api_key:
        logger.error("QWEN_API_KEY not available for title generation.")
        return "Default Title (API Key Error)"

    # Use utils.call_dashscope_qwen, expecting raw text response
    title_text = call_dashscope_qwen(
        api_key=qwen_api_key,
        model_name="qwen-turbo", # Assuming qwen-turbo for titles
        system_prompt=TITLE_PROMPT.strip(), # General title prompt
        user_prompt=transcript_chunk.strip(),
        is_json_response_expected=False
    )
    if title_text and isinstance(title_text, str):
        return title_text.strip('"\\n ')
    return "Default Title (Generation Failed)"


def process_and_render_clips(
    input_video: str, 
    srt_text: str, 
    output_dir: str, 
    subtitle_path: str, 
    platform: str,
    background_music_path: str = "assets/music/bg.mp3"
) -> None:
    """Identifies clips, generates titles, and renders them with effects.

    Args:
        input_video (str): Path to the input video file.
        srt_text (str): The full SRT transcript.
        output_dir (str): Directory to save the output clips.
        subtitle_path (str): Path to the SRT subtitle file for burning.
        platform (str): The target platform (e.g., "tiktok").
        background_music_path (str, optional): Path to the background music file.
                                                Defaults to "assets/music/bg.mp3".
    """
    qwen_api_key = os.getenv('QWEN_API_KEY') # Get key once for this function

    clips = get_clip_timestamps_from_transcript(srt_text, platform=platform)
    if not clips:
        logger.info("No valid clips identified. Exiting process_and_render_clips.")
        return
        
    os.makedirs(output_dir, exist_ok=True)

    for idx, clip_info in enumerate(clips):
        try:
            if not isinstance(clip_info, dict) or not all(k in clip_info for k in ["start", "end"]):
                logger.warning(f"Skipping clip rendering due to missing keys or incorrect type in clip_info: {clip_info}")
                continue

            start_sec = parse_time(clip_info["start"])
            end_sec = parse_time(clip_info["end"])
            segment = extract_srt_segment(srt_text, start_sec, end_sec)
            
            title = _generate_title_with_dashscope(segment, qwen_api_key)
            if not title or title.startswith("Default Title") : 
                default_reason = clip_info.get("reason", f"Clip_{idx+1}")
                title = f"Video Clip: {default_reason[:30]}" # Use reason for a more descriptive default
                logger.warning(f"Using default title for segment: {segment[:100]}... Default: '{title}'")

            safe_title_segment = "".join(c for c in title if c.isalnum() or c in (" ", "_", "-")).rstrip()[:50] # Max length for filename segment
            output_clip_path = os.path.join(output_dir, f"clip_{idx+1}_{safe_title_segment}.mp4")

            logger.info(f"Rendering clip {idx+1}/{len(clips)}: {title} [{clip_info['start']} -> {clip_info['end']}]")
            generate_clip_with_effects(
                input_video_path=input_video,
                output_path=output_clip_path,
                start_time=start_sec, 
                end_time=end_sec,   
                subtitle_path=subtitle_path,
                overlay_text=title,
                background_music=background_music_path
            )
            logger.info(f"✅ Clip saved: {output_clip_path}")
        except Exception as e:
            logger.error(f"Error processing or rendering clip {idx+1} ({clip_info}): {e}", exc_info=True)
            continue
