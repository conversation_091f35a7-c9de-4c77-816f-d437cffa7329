from faster_whisper import WhisperModel
import subprocess
import os
from utils import logger, format_time

# TODO: Consider managing model lifecycle (e.g., lazy loading) if resource use is a concern.
# For now, loading the model when the module is imported.
logger.info("Initializing WhisperModel (base, int8)... This may take a moment.")
try:
    model = WhisperModel("base", compute_type="int8")
    logger.info("WhisperModel initialized successfully.")
except Exception as e:
    logger.error(f"Failed to initialize WhisperModel: {e}")
    # Depending on the application's needs, you might want to raise the error
    # or have a fallback mechanism. For now, subsequent calls will fail.
    model = None 

# TODO: Centralize OUTPUT_DIR configuration
OUTPUT_BASE_DIR = "./outputs"

def transcribe_video(path: str, job_id: str) -> str | None:
    """Transcribes a video file to an SRT subtitle file using WhisperModel.

    Args:
        path (str): The path to the video file to transcribe.
        job_id (str): A unique identifier for the job, used in the SRT filename.

    Returns:
        str | None: The path to the generated SRT file, or None if transcription fails.
    """
    if not model:
        logger.error("WhisperModel is not initialized. Cannot transcribe.")
        return None

    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    srt_filename = f"{job_id}.srt"
    srt_path = os.path.join(OUTPUT_BASE_DIR, srt_filename)

    logger.info(f"Starting transcription for video: '{path}', job_id: '{job_id}'. Output: '{srt_path}'")
    try:
        # segments is a generator
        segments, info = model.transcribe(path, beam_size=5)
        logger.info(f"Detected language '{info.language}' with probability {info.language_probability:.2f}")
        logger.info(f"Transcription (segments processing) for '{path}' started...")

        with open(srt_path, "w", encoding="utf-8") as f:
            idx = 1
            for seg in segments:
                # format_time is imported from utils
                f.write(f"{idx}\n{format_time(seg.start)} --> {format_time(seg.end)}\n{seg.text.strip()}\n\n")
                idx += 1
        logger.info(f"Successfully transcribed video '{path}' to '{srt_path}'.")
        return srt_path
    except Exception as e:
        logger.error(f"Error during transcription or SRT file writing for '{path}': {e}")
        return None

# Removed redundant local format_time function, as it's imported from utils.py

def convert_vtt_to_srt(vtt_path: str, srt_path: str) -> bool:
    """Converts a VTT subtitle file to SRT format using FFmpeg.

    Args:
        vtt_path (str): Path to the input VTT file.
        srt_path (str): Path to save the output SRT file.

    Returns:
        bool: True if conversion was successful, False otherwise.
    """
    logger.info(f"Converting VTT file '{vtt_path}' to SRT file '{srt_path}'.")
    # Ensure output directory for srt_path exists if it's different from a known one
    os.makedirs(os.path.dirname(srt_path), exist_ok=True)

    ffmpeg_command = ["ffmpeg", "-y", "-i", vtt_path, srt_path]
    try:
        subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True)
        logger.info(f"Successfully converted '{vtt_path}' to '{srt_path}'.")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"[FFmpeg] Error converting VTT '{vtt_path}' to SRT '{srt_path}'.")
        logger.error(f"[FFmpeg] Command: {' '.join(ffmpeg_command)}")
        logger.error(f"[FFmpeg] STDOUT: {e.stdout}")
        logger.error(f"[FFmpeg] STDERR: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during VTT to SRT conversion for '{vtt_path}': {e}")
        return False