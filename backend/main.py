from fastapi import FastAP<PERSON>, UploadFile, File, Form
from downloader import download_youtube
from transcriber import transcribe_video, convert_vtt_to_srt
from highlight_selector import get_clip_timestamps_from_transcript
from clipper import create_clips
from captioner import burn_captions
import shutil
import os
import uuid

app = FastAPI()

UPLOAD_DIR = "./uploads"
OUTPUT_DIR = "./outputs"
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

@app.post("/upload")
async def upload_video(file: UploadFile = File(...)):
    """Handles video file uploads, saves the file, and initiates processing.

    Args:
        file (UploadFile): The video file to upload.

    Returns:
        dict: A dictionary containing the job_id and processed clip information.
    """
    job_id = str(uuid.uuid4())
    path = os.path.join(UPLOAD_DIR, f"{job_id}.mp4")
    with open(path, "wb") as f:
        shutil.copyfileobj(file.file, f)
    return await process_pipeline(job_id, path, None)

@app.post("/youtube")
async def process_youtube(url: str = Form(...)):
    """Downloads a YouTube video, transcribes it, and processes it for clips.

    Args:
        url (str): The URL of the YouTube video.

    Returns:
        dict: A dictionary containing the job_id and processed clip information.
    """
    job_id = str(uuid.uuid4())
    video_path, subtitle_path = download_youtube(url, job_id)

    if subtitle_path:
        srt_path = os.path.join(OUTPUT_DIR, f"{job_id}.srt")
        convert_vtt_to_srt(subtitle_path, srt_path)
    else:
        srt_path = transcribe_video(video_path, job_id)

    return await process_pipeline(job_id, video_path, srt_path)

async def process_pipeline(job_id, video_path, srt_path):
    """Core processing pipeline for a video.

    Transcribes the video (if needed), identifies highlight clips,
    creates the clips, and adds captions.

    Args:
        job_id (str): The unique identifier for this processing job.
        video_path (str): The path to the video file.
        srt_path (str | None): The path to the SRT subtitle file, if available.

    Returns:
        dict: A dictionary containing the job_id and processed clip information.
    """
    if not srt_path:
        srt_path = transcribe_video(video_path, job_id)

    with open(srt_path, "r") as f:
        transcript = f.read()

    timestamps = get_clip_timestamps_from_transcript(transcript)
    clips = create_clips(video_path, timestamps, job_id)
    final = burn_captions(clips, srt_path)
    return {"job_id": job_id, "clips": final}