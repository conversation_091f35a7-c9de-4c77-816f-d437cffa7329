import os
import re
import json
import logging
import requests
from datetime import datetime
from typing import List, Dict, Any, Union

# ---------------------------
# 📦 LOGGING CONFIG
# ---------------------------
def setup_logger(name: str = "klap", log_file: str = "pipeline.log") -> logging.Logger:
    """Sets up and returns a logger instance.

    Args:
        name (str, optional): The name of the logger. Defaults to "klap".
        log_file (str, optional): The file to which logs will be written. 
                                Defaults to "pipeline.log".

    Returns:
        logging.Logger: The configured logger instance.
    """
    formatter = logging.Formatter('%(asctime)s %(levelname)s: %(message)s')

    handler = logging.FileHandler(log_file)
    handler.setFormatter(formatter)

    console = logging.StreamHandler()
    console.setFormatter(formatter)

    logger_instance = logging.getLogger(name)
    logger_instance.setLevel(logging.DEBUG)
    logger_instance.addHandler(handler)
    logger_instance.addHandler(console)

    return logger_instance

logger = setup_logger()


# ---------------------------
# 📁 FILE + PATH HELPERS
# ---------------------------
def ensure_dir(path: str) -> None:
    """Ensures that a directory exists, creating it if necessary.

    Args:
        path (str): The path to the directory.
    """
    os.makedirs(path, exist_ok=True)

def timestamp() -> str:
    """Generates a timestamp string in YYYY-MM-DD_HH-MM-SS format.

    Returns:
        str: The formatted timestamp string.
    """
    return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")


# ---------------------------
# ⏱️ TIMECODE HELPERS
# ---------------------------
def parse_time(s: str) -> float:
    """Converts a time string like 'HH:MM:SS.mmm', 'HH:MM:SS,mmm', or 'HH:MM:SS:mmm' to seconds.

    Args:
        s (str): The time string.

    Returns:
        float: The time in seconds.
    """
    parts = s.split(':')
    if len(parts) == 3:  # HH:MM:SS.mmm or HH:MM:SS,mmm
        h, m, s_ms_str = parts
        s_ms_str = s_ms_str.replace(',', '.')
        if '.' in s_ms_str:
            sec, ms = map(float, s_ms_str.split('.', 1))
            return int(h) * 3600 + int(m) * 60 + sec + ms / 1000.0
        else: # Assume it's just seconds if no separator after last colon
            return int(h) * 3600 + int(m) * 60 + float(s_ms_str)
    elif len(parts) == 4:  # HH:MM:SS:mmm (LLM output from logs)
        h, m, sec, ms = map(int, parts)
        return h * 3600 + m * 60 + sec + ms / 1000.0
    else:
        # Fallback or error for unexpected format
        # For now, trying the original logic if it doesn't match new patterns,
        # though this might still lead to errors if the format is truly unexpected.
        # A more robust solution might raise a specific ValueError here.
        try:
            # Attempt original logic as a last resort, or raise error
            h_orig, m_orig, s_ms_orig = re.split("[:,]", s, maxsplit=2)
            sec_part_orig = float(s_ms_orig.replace(",", "."))
            return int(h_orig) * 3600 + int(m_orig) * 60 + sec_part_orig
        except ValueError as e:
            logger.error(f"Cannot parse time string: {s}. Original error: {e}")
            raise ValueError(f"Time string '{s}' is not in a recognized HH:MM:SS.mmm, HH:MM:SS,mmm, or HH:MM:SS:mmm format.")

def parse_srt_timecode(srt_str: str) -> float:
    """Converts an SRT timecode string 'HH:MM:SS,mmm' to seconds.

    Args:
        srt_str (str): The SRT timecode string.

    Returns:
        float: The time in seconds.
    """
    h, m, rest = srt_str.split(":")
    s, ms = rest.split(",")
    return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000.0

def format_time(seconds: float) -> str:
    """Converts float seconds to an SRT timecode string 'HH:MM:SS,mmm'.

    Args:
        seconds (float): The time in seconds.

    Returns:
        str: The SRT timecode string.
    """
    h = int(seconds // 3600)
    m = int((seconds % 3600) // 60)
    s = int(seconds % 60)
    ms = int((seconds % 1) * 1000)
    return f"{h:02}:{m:02}:{s:02},{ms:03}"


# ---------------------------
# ✂️ SRT CHUNKING
# ---------------------------
def chunk_srt(srt_text: str, max_tokens: int = 1000) -> List[str]:
    """Splits a long SRT formatted string into smaller chunks based on estimated tokens.

    Args:
        srt_text (str): The SRT content as a string.
        max_tokens (int, optional): The maximum estimated tokens per chunk. 
                                    Defaults to 1000.

    Returns:
        List[str]: A list of SRT chunk strings.
    """
    entries = srt_text.strip().split("\n\n")
    chunks = []
    current_chunk = []
    current_token_count = 0

    for entry in entries:
        lines = entry.strip().split("\n")
        if len(lines) < 3:
            continue
        timestamp, text = lines[1], " ".join(lines[2:])
        estimated_tokens = len(text.split())

        if current_token_count + estimated_tokens > max_tokens:
            chunks.append("\n".join(current_chunk))
            current_chunk = []
            current_token_count = 0

        current_chunk.append(entry)
        current_token_count += estimated_tokens

    if current_chunk:
        chunks.append("\n".join(current_chunk))

    return chunks


# ---------------------------
# 🤖 CALL LLM (Qwen / qwen)
# ---------------------------

def call_together_qwen(system_prompt: str, user_prompt: str) -> Union[List[Any], Dict[str, Any]]:
    """Calls the Qwen LLM API (via Together AI) with the given prompts.
    Note: Uses TOGETHER_API_KEY environment variable.

    Args:
        system_prompt (str): The system prompt for the LLM.
        user_prompt (str): The user prompt for the LLM.

    Returns:
        Union[List[Any], Dict[str, Any]]: The JSON response from the LLM,
                                         or an empty list if an error occurs.
    """
    api_key = os.getenv("QWEN_API_KEY")
    model_name = "qwen-turbo"
    if not api_key:
        logger.error("QWEN_API_KEY not found in environment variables for call_qwen.")
        return []

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model_name,
        "temperature": 0.7,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    }
    try:
        logger.debug(f"Calling Qwen API. Model: {model_name}")
        res = requests.post(
            "https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers=headers,
            data=json.dumps(payload),
            timeout=60
        )
        res.raise_for_status()
        content = res.json()["choices"][0]["message"]["content"]
        
        # Attempt to parse as JSON if content is a JSON string, otherwise return as is (or handle specific format)
        try:
            # Check if the content itself is a JSON string (e.g. a list of dicts)
            parsed_content = json.loads(content)
            return parsed_content
        except json.JSONDecodeError:
            # If not a JSON string, it might be plain text or another structure
            # For now, let's see if it contains ```json ... ``` block like Groq does
            match = re.search(r"```json\n(.*?)\n```", content, re.DOTALL)
            if match:
                return json.loads(match.group(1))
            return content # Return raw content if no JSON structure is found

    except requests.exceptions.RequestException as e:
        logger.error(f"Together Qwen API request error: {e}")
        return []
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        logger.error(f"Error processing Together Qwen API response: {e} - Response: {res.text if 'res' in locals() else 'N/A'}")
        return []
    except Exception as e:
        logger.error(f"Qwen API general error: {e}")
        return []

def call_dashscope_qwen(api_key: str, model_name: str, system_prompt: str, user_prompt: str, is_json_response_expected: bool = False) -> Union[str, List[Dict[str, str]], Dict[str, Any], None]:
    """Calls the Qwen LLM API (Dashscope) with the given parameters.

    Args:
        api_key (str): The API key for Dashscope.
        model_name (str): The Qwen model to use (e.g., "qwen-turbo").
        system_prompt (str): The system prompt for the LLM.
        user_prompt (str): The user prompt for the LLM.
        is_json_response_expected (bool, optional): If True, attempts to parse the response
                                                 content specifically as a JSON list of dicts
                                                 (often used for structured output like timestamps).
                                                 If False, returns the raw text content.
                                                 Defaults to False.
    Returns:
        Union[str, List[Dict[str, str]], Dict[str, Any], None]: The LLM's response.
            - If is_json_response_expected is True: A list of dictionaries (e.g., for clips) or original dict.
            - If is_json_response_expected is False: The raw text content from the LLM.
            - None if an error occurs.
    """
    if not api_key:
        logger.error("API key not provided for call_dashscope_qwen.")
        return None

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model_name,
        "input": {
            "messages": [
                {"role": "system", "content": system_prompt.strip()},
                {"role": "user", "content": user_prompt.strip()}
            ]
        },
        "parameters": {
            # Add temperature or other params if needed, e.g.
            # "temperature": 0.7,
            # "result_format": "text" or "message" - Dashscope might vary
        }
    }
    # Dashscope API endpoint for Qwen models (confirm if this is the correct one for general chat)
    # The one from highlight_selector was "https://dashscope-intl.aliyuncs.com/compatible-mode/v1"
    # Let's stick to that for now as it was working.
    endpoint_url = "https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions"
    # The original highlight_selector.py used: "https://dashscope-intl.aliyuncs.com/compatible-mode/v1"
    # This endpoint "compatible-mode/v1" might expect an OpenAI-like payload.
    # Let's adjust the payload to be more OpenAI-like for this endpoint.
    
    openai_compatible_payload = {
        "model": model_name,
        "messages": [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": user_prompt.strip()}
        ]
        # Add temperature etc. here if needed
    }

    try:
        logger.debug(f"Calling Dashscope Qwen API. Model: {model_name}, Endpoint: {endpoint_url}")
        # Using the original endpoint from highlight_selector.py for now
        response = requests.post(
            endpoint_url, 
            headers=headers,
            json=openai_compatible_payload, # Use json parameter for requests to auto-serialize
            timeout=60
        )
        response.raise_for_status()
        
        # The structure from highlight_selector was: response.json()["choices"][0]["message"]["content"]
        raw_content = response.json()["choices"][0]["message"]["content"]

        if is_json_response_expected:
            # Attempt to extract JSON array like [{}, {}]
            match = re.search(r'\[(.*?)\]', raw_content, re.DOTALL) # Search for content within [...]
            if match:
                try:
                    # The match includes the brackets, which is what json.loads expects for an array.
                    return json.loads(match.group(0)) 
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON from Dashscope Qwen matched content: {match.group(0)}. Error: {e}")
                    return None # Or perhaps return raw_content if fallback is desired
            else:
                # Fallback or error if expected JSON array format is not found
                logger.warning(f"Could not extract JSON array from Dashscope Qwen response for structured output: {raw_content}")
                # Check if it's a single JSON object
                try:
                    return json.loads(raw_content) # Try to parse the whole content as JSON
                except json.JSONDecodeError:
                    logger.warning(f"Dashscope Qwen response was not a JSON array nor a single JSON object: {raw_content}")
                    return None # Or return raw_content
        else:
            # If JSON is not specifically expected, return the raw text content
            return raw_content.strip()

    except requests.exceptions.RequestException as e:
        logger.error(f"Dashscope Qwen API request error: {e}")
        return None
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        # This can happen if response.json() fails or structure is unexpected
        logger.error(f"Error processing Dashscope Qwen API response: {e} - Response text: {response.text if 'response' in locals() else 'N/A'}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in call_dashscope_qwen: {e}")
        return None


def call_groq(api_key: str, model: str, system_prompt: str, user_prompt: str) -> Union[List[Any], Dict[str, Any], str, None]:
    """Calls the Groq LLM API with the given parameters and prompts.

    Args:
        api_key (str): The API key for Groq.
        model (str): The model to use.
        system_prompt (str): The system prompt for the LLM.
        user_prompt (str): The user prompt for the LLM.

    Returns:
        Union[List[Any], Dict[str, Any], str, None]: The JSON response (parsed if possible) or text content from the LLM,
                                         or None if an error occurs.
    """
    if not api_key:
        logger.error("API key not provided for call_groq.")
        return None
        
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "temperature": 0.4, # Adjusted for potentially creative tasks like titles
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    }
    try:
        logger.debug(f"Calling Groq API. Model: {model}")
        res = requests.post(
            "https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers=headers,
            data=json.dumps(payload),
            timeout=60
        )
        res.raise_for_status()
        # Assuming Groq also returns content in a similar structure
        # or direct JSON/text in choices[0].message.content
        content = res.json()["choices"][0]["message"]["content"]

        # Attempt to parse as JSON if content is a JSON string
        try:
            # This would parse if content is like '[{\"key\": \"value\"}]' or '{\"key\": \"value\"}'
            return json.loads(content) 
        except json.JSONDecodeError:
            # If not a plain JSON string, check for ```json ... ``` block
            match = re.search(r"```json\n(.*?)\n```", content, re.DOTALL)
            if match:
                try:
                    return json.loads(match.group(1))
                except json.JSONDecodeError as e_match:
                    logger.warning(f"Failed to parse JSON from Groq matched block: {e_match}. Raw content: {content}")
                    return content.strip() # Return stripped text if match parsing fails
            return content.strip() # Return stripped text content if no JSON structure found

    except requests.exceptions.RequestException as e:
        logger.error(f"Groq API request error: {e}")
        return None
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        # This can happen if res.json() fails or structure is unexpected
        logger.error(f"Error processing Groq API response: {e} - Response text: {res.text if 'res' in locals() else 'N/A'}")
        return None
    except Exception as e:
        logger.error(f"Groq API general error: {e}")
        return None

def extract_srt_segment(srt_text: str, start_time: float, end_time: float) -> str:
    """Extracts subtitle text lines from SRT content between two timestamps.

    Args:
        srt_text (str): The SRT content as a string.
        start_time (float): The start time in seconds.
        end_time (float): The end time in seconds.

    Returns:
        str: A single string concatenating all extracted subtitle lines.
    """
    entries = srt_text.strip().split("\n\n")
    extracted = []

    for entry in entries:
        lines = entry.strip().split("\n")
        if len(lines) < 3:
            continue
        timestamp = lines[1]
        text = " ".join(lines[2:])
        try:
            start, end = timestamp.split(" --> ")
            start_sec = parse_srt_timecode(start)
            end_sec = parse_srt_timecode(end)
            if start_sec >= start_time and end_sec <= end_time:
                extracted.append(text)
        except ValueError as e:
            logger.warning(f"Could not parse timestamp line in SRT: {timestamp}. Error: {e}")
            continue
        except Exception as e:
            logger.warning(f"Error processing SRT entry: {entry}. Error: {e}")
            continue

    return " ".join(extracted)
