import subprocess
import os
from typing import <PERSON><PERSON>, Optional
from utils import logger

# TODO: Centralize DOWNLOAD_DIR configuration (e.g., from a config module or passed as arg)
DOWNLOAD_BASE_DIR = "./downloads/youtube"

def download_youtube(url: str, job_id: str) -> Tuple[str, Optional[str]]:
    """Downloads a YouTube video and its English VTT subtitles using yt-dlp.

    Args:
        url (str): The URL of the YouTube video.
        job_id (str): A unique identifier for the job, used in output filenames.

    Returns:
        Tuple[str, Optional[str]]: A tuple containing:
            - The path to the downloaded video file.
            - The path to the downloaded VTT subtitle file (if available, else None).
        Returns (None, None) if download fails fundamentally.
    """
    os.makedirs(DOWNLOAD_BASE_DIR, exist_ok=True)

    # Define base filename template for yt-dlp
    # yt-dlp will replace %(ext)s with the actual extension (e.g., mp4, vtt)
    output_template = os.path.join(DOWNLOAD_BASE_DIR, f"{job_id}.%(ext)s")
    
    # Predict final paths based on common extensions
    video_path = os.path.join(DOWNLOAD_BASE_DIR, f"{job_id}.mp4") # Assuming mp4 is the video format
    vtt_path = os.path.join(DOWNLOAD_BASE_DIR, f"{job_id}.en.vtt")

    logger.info(f"Starting download for YouTube URL: '{url}', job_id: '{job_id}'. Output template: '{output_template}'")

    # Ensure paths used by yt-dlp are robust if job_id or DOWNLOAD_BASE_DIR could have spaces/special chars
    # For simplicity, assuming they are safe here. Consider more robust quoting if needed.
    cmd = [
        "yt-dlp", 
        "--write-sub", "--write-auto-sub", 
        "--sub-lang", "en", "--sub-format", "vtt",
        "-f", "bestvideo[ext=mp4]+bestaudio[ext=m4a]/mp4", # Request mp4 container
        "-o", output_template, 
        url
    ]

    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"Successfully downloaded content for job_id '{job_id}' from URL '{url}'.")
        
        # Verify actual video path; yt-dlp might pick a slightly different extension like .mkv then remux to mp4
        # For now, we assume it successfully creates the .mp4 as requested by the format selector.
        if not os.path.exists(video_path):
            logger.warning(f"Expected video file '{video_path}' not found after yt-dlp. Check actual output.")
            # Attempt to find any video file if the exact one isn't there (simple check)
            possible_videos = [f for f in os.listdir(DOWNLOAD_BASE_DIR) if f.startswith(job_id) and not f.endswith(".vtt")]
            if possible_videos:
                video_path = os.path.join(DOWNLOAD_BASE_DIR, possible_videos[0])
                logger.info(f"Found video file: {video_path}")
            else:
                 logger.error(f"No video file found for job '{job_id}' in '{DOWNLOAD_BASE_DIR}'.")
                 return None, None # Critical failure

        return video_path, vtt_path if os.path.exists(vtt_path) else None
        
    except subprocess.CalledProcessError as e:
        logger.error(f"[yt-dlp] Error downloading from URL '{url}' for job_id '{job_id}'.")
        logger.error(f"[yt-dlp] Command: {' '.join(cmd)}")
        logger.error(f"[yt-dlp] STDOUT: {e.stdout}")
        logger.error(f"[yt-dlp] STDERR: {e.stderr}")
        return None, None # Indicate failure
    except Exception as e:
        logger.error(f"Unexpected error during YouTube download for '{url}': {e}")
        return None, None # Indicate failure