import ffmpeg
import sys
import os

print(f"Python executable: {sys.executable}")
print(f"Imported ffmpeg module from: {ffmpeg.__file__}")
print(f"ffmpeg module version (if available): {getattr(ffmpeg, '__version__', 'N/A')}")

print("\nAttributes of the imported ffmpeg module:")
print(dir(ffmpeg))

# Test if probe exists and is callable
if hasattr(ffmpeg, 'probe') and callable(ffmpeg.probe):
    print("\n'ffmpeg.probe' exists and is callable.")
    # You could try a minimal probe here if you have a dummy/small media file
    # For now, just checking existence is enough.
    # try:
    #     # Create a dummy file to probe (or use an existing small media file)
    #     # This part is more involved; for now, we focus on the attribute.
    #     # ffmpeg.probe('dummy.mp4') # This would require a file
    #     print("Minimal probe attempt would go here.")
    # except Exception as e:
    #     print(f"Error during minimal probe attempt: {e}")
else:
    print("\n'ffmpeg.probe' DOES NOT exist or is not callable.")

print("\nAttempting to list system PATH:")
system_path = os.environ.get('PATH', 'Not found')
print(system_path)
if 'ffmpeg' in system_path.lower():
    print("\n'ffmpeg' appears to be in the system PATH (case-insensitive check).")
else:
    print("\n'ffmpeg' does NOT appear to be in the system PATH (case-insensitive check).")
    print("Please ensure the FFmpeg executable is installed and its directory is in your system's PATH.") 