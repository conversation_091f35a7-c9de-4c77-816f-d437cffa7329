import os
import yt_dlp
import ffmpeg
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass
import subprocess
import shutil

@dataclass
class VideoInfo:
    """Video metadata container"""
    title: str
    duration: float
    width: int
    height: int
    fps: float
    format: str
    file_path: str

class VideoProcessor:
    """Handles video downloading and preprocessing"""

    def __init__(self, config: Dict, app_temp_files_list: Optional[List[str]] = None):
        self.config = config
        self.temp_dir = Path("data/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.logger = self.config.get('logger', print)
        self.app_temp_files_list = app_temp_files_list if app_temp_files_list is not None else []

    def download_video(self, url: str, output_dir: str = None) -> VideoInfo:
        """
        Download high-quality video from URL using yt-dlp.
        Downloads source material in high quality for later processing.
        """
        if output_dir is None:
            output_dir = str(self.temp_dir)

        # Download high-quality source video and subtitles
        # We'll keep the source video in high quality and only apply vertical formatting during clip generation
        ydl_opts = {
            'format': 'bestvideo[height>=720]+bestaudio/best[height>=720]/best',  # Get high quality source
            'outtmpl': f'{output_dir}/%(title)s.%(ext)s',
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['en'],
            'subtitlesformat': 'srt',
            'merge_output_format': 'mp4',
        }

        self.logger.info(f"Downloading high-quality video using yt-dlp from: {url}")
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                downloaded_video_path_str = ydl.prepare_filename(info)

            # Add the downloaded file to temp files for cleanup
            if downloaded_video_path_str:
                self.app_temp_files_list.append(downloaded_video_path_str)
                self.logger.info(f"Added to temp files for cleanup: {downloaded_video_path_str}")

            if not downloaded_video_path_str or not Path(downloaded_video_path_str).exists():
                self.logger.error(f"Downloaded video file not found: {downloaded_video_path_str}")
                raise RuntimeError(f"Video download failed or file not found: {downloaded_video_path_str}")

            self.logger.info(f"Video downloaded successfully: {downloaded_video_path_str}")

            # Extract video info from the downloaded file
            return self._extract_video_info(downloaded_video_path_str, info)

        except Exception as e:
            self.logger.error(f"Error downloading video: {e}", exc_info=True)
            raise RuntimeError(f"Video download failed: {e}")

    def _extract_video_info(self, file_path: str, yt_info: Dict = None) -> VideoInfo:
        """Extract video metadata using FFmpeg probe"""
        try:
            self.logger.debug(f"Probing video file for info: {file_path}")
            probe = ffmpeg.probe(file_path)
            video_stream = next(
                (stream for stream in probe['streams'] if stream['codec_type'] == 'video'),
                None # Default if no video stream found
            )

            if not video_stream:
                self.logger.error(f"No video stream found in {file_path} after FFmpeg processing.")
                # Fallback or raise error - for now, try to construct VideoInfo with placeholders
                return VideoInfo(
                    title=yt_info.get('title', Path(file_path).stem) if yt_info else Path(file_path).stem,
                    duration=float(probe.get('format', {}).get('duration', '0')),
                    width=0, # Placeholder
                    height=0, # Placeholder
                    fps=0, # Placeholder
                    format=probe.get('format', {}).get('format_name', 'unknown'),
                    file_path=file_path
                )

            # Ensure necessary keys exist, provide defaults if not
            width = video_stream.get('width', 0)
            height = video_stream.get('height', 0)
            r_frame_rate = video_stream.get('r_frame_rate', '0/1')
            try:
                fps = eval(r_frame_rate)
            except ZeroDivisionError:
                fps = 0 # Fallback if r_frame_rate is "0/0" or similar invalid
            except Exception:
                fps = 0 # General fallback

            return VideoInfo(
                title=yt_info.get('title', Path(file_path).stem) if yt_info else Path(file_path).stem,
                duration=float(probe.get('format', {}).get('duration', '0')),
                width=int(width),
                height=int(height),
                fps=fps,
                format=probe.get('format', {}).get('format_name', 'unknown'),
                file_path=file_path
            )
        except Exception as e:
            self.logger.error(f"Error extracting video info from {file_path}: {e}", exc_info=True)
            # Provide a fallback VideoInfo object in case of catastrophic probe failure
            return VideoInfo(
                title=yt_info.get('title', "Unknown Title") if yt_info else Path(file_path).stem,
                duration=0.0, width=0, height=0, fps=0, format="unknown", file_path=file_path
            )

    def normalize_video(self, video_info: VideoInfo) -> str:
        """
        Further normalizes the video:
        - Ensures consistent audio levels.
        - Converts to a standard resolution (e.g., upscales) and aspect ratio.
        - Standardizes frame rate (optional, if specified in config).
        """
        self.logger.info(f"Starting normalization for video: {video_info.file_path}")

        # --- 1. Audio Normalization ---
        audio_normalized_input_path = video_info.file_path
        audio_normalized_output_filename = f"{Path(video_info.file_path).stem}_audio_norm.mp4"
        audio_normalized_output_path = str(self.temp_dir / audio_normalized_output_filename)

        self.logger.info(f"Applying audio normalization (loudnorm) to {audio_normalized_input_path}")
        try:
            audio_norm_cmd = [
                'ffmpeg',
                '-i', audio_normalized_input_path,
                '-af', 'loudnorm',
                '-c:v', 'copy',       # Copy video stream as is for this step
                '-c:a', 'aac',        # Re-encode audio to AAC
                '-y',                 # Overwrite output file
                audio_normalized_output_path
            ]
            self.logger.info(f"Executing FFmpeg command for audio normalization: {' '.join(audio_norm_cmd)}")
            process = subprocess.Popen(audio_norm_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            _, stderr = process.communicate()

            if process.returncode != 0:
                self.logger.error(f"Error during audio normalization: {stderr.decode()}")
                raise RuntimeError(f"FFmpeg audio normalization failed: {stderr.decode()}")
            else:
                self.logger.info(f"Audio normalization successful. Output: {audio_normalized_output_path}")
                # The input for the next step is the audio-normalized file
                current_input_path = audio_normalized_output_path
        except Exception as e:
            self.logger.error(f"Unexpected error during audio normalization: {str(e)}", exc_info=True)
            raise

        # --- 2. Video Upscaling and FPS Standardization (existing logic) ---
        # The input for this stage is the `current_input_path` (audio-normalized video)

        # Determine final output path for the fully normalized video
        final_output_filename = f"{Path(video_info.file_path).stem}_normalized.mp4" # Original naming for the final output
        final_output_path = str(self.temp_dir / final_output_filename)

        self.logger.info(f"Normalizing (upscaling/FPS) video: {current_input_path} to {final_output_path}")

        # Get target resolution and FPS from config (similar to previous logic)
        platform_config = self.config.get('platforms', {}).get('youtube_shorts', {}) # Assuming youtube_shorts for now
        target_w_norm = int(platform_config.get('resolution', '1080x1920').split('x')[0])
        target_h_norm = int(platform_config.get('resolution', '1080x1920').split('x')[1])
        target_fps_norm = int(platform_config.get('fps', self.config.get('video',{}).get('fps', 30)))

        self.logger.info(f"  Target video normalization specs: {target_w_norm}x{target_h_norm} @{target_fps_norm}fps")

        # Probe the audio-normalized video to check if upscaling is needed
        # (assuming its dimensions are the same as video_info unless audio norm changed them, which it shouldn't with -c:v copy)
        # It's safer to probe `current_input_path` if there's any doubt.
        # For simplicity, we'll use video_info for initial w/h/fps check, assuming -c:v copy preserved them.

        if video_info.width == target_w_norm and \
           video_info.height == target_h_norm and \
           video_info.fps == target_fps_norm and \
           current_input_path == video_info.file_path: # Check if audio norm actually created a new file
            self.logger.info("Video already meets target specs & no separate audio norm file. Skipping further normalization.")
            # If audio norm was skipped or in-place (not the case here), and specs match.
            # If audio norm created a new file, we still need to rename/move it to final_output_path
            if current_input_path != final_output_path:
                 shutil.move(current_input_path, final_output_path)
            return final_output_path


        try:
            stream = ffmpeg.input(current_input_path) # Input is the audio-normalized video

            processed_video = stream.video.filter('scale', target_w_norm, target_h_norm).filter('fps', fps=target_fps_norm)

            # Audio stream from current_input_path is already normalized, so just pass it through
            processed_audio = stream.audio

            (
                ffmpeg.output(
                    processed_video,
                    processed_audio,
                    final_output_path, # Final output path
                    vcodec=self.config.get('video', {}).get('codec', 'libx264'),
                    acodec='aac', # Keep AAC audio
                    audio_bitrate=self.config.get('audio', {}).get('bitrate', '192k'),
                    **{'crf': self.config.get('video', {}).get('crf', 23),
                       'preset': self.config.get('video', {}).get('preset', 'medium')}
                )
                .overwrite_output()
                .run(quiet=False, capture_stdout=True, capture_stderr=True)
            )
            self.logger.info(f"Video normalization (upscaling/FPS) complete. Output: {final_output_path}")

            # Mark intermediate audio-normalized file for cleanup
            if current_input_path != video_info.file_path and current_input_path != final_output_path:
                if self.app_temp_files_list is not None:
                    self.logger.debug(f"Marking intermediate audio-normalized file for cleanup: {current_input_path}")
                    self.app_temp_files_list.append(current_input_path)
                # try:
                #     os.remove(current_input_path)
                #     self.logger.info(f"Removed intermediate audio-normalized file: {current_input_path}")
                # except OSError as e:
                #     self.logger.warning(f"Could not remove intermediate file {current_input_path}: {e}")

            return final_output_path

        except ffmpeg.Error as e:
            stderr_output = e.stderr.decode('utf8') if e.stderr else "No stderr output"
            self.logger.error(f"FFmpeg Error during video normalization (upscaling/FPS): {stderr_output}")
            raise RuntimeError(f"Error normalizing/upscaling video: {stderr_output}")
        except Exception as e:
            self.logger.error(f"Unexpected error during video upscaling/FPS normalization: {str(e)}", exc_info=True)
            raise

    def extract_frames(self, video_path: str, timestamps: List[float]) -> List[str]:
        """
        Extract frames at specific timestamps

        Args:
            video_path: Path to video file
            timestamps: List of timestamp in seconds

        Returns:
            List of frame file paths
        """
        frame_paths = []

        for i, timestamp in enumerate(timestamps):
            frame_path = str(self.temp_dir / f"frame_{i}_{timestamp:.2f}.jpg")

            try:
                (
                    ffmpeg
                    .input(video_path, ss=timestamp)
                    .filter('scale', 640, 360)
                    .output(frame_path, vframes=1)
                    .overwrite_output()
                    .run(quiet=True)
                )
                frame_paths.append(frame_path)
            except ffmpeg.Error:
                continue

        return frame_paths