import ffmpeg
from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip
import pysrt
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

# Import ClipHighlight from ai_analyzer
from src.core.ai_analyzer import <PERSON><PERSON><PERSON><PERSON>light

@dataclass
class ClipSpec:
    """Clip generation specification"""
    start_time: float
    end_time: float
    platform: str
    title: str
    subtitle_path: Optional[str] = None
    background_music: Optional[str] = None

class ClipGenerator:
    """Generates optimized clips for different platforms"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.output_dir = Path("data/exports")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = config.get('logger', print) # Added logger
        
        # Platform configurations
        self.platforms = config['platforms']
        # Subtitle styling configurations
        self.subtitle_styles = config.get('subtitle_styles', {})
        self.default_subtitle_style_name = config.get('default_subtitle_style', 'default')
        self.watermark_config = config.get('watermark', {})
        self.title_overlay_config = config.get('title_overlay', {})
    
    def generate_clip(self, video_path: str, clip_spec: ClipSpec) -> str:
        """
        Generate a single clip with platform optimization
        
        Args:
            video_path: Source video path
            clip_spec: Clip specification
            
        Returns:
            Path to generated clip
        """
        platform_config = self.platforms[clip_spec.platform]
        
        # Create output filename
        safe_title = "".join(c for c in clip_spec.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        output_path = self.output_dir / f"{safe_title}_{clip_spec.platform}.mp4"
        
        try:
            # Load video
            with VideoFileClip(video_path) as video:
                # Extract clip
                clip = video.subclip(clip_spec.start_time, clip_spec.end_time)
                
                # Apply platform optimization
                clip = self._optimize_for_platform(clip, platform_config)
                
                # Add subtitles if available
                if clip_spec.subtitle_path:
                    clip = self._add_subtitles(clip, clip_spec.subtitle_path, 
                                             clip_spec.start_time, clip_spec.end_time,
                                             platform_config.get('subtitle_style', self.default_subtitle_style_name))
                
                # Add title overlay if enabled (using clip_spec.title)
                if self.title_overlay_config.get('enabled', False):
                    clip = self._add_title_overlay(clip, clip_spec.title, self.title_overlay_config)

                # Add watermark if enabled
                if self.watermark_config.get('enabled', False) and self.watermark_config.get('image_path'):
                    clip = self.add_watermark(clip, self.watermark_config)
                
                # Add background music if specified
                if clip_spec.background_music:
                    clip = self._add_background_music(clip, clip_spec.background_music)
                
                # Write final video
                clip.write_videofile(
                    str(output_path),
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    fps=platform_config.get('fps', 30)
                )
                
                clip.close()
            
            return str(output_path)
            
        except Exception as e:
            raise RuntimeError(f"Error generating clip: {e}")
    
    def _optimize_for_platform(self, clip, platform_config: Dict):
        """Apply platform-specific optimizations"""
        # Resize for platform
        resolution = platform_config['resolution']
        width, height = map(int, resolution.split('x'))
        
        # Calculate crop/scale parameters
        clip_aspect = clip.w / clip.h
        target_aspect = width / height
        
        if clip_aspect > target_aspect:
            # Video is wider - crop sides
            new_width = int(clip.h * target_aspect)
            x_center = clip.w // 2
            clip = clip.crop(
                x_center=x_center,
                width=new_width
            )
        elif clip_aspect < target_aspect:
            # Video is taller - crop top/bottom
            new_height = int(clip.w / target_aspect)
            y_center = clip.h // 2
            clip = clip.crop(
                y_center=y_center,
                height=new_height
            )
        
        # Resize to target resolution
        clip = clip.resize((width, height))
        
        # Ensure duration doesn't exceed platform limit
        max_duration = platform_config['max_duration']
        if clip.duration > max_duration:
            clip = clip.subclip(0, max_duration)
        
        return clip
    
    def _add_subtitles(self, clip, subtitle_path: str, start_time: float, 
                      end_time: float, style: str):
        """Add burned-in subtitles to clip"""
        try:
            # Load subtitle file
            subs = pysrt.open(subtitle_path)
            
            # Filter subtitles for clip duration
            clip_subs = []
            for sub in subs:
                sub_start = sub.start.ordinal / 1000.0
                sub_end = sub.end.ordinal / 1000.0
                
                # Check if subtitle overlaps with clip
                if sub_end > start_time and sub_start < end_time:
                    # Adjust timing relative to clip
                    relative_start = max(0, sub_start - start_time)
                    relative_end = min(clip.duration, sub_end - start_time)
                    
                    if relative_end > relative_start:
                        clip_subs.append({
                            'text': sub.text,
                            'start': relative_start,
                            'end': relative_end
                        })
            
            # Create subtitle clips
            subtitle_clips = []
            for sub in clip_subs:
                txt_clip = self._create_subtitle_clip(
                    sub['text'], 
                    sub['start'], 
                    sub['end'], 
                    clip.size,
                    style
                )
                subtitle_clips.append(txt_clip)
            
            # Composite with video
            if subtitle_clips:
                final_clip = CompositeVideoClip([clip] + subtitle_clips)
                return final_clip
            
        except Exception as e:
            print(f"Warning: Could not add subtitles: {e}")
        
        return clip
    
    def _create_subtitle_clip(self, text: str, start: float, end: float, 
                             video_size: Tuple[int, int], style_name_from_platform: str):
        """Create a subtitle text clip using configured styles."""
        width, height = video_size

        # Determine the style to use: platform-specific or default
        active_style_name = style_name_from_platform if style_name_from_platform in self.subtitle_styles else self.default_subtitle_style_name
        style_config = self.subtitle_styles.get(active_style_name, {})
        
        # Get style properties from config, with defaults
        font = style_config.get('font', 'Arial-Bold')
        # Fontsize can be absolute or relative to video width/height
        fontsize_abs = style_config.get('fontsize') 
        fontsize_ratio_w = style_config.get('fontsize_ratio_width') # e.g., 0.04 for 4% of width
        fontsize_ratio_h = style_config.get('fontsize_ratio_height') # e.g., 0.08 for 8% of height

        if fontsize_abs:
            fontsize = int(fontsize_abs)
        elif fontsize_ratio_w:
            fontsize = int(width * fontsize_ratio_w)
        elif fontsize_ratio_h:
            fontsize = int(height * fontsize_ratio_h)
        else:
            fontsize = int(width // 30) # Fallback default if nothing specified
        
        color = style_config.get('color', 'white')
        bg_color = style_config.get('bg_color', None) # For background box
        stroke_color = style_config.get('stroke_color', 'black')
        stroke_width = style_config.get('stroke_width', 1.5)
        
        # Position: can be a preset string or explicit coordinates/ratios
        position_preset = style_config.get('position_preset')
        pos_x_abs = style_config.get('pos_x_abs')
        pos_y_abs = style_config.get('pos_y_abs')
        pos_x_ratio = style_config.get('pos_x_ratio') # 0.0 to 1.0 for left to right
        pos_y_ratio = style_config.get('pos_y_ratio') # 0.0 to 1.0 for top to bottom

        position: Any # Can be tuple or string
        if position_preset == 'bottom_center':
            position = ('center', height * style_config.get('vertical_offset_ratio', 0.85))
        elif position_preset == 'center':
            position = ('center', 'center')
        elif position_preset == 'top_center':
            position = ('center', height * style_config.get('vertical_offset_ratio', 0.1))
        elif isinstance(pos_x_abs, (int, float)) and isinstance(pos_y_abs, (int, float)):
            position = (pos_x_abs, pos_y_abs)
        elif isinstance(pos_x_ratio, float) and isinstance(pos_y_ratio, float):
            position = (width * pos_x_ratio, height * pos_y_ratio)
        else: # Fallback default position
            position = ('center', height * 0.85)

        self.logger.debug(f"Subtitle style '{active_style_name}': font={font}, size={fontsize}, color={color}, stroke={stroke_color}@{stroke_width}, pos={position}")

        # Create text clip
        txt_clip = TextClip(
            text,
            fontsize=fontsize,
            color=color,
            font=font,
            stroke_color=stroke_color,
            stroke_width=stroke_width,
            bg_color=bg_color # Added bg_color
            # method='caption' or 'label' might be needed for complex layouts or bg_color handling
            # For now, assuming TextClip handles bg_color appropriately or it's transparent if None
        ).set_position(position).set_duration(end - start).set_start(start)
        
        return txt_clip
    
    def _add_title_overlay(self, clip: VideoFileClip, title_text: str, config: Dict) -> VideoFileClip:
        self.logger.info(f"Adding title overlay: '{title_text}'")
        try:
            font = config.get('font', 'Arial-Bold')
            fontsize_ratio_w = config.get('fontsize_ratio_width', 0.05)
            fontsize = int(clip.w * fontsize_ratio_w)
            color = config.get('color', 'white')
            stroke_color = config.get('stroke_color', 'black')
            stroke_width = config.get('stroke_width', 2)
            position_preset = config.get('position_preset', 'top_center') # e.g., top_center, center
            duration = config.get('duration_seconds', min(5, clip.duration)) # Show for 5s or clip duration
            vertical_offset_ratio = config.get('vertical_offset_ratio', 0.1) # 10% from top for top_center

            position: Any
            if position_preset == 'top_center':
                position = ('center', clip.h * vertical_offset_ratio)
            elif position_preset == 'center':
                position = ('center', 'center')
            elif position_preset == 'bottom_center':
                position = ('center', clip.h * (1-vertical_offset_ratio))
            else: # default to top_center
                position = ('center', clip.h * 0.1)

            title_clip = TextClip(
                title_text,
                fontsize=fontsize,
                color=color,
                font=font,
                stroke_color=stroke_color,
                stroke_width=stroke_width
            ).set_position(position).set_duration(duration).set_start(0) # Appears at the start

            return CompositeVideoClip([clip, title_clip])
        except Exception as e:
            self.logger.error(f"Error adding title overlay: {e}", exc_info=True)
            return clip # Return original clip on error

    def add_watermark(self, clip: VideoFileClip, config: Dict) -> VideoFileClip:
        self.logger.info(f"Adding watermark from: {config.get('image_path')}")
        from moviepy.editor import ImageClip # Import here to keep it optional if not used

        try:
            watermark_path = config.get('image_path')
            if not watermark_path or not Path(watermark_path).exists():
                self.logger.warning(f"Watermark image not found at {watermark_path}. Skipping watermark.")
                return clip

            # Size: can be relative to clip width or height, or absolute
            size_ratio_w = config.get('size_ratio_width') # e.g., 0.15 for 15% of clip width
            size_ratio_h = config.get('size_ratio_height')
            abs_width = config.get('width')
            abs_height = config.get('height')

            watermark = ImageClip(watermark_path)
            target_w, target_h = None, None

            if abs_width:
                target_w = int(abs_width)
            elif size_ratio_w:
                target_w = int(clip.w * size_ratio_w)
            
            if abs_height:
                target_h = int(abs_height)
            elif size_ratio_h:
                target_h = int(clip.h * size_ratio_h)

            if target_w and not target_h: # scale height proportionally
                watermark = watermark.resize(width=target_w)
            elif target_h and not target_w: # scale width proportionally
                watermark = watermark.resize(height=target_h)
            elif target_w and target_h: # explicit size
                watermark = watermark.resize((target_w, target_h))
            else: # Default: 15% of clip width, height auto
                watermark = watermark.resize(width=int(clip.w * 0.15))

            opacity = float(config.get('opacity', 0.7))
            watermark = watermark.set_opacity(opacity)

            # Position: preset strings or explicit coordinates/ratios
            # Example presets: top_right, top_left, bottom_right, bottom_left, center
            # Ratios are relative to clip dimensions. Margin can also be configured.
            position_preset = config.get('position_preset', 'bottom_right')
            margin_ratio = config.get('margin_ratio', 0.02) # 2% margin
            margin_x = int(clip.w * margin_ratio)
            margin_y = int(clip.h * margin_ratio)

            pos_x, pos_y = 0,0
            if position_preset == 'top_right':
                pos_x = clip.w - watermark.w - margin_x
                pos_y = margin_y
            elif position_preset == 'top_left':
                pos_x = margin_x
                pos_y = margin_y
            elif position_preset == 'bottom_left':
                pos_x = margin_x
                pos_y = clip.h - watermark.h - margin_y
            elif position_preset == 'bottom_right':
                pos_x = clip.w - watermark.w - margin_x
                pos_y = clip.h - watermark.h - margin_y
            elif position_preset == 'center':
                pos_x = (clip.w - watermark.w) / 2
                pos_y = (clip.h - watermark.h) / 2
            else: # Default to bottom_right
                pos_x = clip.w - watermark.w - margin_x
                pos_y = clip.h - watermark.h - margin_y
            
            watermark = watermark.set_position((pos_x, pos_y)).set_duration(clip.duration)

            return CompositeVideoClip([clip, watermark])
        except Exception as e:
            self.logger.error(f"Error adding watermark: {e}", exc_info=True)
            return clip # Return original clip on error
    
    def _add_background_music(self, clip, music_path: str):
        """Add background music to clip"""
        try:
            from moviepy.editor import AudioFileClip
            
            # Load background music
            music = AudioFileClip(music_path)
            
            # Adjust music duration to match video
            if music.duration > clip.duration:
                music = music.subclip(0, clip.duration)
            else:
                # Loop music if needed
                loops_needed = int(clip.duration / music.duration) + 1
                music = music.loop(n=loops_needed).subclip(0, clip.duration)
            
            # Mix audio
            if clip.audio is not None:
                # Reduce background music volume
                music_volume = self.config.get('audio', {}).get('music_volume', 0.5) # Default to 0.5 if not in config
                music = music.volumex(music_volume)
                
                # Composite audio
                final_audio = clip.audio.volumex(1.0).set_start(0)
                music = music.set_start(0)
                
                from moviepy.audio.AudioClip import CompositeAudioClip
                composite_audio = CompositeAudioClip([final_audio, music])
                clip = clip.set_audio(composite_audio)
            else:
                clip = clip.set_audio(music)
            
        except Exception as e:
            # print(f"Warning: Could not add background music: {e}")
            self.logger.warning(f"Could not add background music: {e}", exc_info=True)
        
        return clip
    
    def generate_thumbnail(
        self, 
        clip_path: str, 
        timestamp_seconds: Optional[float] = None, 
        output_dir: Optional[Path] = None,
        filename_suffix: str = "_thumbnail.jpg"
    ) -> Optional[str]:
        """Generates a thumbnail for the given clip path.

        Args:
            clip_path (str): Path to the video clip.
            timestamp_seconds (Optional[float]): Timestamp in seconds for the thumbnail. 
                                               Defaults to the middle of the clip.
            output_dir (Optional[Path]): Directory to save the thumbnail. Defaults to clip's directory.
            filename_suffix (str): Suffix for the thumbnail filename.

        Returns:
            Optional[str]: Path to the generated thumbnail, or None on failure.
        """
        self.logger.info(f"Generating thumbnail for clip: {clip_path}")
        try:
            video_clip = VideoFileClip(clip_path)
            if timestamp_seconds is None:
                timestamp_seconds = video_clip.duration / 2 # Default to middle frame
            elif timestamp_seconds > video_clip.duration:
                self.logger.warning(f"Requested thumbnail timestamp {timestamp_seconds}s is beyond clip duration {video_clip.duration}s. Using middle frame instead.")
                timestamp_seconds = video_clip.duration / 2

            if output_dir is None:
                output_dir = Path(clip_path).parent
            else:
                output_dir.mkdir(parents=True, exist_ok=True)
            
            base_filename = Path(clip_path).stem
            thumbnail_filename = f"{base_filename}{filename_suffix}"
            thumbnail_full_path = output_dir / thumbnail_filename

            video_clip.save_frame(str(thumbnail_full_path), t=timestamp_seconds)
            video_clip.close() # Release resources
            self.logger.info(f"Thumbnail saved to: {thumbnail_full_path}")
            return str(thumbnail_full_path)
        except Exception as e:
            self.logger.error(f"Error generating thumbnail for {clip_path}: {e}", exc_info=True)
            if 'video_clip' in locals(): # Ensure clip is closed if opened
                video_clip.close()
            return None
    
    def generate_batch(self, video_path: str, highlights: List[ClipHighlight], 
                      subtitle_path: str = None) -> List[Dict[str, Optional[str]]]:
        """
        Generate multiple clips for all platforms, now returns list of dicts with clip and thumbnail paths.
        
        Args:
            video_path: Source video path
            highlights: List of ClipHighlight objects from AIAnalyzer
            subtitle_path: Path to subtitle file
            
        Returns:
            List of dictionaries, each with 'clip_path' and 'thumbnail_path'.
        """
        generated_files_info = [] # Changed from generated_clips list
        
        default_platform_name = self.config.get('clip_generator_default_platform', 'youtube_shorts')

        if not highlights:
            self.logger.warning("No highlights provided to generate_batch. Returning empty list.")
            return []

        for i, highlight_obj in enumerate(highlights):
            target_platform_name = default_platform_name 

            if target_platform_name not in self.platforms:
                self.logger.warning(f"Platform '{target_platform_name}' not configured. Skipping clip for highlight: {highlight_obj.title}")
                continue

            clip_spec = ClipSpec(
                start_time=highlight_obj.start_time,
                end_time=highlight_obj.end_time,
                title=highlight_obj.title, 
                platform=target_platform_name,
                subtitle_path=subtitle_path,
                background_music=self.config.get('audio', {}).get('background_music_path') 
            )
            
            clip_output_path_str: Optional[str] = None
            thumbnail_output_path_str: Optional[str] = None
            try:
                self.logger.info(f"Generating clip {i+1}/{len(highlights)}: {clip_spec.title} for {clip_spec.platform}")
                clip_output_path_str = self.generate_clip(video_path, clip_spec)
                self.logger.info(f"  Successfully generated clip: {clip_output_path_str}")

                if clip_output_path_str:
                    # Generate thumbnail for the successfully created clip
                    # Thumbnails will be saved in the same directory as the clip by default.
                    thumbnail_output_path_str = self.generate_thumbnail(clip_output_path_str)
                    if thumbnail_output_path_str:
                        self.logger.info(f"    Successfully generated thumbnail: {thumbnail_output_path_str}")
                    else:
                        self.logger.warning(f"    Failed to generate thumbnail for clip: {clip_output_path_str}")
                
            except Exception as e:
                self.logger.error(f"Failed to generate clip or thumbnail for '{highlight_obj.title}': {e}", exc_info=True)
            
            generated_files_info.append({
                'clip_path': clip_output_path_str,
                'thumbnail_path': thumbnail_output_path_str,
                'highlight_info': highlight_obj # Keep original highlight info for metadata in main
            })
        
        return generated_files_info 