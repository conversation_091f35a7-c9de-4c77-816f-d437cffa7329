import ollama
from langchain.llms import Ollama
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
import yaml
from typing import List, Dict
from pathlib import Path

class TitleGenerator:
    """AI-powered title generation for viral content"""
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Initialize Ollama client
        self.llm = Ollama(
            model=config['llm']['model'],
            temperature=config['llm']['temperature']
        )
        
        # Load prompt templates
        self.prompts = self._load_prompts()
    
    def _load_prompts(self) -> Dict:
        """Load prompt templates from configuration"""
        prompt_file = Path("config/prompts.yaml")
        if prompt_file.exists():
            with open(prompt_file, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Default prompts
            return {
                'title_generation': '''
Generate 3 viral, engaging titles for a {platform} video clip about: {content_description}

The titles should:
- Be attention-grabbing and curiosity-inducing
- Use platform-appropriate language and style
- Include relevant keywords
- Be optimized for {platform} algorithm
- Each be under {max_length} characters

Keywords found in content: {keywords}

Format your response as:
1. [Title 1]
2. [Title 2] 
3. [Title 3]
                ''',
                'description_generation': '''
Create a compelling description for a {platform} video with title: "{title}"

Content summary: {content_summary}
Keywords: {keywords}

The description should:
- Hook viewers in the first line
- Include relevant hashtags
- Be platform-appropriate length
- Encourage engagement (likes, shares, comments)

Description:
                '''
            }
    
    def generate_titles(self, content_description: str, keywords: List[str], 
                       platform: str = "tiktok") -> List[str]:
        """
        Generate viral titles for content
        
        Args:
            content_description: Description of video content
            keywords: List of relevant keywords
            platform: Target platform (tiktok, youtube_shorts, instagram_reels)
            
        Returns:
            List of generated titles
        """
        # Platform-specific configurations
        platform_configs = {
            'tiktok': {'max_length': 100, 'style': 'casual, trendy'},
            'youtube_shorts': {'max_length': 100, 'style': 'clickbait, engaging'},
            'instagram_reels': {'max_length': 125, 'style': 'aesthetic, relatable'}
        }
        
        config = platform_configs.get(platform, platform_configs['tiktok'])
        
        # Create prompt
        prompt_template = PromptTemplate(
            input_variables=["platform", "content_description", "keywords", "max_length"],
            template=self.prompts['title_generation']
        )
        
        # Generate titles
        try:
            chain = LLMChain(llm=self.llm, prompt=prompt_template)
            result = chain.run(
                platform=platform,
                content_description=content_description,
                keywords=", ".join(keywords[:5]),  # Top 5 keywords
                max_length=config['max_length']
            )
            
            # Parse titles from response
            titles = self._parse_titles(result)
            return titles[:3]  # Return top 3 titles
            
        except Exception as e:
            print(f"Error generating titles: {e}")
            # Fallback titles
            return [
                f"Amazing {content_description[:30]}...",
                f"You Won\'t Believe This {content_description[:25]}!",
                f"This {content_description[:35]} is Incredible"
            ]
    
    def generate_description(self, title: str, content_summary: str, 
                           keywords: List[str], platform: str = "tiktok") -> str:
        """
        Generate platform-optimized description
        
        Args:
            title: Video title
            content_summary: Summary of video content
            keywords: List of relevant keywords
            platform: Target platform
            
        Returns:
            Generated description
        """
        prompt_template = PromptTemplate(
            input_variables=["platform", "title", "content_summary", "keywords"],
            template=self.prompts['description_generation']
        )
        
        try:
            chain = LLMChain(llm=self.llm, prompt=prompt_template)
            description = chain.run(
                platform=platform,
                title=title,
                content_summary=content_summary,
                keywords=", ".join(keywords[:8])
            )
            
            return description.strip()
            
        except Exception as e:
            print(f"Error generating description: {e}")
            # Fallback description
            return f"Check out this amazing content! {' '.join([f'#{kw}' for kw in keywords[:5]])}"
    
    def _parse_titles(self, response: str) -> List[str]:
        """Parse titles from LLM response"""
        titles = []
        lines = response.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line and (line.startswith(('1.', '2.', '3.')) or line.startswith('-')):
                # Remove numbering and clean up
                title = line
                for prefix in ['1.', '2.', '3.', '-', '*']:
                    if title.startswith(prefix):
                        title = title[len(prefix):].strip()
                        break
                
                # Remove brackets if present
                if title.startswith('[') and title.endswith(']'):
                    title = title[1:-1]
                
                if title:
                    titles.append(title)
        
        return titles 