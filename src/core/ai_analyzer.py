import requests # Example import, replace with actual LLM SDK if available
import json # For parsing LLM response if it's JSON
import pysrt # For parsing SRT if LLM returns SRT blocks or if needed for input
from typing import Dict, List, Tuple, Optional, Any # Added Any
from dataclasses import dataclass # Added dataclass
import os # Added os for API key
import re # Added re for parsing potentially

# Assuming backend.utils is in the project root or accessible via PYTHONPATH
from backend.utils import call_dashscope_qwen, parse_time # Added parse_time if needed for time conversion from LLM if not float

@dataclass
class ClipHighlight:
    """Represents a highlight segment identified by the AI Analyzer."""
    start_time: float  # in seconds
    end_time: float    # in seconds
    title: str
    summary: str
    reason: str

class AIAnalyzer:
    """Uses an LLM to analyze SRT subtitles and identify highlight segments for clips."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.llm_config = config.get('llm', {}) # Get LLM specific config if any
        self.logger = self.config.get('logger', print) # Use configured logger or print
        # Ensure QWEN_API_KEY is loaded, preferentially from llm_config, then os.environ
        self.qwen_api_key = self.llm_config.get('qwen_api_key', os.getenv('QWEN_API_KEY'))
        self.qwen_model = self.llm_config.get('qwen_model', 'qwen-turbo')

        # No local AI models like Whisper or sentiment analyzers are initialized here anymore.
        # NLTK downloads are also removed as TextBlob/direct transcription is not used.

    def _parse_srt_to_text(self, srt_file_path: str) -> str:
        """Helper to read an SRT file and extract its plain text content."""
        try:
            subs = pysrt.open(srt_file_path, encoding='utf-8')
            # To preserve some structure for the LLM, let's join with newlines between entries
            # And include timestamps in a readable format for the LLM to reference.
            # Example: "[00:00:01,000 --> 00:00:03,500] Text of subtitle"
            
            srt_entries_for_llm = []
            for sub in subs:
                start_str = sub.start.to_time().strftime('%H:%M:%S,%f')[:-3]
                end_str = sub.end.to_time().strftime('%H:%M:%S,%f')[:-3]
                srt_entries_for_llm.append(f"[{start_str} --> {end_str}] {sub.text_without_tags}")
            
            full_text = "\n".join(srt_entries_for_llm)
            # self.logger(f"Prepared SRT for LLM (first 500 chars): {full_text[:500]}")
            return full_text
        except Exception as e:
            self.logger(f"Error reading or parsing SRT file {srt_file_path}: {e}")
            raise

    def get_highlights_from_srt_llm(self, srt_file_path: str) -> List[ClipHighlight]:
        """
        Identifies highlight segments from an SRT file using an LLM (e.g., qwen-turbo).

        Args:
            srt_file_path: Path to the SRT subtitle file.

        Returns:
            A list of ClipHighlight objects, each containing start_time, end_time,
            title, summary, and reason for the clip.
        """
        self.logger(f"Starting highlight detection from SRT using LLM: {srt_file_path}")

        if not self.qwen_api_key:
            self.logger("QWEN_API_KEY not found in config or environment. Cannot get highlights.")
            return []

        try:
            # Pass the full SRT text to the LLM, not just concatenated text.
            with open(srt_file_path, 'r', encoding='utf-8') as f:
                srt_full_content = f.read()
            if not srt_full_content.strip():
                self.logger("SRT file is empty. No highlights to find.")
                return []
        except Exception as e:
            self.logger(f"Failed to read SRT content for LLM: {e}")
            return []

        system_prompt = """
You are an expert video content analyst. Your task is to identify the most engaging and viral-worthy segments from the provided SRT transcript.
For each segment, you must provide:
1.  `start_time`: The precise start time of the segment in TOTAL SECONDS (float).
2.  `end_time`: The precise end time of the segment in TOTAL SECONDS (float).
3.  `title`: A short, catchy, and viral title for this clip (string, max 15 words).
4.  `summary`: A brief summary of what happens in this clip (string, 1-2 sentences).
5.  `reason`: A concise explanation of why this segment is a good highlight (e.g., "strong emotional peak", "clear call to action", "funny moment").

Constraints:
- Each clip should be between 15 and 90 seconds long.
- Identify up to 5-7 highlight clips from the transcript.
- Ensure `end_time` is greater than `start_time`.
- Times must be based on the SRT timestamps. Parse SRT time like '00:01:15,320' into total seconds. For example, 00:01:15,320 is 75.32 seconds.

Respond ONLY with a valid JSON array of objects. Each object should follow this structure:
{
  "start_time": <float_seconds>,
  "end_time": <float_seconds>,
  "title": "<string>",
  "summary": "<string>",
  "reason": "<string>"
}

Example JSON output:
[
  {
    "start_time": 75.32,
    "end_time": 105.80,
    "title": "Unboxing Gone WRONG!",
    "summary": "The host attempts to unbox a new gadget, but it comically backfires, leading to unexpected chaos.",
    "reason": "Funny, surprising, and relatable moment with a clear narrative."
  }
]
"""
        # The user prompt will be the full SRT content.
        # The LLM is expected to parse times from the SRT format.
        user_prompt = f"Analyze the following SRT transcript and extract highlights as JSON:\n\nSRT Transcript:\n{srt_full_content}"

        self.logger("Sending SRT content to LLM for highlight detection...")
        
        llm_response = call_dashscope_qwen(
            api_key=self.qwen_api_key,
            model_name=self.qwen_model,
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            is_json_response_expected=True
        )

        if llm_response is None:
            self.logger("LLM call failed or returned None. No highlights generated.")
            return []

        if not isinstance(llm_response, list):
            self.logger(f"LLM response was not a list as expected. Got type: {type(llm_response)}. Response: {str(llm_response)[:500]}")
            # Attempt to parse if it's a string that contains a list (e.g. ```json ... ```)
            if isinstance(llm_response, str):
                try:
                    # Regex to find JSON array within potentially messy string output
                    match = re.search(r'\[(.*?)\]\s*', llm_response, re.DOTALL | re.MULTILINE)
                    if match:
                        json_str = match.group(0)
                        llm_response = json.loads(json_str)
                        if not isinstance(llm_response, list): # double check after parsing
                             self.logger(f"Parsed string to JSON, but it's still not a list. Type: {type(llm_response)}")
                             return []
                    else: # No JSON array found in string
                        self.logger(f"LLM string response did not contain a parsable JSON array.")
                        return []
                except json.JSONDecodeError as e:
                    self.logger(f"Failed to decode JSON from LLM string response: {e}. Response: {llm_response[:500]}")
                    return []
            else: # Not a list and not a string, cannot proceed
                return []

        parsed_highlights: List[ClipHighlight] = []
        for item in llm_response:
            if not isinstance(item, dict):
                self.logger(f"Warning: LLM response item is not a dictionary: {item}. Skipping.")
                continue

            try:
                # Validate required keys and types
                start_time = float(item['start_time'])
                end_time = float(item['end_time'])
                title = str(item['title'])
                summary = str(item['summary'])
                reason = str(item['reason'])

                if end_time <= start_time:
                    self.logger(f"Warning: Invalid times (end <= start) in LLM item: {item}. Skipping.")
                    continue
                
                # Optional: Add min/max duration checks here if desired, though prompt requests it.
                # min_clip_duration = self.config.get('ai', {}).get('min_clip_duration', 15)
                # max_clip_duration = self.config.get('ai', {}).get('max_clip_duration', 90)
                # if not (min_clip_duration <= (end_time - start_time) <= max_clip_duration):
                #     self.logger(f"Warning: Clip duration out of bounds for {title}: {(end_time - start_time):.2f}s. Skipping.")
                #     continue

                parsed_highlights.append(
                    ClipHighlight(
                        start_time=start_time,
                        end_time=end_time,
                        title=title,
                        summary=summary,
                        reason=reason,
                    )
                )
            except KeyError as e:
                self.logger(f"Warning: Missing key {e} in LLM response item: {item}. Skipping.")
            except ValueError as e:
                self.logger(f"Warning: Type conversion error for LLM response item ({e}): {item}. Skipping.")
            except Exception as e:
                self.logger(f"Warning: Unexpected error parsing LLM response item ({e}): {item}. Skipping.")

        self.logger(f"LLM identified {len(parsed_highlights)} valid highlights.")
        # Sort by start time just in case LLM doesn't guarantee order
        parsed_highlights.sort(key=lambda x: x.start_time)
        
        return parsed_highlights

    # All other methods like transcribe_audio, analyze_content, _normalize_sentiment_score,
    # _calculate_engagement_score, _extract_keywords, detect_highlights are removed. 