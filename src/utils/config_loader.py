import yaml
from pathlib import Path
from typing import Dict

class ConfigLoader:
    """Loads configuration files"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
    
    def load_settings(self) -> Dict:
        """Load main application settings"""
        return self._load_yaml("settings.yaml")
    
    def load_prompts(self) -> Dict:
        """Load LLM prompt templates"""
        return self._load_yaml("prompts.yaml")
    
    def load_platform_configs(self) -> Dict:
        """Load platform-specific configurations"""
        return self._load_yaml("platforms.yaml")
    
    def _load_yaml(self, filename: str) -> Dict:
        """Helper to load a YAML file"""
        file_path = self.config_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        try:
            with open(file_path, 'r') as f:
                return yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML file {filename}: {e}")

# Example usage:
if __name__ == "__main__":
    loader = ConfigLoader()
    
    settings = loader.load_settings()
    print("Settings:", settings)
    
    prompts = loader.load_prompts()
    print("\nPrompts:", prompts)
    
    platforms = loader.load_platform_configs()
    print("\nPlatforms:", platforms) 