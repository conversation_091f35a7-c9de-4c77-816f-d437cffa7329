import argparse
from pathlib import Path
from tqdm import tqdm
import json
import os
from typing import Optional, List
import concurrent.futures # Added for ThreadPoolExecutor

from src.core.video_processor import VideoProcessor, VideoInfo
from src.core.ai_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.core.clip_generator import <PERSON><PERSON><PERSON>enerator, ClipSpec
from src.core.title_generator import TitleGenerator
from src.utils.config_loader import Config<PERSON>oader
from src.utils.logger import setup_logger

class Application:
    """Main application orchestrator"""
    
    def __init__(self):
        # Load configuration
        config_loader = ConfigLoader()
        self.config = config_loader.load_settings()
        self.temp_files_to_clean = [] # Initialize list for temp files
        
        # Setup logger
        app_config = self.config.get('app', {})
        is_debug_mode = app_config.get('debug', False)  # Get the boolean debug flag
        # Determine log_level string: "DEBUG" if is_debug_mode is True, else use configured log_level or default to "INFO"
        log_level_str = "DEBUG" if is_debug_mode else app_config.get('log_level', "INFO")
        
        self.logger = setup_logger(
            log_level=log_level_str,
            log_file=app_config.get('log_file', 'logs/app.log')
        )
        
        # Initialize components
        self.video_processor = VideoProcessor(self.config, self.temp_files_to_clean)
        self.ai_analyzer = AIAnalyzer(self.config)
        self.clip_generator = ClipGenerator(self.config)
        self.title_generator = TitleGenerator(self.config)
        
        self.logger.info("Application initialized.")
    
    def run(self, video_url: str, output_dir: str = None):
        """
        Run the full clip generation pipeline using LLM for highlight detection.
        
        Args:
            video_url: URL of the YouTube video
            output_dir: Directory to save generated clips
        """
        self.logger.info(f"Processing video from URL: {video_url}")
        
        # Use a ThreadPoolExecutor to offload blocking tasks
        # max_workers can be configured, e.g., self.config.get('app', {}).get('max_workers', 1)
        # For a sequential pipeline per video, 1 worker for the main tasks is fine unless parts can be parallelized.
        # Here, we are offloading to prevent blocking a hypothetical calling thread (e.g. Streamlit UI event loop).
        max_pipeline_workers = self.config.get('app', {}).get('pipeline_max_workers', 1)

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_pipeline_workers) as executor:
                # Stage 1: Download and Normalize Video
                self.logger.info("Submitting video download and normalization task...")
                future_video_processing = executor.submit(
                    self._download_and_normalize_video_task, video_url
                )
                video_info, normalized_video_path, srt_file_path = future_video_processing.result()
                
                if not video_info or not normalized_video_path or not srt_file_path:
                    self.logger.error("Video processing failed. Aborting pipeline.")
                    return

                self.logger.info(f"Video downloaded and normalized: {normalized_video_path}, SRT: {srt_file_path}")

                # Stage 2: Get Highlights from SRT using LLM
                self.logger.info("Submitting AI analysis task...")
                future_analysis = executor.submit(
                    self.ai_analyzer.get_highlights_from_srt_llm, srt_file_path
                )
                highlights = future_analysis.result() # List[ClipHighlight]

                if not highlights:
                    self.logger.warning("No suitable highlights found by LLM.")
                    # No need to clean self.temp_files_to_clean yet, finally block will handle it.
                    return
                
                self.logger.info(f"LLM identified {len(highlights)} highlights.")
                for idx, hl in enumerate(highlights):
                    self.logger.info(f"  Highlight {idx+1}: {hl.title} ({hl.start_time:.2f}s - {hl.end_time:.2f}s) - Reason: {hl.reason}")

                # Stage 3: Generate Clips (Batch)
                self.logger.info("Submitting clip generation task...")
                future_clips = executor.submit(
                    self.clip_generator.generate_batch, 
                    normalized_video_path, 
                    highlights, 
                    subtitle_path=srt_file_path
                )
                generated_files_data = future_clips.result() # List of dicts with clip and thumbnail paths

            # Processing of generated_files_data (logging, metadata) happens outside the executor block
            self.logger.info(f"Generated {len(generated_files_data)} clip(s)/file sets:")
            for file_set in generated_files_data:
                self.logger.info(f"  Clip: {file_set.get('clip_path')}, Thumbnail: {file_set.get('thumbnail_path')}")
            
            # Persist Clip Metadata
            if generated_files_data:
                clip_metadata_list = []
                default_platform_name = self.config.get('clip_generator_default_platform', 'youtube_shorts')

                for file_set in generated_files_data:
                    clip_file_path = file_set.get('clip_path')
                    thumbnail_file_path = file_set.get('thumbnail_path')
                    highlight_obj = file_set.get('highlight_info') 

                    if not clip_file_path or not highlight_obj: 
                        self.logger.warning(f"Skipping metadata for a failed clip generation attempt. Highlight: {highlight_obj.title if highlight_obj else 'N/A'}")
                        continue
                    
                    metadata = {
                        'clip_file_path': clip_file_path,
                        'thumbnail_file_path': thumbnail_file_path, 
                        'title': highlight_obj.title,
                        'start_time_seconds': highlight_obj.start_time,
                        'end_time_seconds': highlight_obj.end_time,
                        'duration_seconds': highlight_obj.end_time - highlight_obj.start_time,
                        'summary': highlight_obj.summary,
                        'reason_for_clip': highlight_obj.reason,
                        'platform_generated_for': default_platform_name, 
                        'platform_specs': self.config.get('platforms', {}).get(default_platform_name, {})
                    }
                    clip_metadata_list.append(metadata)

                if clip_metadata_list:
                    if generated_files_data and generated_files_data[0].get('clip_path'):
                        clips_output_directory = Path(generated_files_data[0]['clip_path']).parent
                    else: 
                        clips_output_directory = self.clip_generator.output_dir 
                    
                    clips_output_directory.mkdir(parents=True, exist_ok=True) 
                    metadata_file_path = clips_output_directory / "clips_metadata.json"
                    
                    try:
                        with open(metadata_file_path, 'w', encoding='utf-8') as f:
                            json.dump(clip_metadata_list, f, indent=4, ensure_ascii=False)
                        self.logger.info(f"Successfully saved clip metadata to: {metadata_file_path}")
                    except Exception as e:
                        self.logger.error(f"Failed to save clip metadata: {e}", exc_info=True)
            
            self.logger.info("Processing complete with LLM-based highlight detection.")
            
        except Exception as e:
            self.logger.error(f"An error occurred in the main pipeline execution: {e}", exc_info=True)
            self.logger.critical("Pipeline execution failed.")
        finally:
            self.logger.info("Cleaning up temporary files...")
            if hasattr(self, 'temp_files_to_clean') and self.temp_files_to_clean:
                self._cleanup_temp_files(self.temp_files_to_clean)
                self.logger.info(f"Cleaned {len(self.temp_files_to_clean)} temporary files/directories.")
            else:
                self.logger.info("No temporary files marked for cleanup by the application.")

    def _download_and_normalize_video_task(self, video_url: str):
        """Task for downloading and normalizing video, returns (video_info, normalized_video_path, srt_file_path)."""
        try:
            self.logger.info("Downloading video and subtitles...")
            video_info = self.video_processor.download_video(video_url)
            self.logger.info(f"Video downloaded: {video_info.title}")

            base_name = Path(video_info.file_path).stem
            srt_file_path_obj = next(Path(self.video_processor.temp_dir).glob(f"{base_name}*.srt"), None)

            if not srt_file_path_obj or not srt_file_path_obj.exists():
                self.logger.error(f"SRT subtitle file not found for {video_info.title} after download. Path expected: {self.video_processor.temp_dir / base_name}.srt")
                return None, None, None # Indicate failure
            srt_file_path = str(srt_file_path_obj)
            self.logger.info(f"Using SRT file: {srt_file_path}")

            normalized_video_path = self.video_processor.normalize_video(video_info)
            self.logger.info(f"Video normalized: {normalized_video_path}")
            return video_info, normalized_video_path, srt_file_path
        except Exception as e:
            self.logger.error(f"Error in video download/normalization task: {e}", exc_info=True)
            return None, None, None # Indicate failure

    def _cleanup_temp_files(self, paths: List[str]):
        """Removes the files or directories in the provided list."""
        for path_str in paths:
            try:
                path_obj = Path(path_str)
                if path_obj.exists():
                    if path_obj.is_file():
                        path_obj.unlink()
                        self.logger.debug(f"Successfully removed temporary file: {path_str}")
                    elif path_obj.is_dir():
                        # shutil.rmtree(path_obj) # Be careful with recursive directory removal
                        # For now, only removing files listed. If a dir is listed, log it.
                        # If recursive deletion of listed temp dirs is needed, use shutil.rmtree.
                        # Let's assume for now paths are files, or we handle dirs differently if added.
                        # If we want to clean a whole temp dir, it should be added as a single path.
                        self.logger.debug(f"Path {path_str} is a directory. Manual cleanup or shutil.rmtree might be needed if it was meant to be deleted recursively.")
                else:
                    self.logger.debug(f"Temporary file/dir not found (already cleaned?): {path_str}")
            except Exception as e:
                self.logger.warning(f"Error cleaning up temporary path {path_str}: {e}", exc_info=True)

def main():
    parser = argparse.ArgumentParser(description="YouTube Clip Generator")
    parser.add_argument("video_url", help="URL of the YouTube video")
    parser.add_argument("--output_dir", "-o", help="Output directory for clips", default=None)
    
    args = parser.parse_args()
    
    app = Application()
    app.run(args.video_url, args.output_dir)

if __name__ == "__main__":
    main() 